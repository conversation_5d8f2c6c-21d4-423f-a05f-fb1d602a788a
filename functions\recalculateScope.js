let TOTAL_EMISSION = 0

const recalculateScope = (transaction, factor, NaceCode) => {
    try {
        const { Scope, Amount, _id, Status, Scope_1, Scope_2, Scope_3 } = transaction
        let newEmission = 0
        let updatedData = { supplierFactor: factor, NaceCode }

        if (Status === 2) {
            newEmission = (factor * Amount || 0) / 1000000
            
            const scopeMap = {
                1: { Scope_1: newEmission },
                2: { Scope_2: newEmission },
                3: { Scope_3: newEmission }
            }
            
            updatedData = { 
                ...updatedData,
                ...scopeMap[Scope]
            }
            
            // Calculate total emission based on scope
            const existingScopes = {
                1: [Scope_2, Scope_3],
                2: [Scope_1, Scope_3],
                3: [Scope_1, Scope_2]
            }
            TOTAL_EMISSION += newEmission + existingScopes[Scope].reduce((a, b) => a + b, 0)
        } else {
            TOTAL_EMISSION += Scope_1 + Scope_2 + Scope_3
        }

        return {
            data: { _id: _id.toString(), emission: newEmission, type: Scope },
            updateOperation: {
                updateOne: {
                    filter: { _id },
                    update: { $set: updatedData }
                }
            }
        }
    } catch (error) {
        console.log(error)
        return null
    }
}

exports = async function (input) {
    const { supplierId, factor, RegistrationNumber, NaceCode, year, isSupplierFactorChanged, isSupplierNameChanged } = input

    const request = context.services
        .get("mongodb-atlas")
        .db(context.environment.values.database)
        .collection("transaction")

    const transactions = await request
        .find({
            SupplierID: supplierId,
            RegistrationNumber,
            PeriodYear: year
        }, {
            Scope: 1,
            Amount: 1,
            _id: 1,
            Status: 1,
            Scope_1: 1,
            Scope_2: 1,
            Scope_3: 1
        })
        .toArray()

    const updatedTransactions = []
    const updateOperations = []

    // Process all transactions in a single pass
    transactions.forEach(doc => {
        if (isSupplierFactorChanged) {
            const result = recalculateScope(doc, factor, NaceCode)
            if (result) {
                updateOperations.push(result.updateOperation)
                updatedTransactions.push(result.data)
            }
        } else if (isSupplierNameChanged) {
            const { Scope_1, Scope_2, Scope_3, _id, Scope } = doc
            const emission = Scope_1 + Scope_2 + Scope_3
            updatedTransactions.push({ _id: _id.toString(), emission, type: Scope })
        }
    })

    // Process in chunks of 50
    if (updateOperations.length > 0) {
        const chunks = updateOperations.reduce((acc, curr, i) => {
            const chunkIndex = Math.floor(i / 50)
            acc[chunkIndex] = acc[chunkIndex] || []
            acc[chunkIndex].push(curr)
            return acc
        }, [])

        await Promise.all(chunks.map(chunk => request.bulkWrite(chunk)))
    }

    return { updatedTransactions, TOTAL_EMISSION }
}
