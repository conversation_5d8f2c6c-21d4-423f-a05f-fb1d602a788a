exports = async (args) => {
	const { RegistrationNumber, year } = args

	try {
		const company = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("company")
			.findOne({ RegistrationNumber: RegistrationNumber })
		let payload = { success: true, data: null }

		const Emissions = company.Emissions
		if (Emissions === undefined) {
			payload = { success: false, data: null }
		} else {
			let newEmission = Emissions[year]
			if (newEmission) {
				payload = { success: true, data: newEmission }
			} else {
				payload = { success: false, data: null }
			}
		}
		return payload
	} catch (error) {
		console.log(error)
		return { success: false }
	}
}
