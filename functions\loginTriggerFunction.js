exports = async function (authEvent) {
	/*
    An Authentication Trigger will always call a function with an authEvent.
    Documentation on Triggers: https://docs.mongodb.com/realm/triggers/overview/

    Access the user associated with the authEvent:
    const user = authEvent.user

    Access the time the authEvent happened:
    const time = authEvent.time

    Access the operation type for the authEvent:
    const operationType = authEvent.operationType

    Access the providers associated with the authEvent:
    const providers = authEvent.providers

    Functions run by Trigger<PERSON> are run as System users and have full access to Services, Functions, and MongoDB Data.

    Access a mongodb service:
    const collection = context.services.get("<SERVICE_NAME>").db("<DB_NAME>").collection("<COLL_NAME>");
    const doc = collection.findOne({ name: "mongodb" });

    Call other named functions if they are defined in your application:
    const result = context.functions.execute("function_name", arg1, arg2);

    Access the default http client and execute a GET request:
    const response = context.http.get({ url: <URL> })

    Learn more about http client here: https://docs.mongodb.com/realm/functions/context/#context-http
  */
	const user = authEvent.user

	const mongodb = context.services.get("mongodb-atlas")
	const users = mongodb.db(context.environment.values.database).collection("user")
	const userData = await users.findOne({ netlifyID: user.data.netlifyID })

	if (!userData) {
		const newUser = {
			netlifyID: user.data.netlifyID,
			email: user.data.email,
			role: user.data.role || "",
			app_metadata: {
				provider: user.data.provider || "",
			},
			user_metadata: {
				avatarurl: "",
				full_name: "",
			},
			// "name": user.data.name || '',
			"saf-t_files": [],
			Subscription: 0,
		}

		users.updateOne({ netlifyID: newUser.netlifyID }, newUser, { upsert: true })
	}
}
