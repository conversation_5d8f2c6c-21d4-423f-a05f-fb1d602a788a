// Electric Van, Electric Rigid Truck, Electric Articulated Truck
export const CATEGORY4_ELECTRIC_TRANSPORTATION_VALUE = 0.38

export const CATEGORY4_TRANSPORTATION_API_DATA = [
	{
		type: "freight",
		distance: 1000,
		unit: "km",
		medium: "land",
		class: "bike",
		weight: 1000,
		weightUnit: "kg",
	},
	{
		type: "freight",
		distance: 1000,
		unit: "km",
		medium: "land",
		class: "van",
		weight: 1000,
		weightUnit: "kg",
	},
	{
		type: "freight",
		distance: 1000,
		unit: "km",
		medium: "land",
		class: "rigid",
		weight: 1000,
		weightUnit: "kg",
	},
	{
		type: "freight",
		distance: 1000,
		unit: "km",
		medium: "land",
		class: "articulated",
		weight: 1000,
		weightUnit: "kg",
	},
	{
		type: "freight",
		distance: 1000,
		unit: "km",
		medium: "water",
		class: "crude tanker",
		weight: 1000,
		weightUnit: "kg",
	},
	{
		type: "freight",
		distance: 1000,
		unit: "km",
		medium: "water",
		class: "product tanker",
		weight: 1000,
		weightUnit: "kg",
	},
	{
		type: "freight",
		distance: 1000,
		unit: "km",
		medium: "water",
		class: "chemical tanker",
		weight: 1000,
		weightUnit: "kg",
	},
	{
		type: "freight",
		distance: 1000,
		unit: "km",
		medium: "water",
		class: "LNG tanker",
		weight: 1000,
		weightUnit: "kg",
	},
	{
		type: "freight",
		distance: 1000,
		unit: "km",
		medium: "water",
		class: "LPG tanker",
		weight: 1000,
		weightUnit: "kg",
	},
	{
		type: "freight",
		distance: 1000,
		unit: "km",
		medium: "water",
		class: "bulk carrier",
		weight: 1000,
		weightUnit: "kg",
	},
	{
		type: "freight",
		distance: 1000,
		unit: "km",
		medium: "water",
		class: "general cargo",
		weight: 1000,
		weightUnit: "kg",
	},
	{
		type: "freight",
		distance: 1000,
		unit: "km",
		medium: "water",
		class: "container ship",
		weight: 1000,
		weightUnit: "kg",
	},
	{
		type: "freight",
		distance: 1000,
		unit: "km",
		medium: "water",
		class: "vehicle transport",
		weight: 1000,
		weightUnit: "kg",
	},
	{
		type: "freight",
		distance: 1000,
		unit: "km",
		medium: "water",
		class: "RoRo-Ferry",
		weight: 1000,
		weightUnit: "kg",
	},
	{
		type: "freight",
		distance: 1000,
		unit: "km",
		medium: "water",
		class: "large RoPax ferry",
		weight: 1000,
		weightUnit: "kg",
	},
	{ type: "freight", distance: 1000, unit: "km", medium: "air", weight: 1000, weightUnit: "kg" },
]

export const CATEGORY4_DATA = [
	{
		co2e: 622.64,
		value: "Van < 3.5 tons",
		label: "Van < 3.5 tons",
		beis_id: "27_303_3106_14_1",
	},
	{
		co2e: 505.46,
		value: "Rigid 3.5 - 7.5 tons",
		label: "Rigid 3.5 - 7.5 tons",
		beis_id: "27_304_3112_14_1",
	},
	{
		co2e: 380.23,
		value: "Rigid 7.5 - 17 tons",
		label: "Rigid 7.5 - 17 tons",
		beis_id: "27_304_3116_14_1",
	},
	{
		co2e: 153.98,
		value: "Rigid > 17 tons",
		label: "Rigid > 17 tons",
		beis_id: "27_304_3120_14_1",
	},
	{
		co2e: 75.47,
		value: "semi-trailer truck",
		label: "semi-trailer truck",
		beis_id: "27_304_3136_14_1",
	},
	{
		co2e: 27.79,
		value: "Freight train",
		label: "Freight train",
		beis_id: "27_315_3151_14_1",
	},
	{
		co2e: 4.56,
		value: "Crude tanker",
		label: "Crude tanker",
		beis_id: "27_319_3197_14_1",
	},
	{
		co2e: 9.02,
		value: "Products tanker",
		label: "Products tanker",
		beis_id: "27_319_3203_14_1",
	},
	{
		co2e: 10.3,
		value: "Chemical tanker",
		label: "Chemical tanker",
		beis_id: "27_319_3208_14_1",
	},
	{
		co2e: 11.53,
		value: "LNG tanker",
		label: "LNG tanker",
		beis_id: "27_319_3211_14_1",
	},
	{
		co2e: 10.36,
		value: "LPG Tanker",
		label: "LPG Tanker",
		beis_id: "27_319_3214_14_1",
	},
	{
		co2e: 3.53,
		value: "Bulk carrier",
		label: "Bulk carrier",
		beis_id: "27_320_3221_14_1",
	},
	{
		co2e: 13.21,
		value: "General cargo ship",
		label: "General cargo ship",
		beis_id: "27_320_3228_14_1",
	},
	{
		co2e: 16.12,
		value: "Container ship",
		label: "Container ship",
		beis_id: "27_320_3231_14_1",
	},
	{
		co2e: 38.52,
		value: "Vehicle transport",
		label: "Vehicle transport",
		beis_id: "27_320_3238_14_1",
	},
	{
		co2e: 51.59,
		value: "RoRo-Ferry",
		label: "RoRo-Ferry",
		beis_id: "27_320_3241_14_1",
	},
]

export const CATEGORY4_TRANSPORT_TYPE_DATA = [
	{ value: "bike", unit: "kms", medium: "land", label: "Bike", weight: 1000, weightUnit: "kgs" },
	{ value: "van", unit: "kms", medium: "land", label: "Van", weight: 1000, weightUnit: "kgs" },
	{
		value: "e_van",
		unit: "kms",
		medium: "land",
		label: "Electric Van",
		weight: 1000,
		weightUnit: "kgs",
	},
	{ value: "rigid", unit: "kms", medium: "land", label: "Rigid", weight: 1, weightUnit: "kgs" },
	{
		value: "e_rigid",
		unit: "kms",
		medium: "land",
		label: "Electric Rigid Truck",
		weight: 100,
		weightUnit: "kgs",
	},
	{
		value: "articulated",
		unit: "kms",
		medium: "land",
		label: "Articulated",
		weight: 100,
		weightUnit: "kgs",
	},
	{
		value: "e_articulated",
		unit: "kms",
		medium: "land",
		label: "Electric Articulated Truck",
		weight: 100,
		weightUnit: "kgs",
	},
	{
		value: "crude tanker",
		unit: "kms",
		medium: "water",
		label: "Crude tanker",
		weight: 100,
		weightUnit: "kgs",
	},
	{
		value: "product tanker",
		unit: "kms",
		medium: "water",
		label: "Product tanker",
		weight: 100,
		weightUnit: "kgs",
	},
	{
		value: "chemical tanker",
		unit: "kms",
		medium: "water",
		label: "Chemical tanker",
		weight: 100,
		weightUnit: "kgs",
	},
	{
		value: "LNG tanker",
		unit: "kms",
		medium: "water",
		label: "LNG tanker",
		weight: 100,
		weightUnit: "kgs",
	},
	{
		value: "LPG tanker",
		unit: "kms",
		medium: "water",
		label: "LPG tanker",
		weight: 100,
		weightUnit: "kgs",
	},
	{
		value: "bulk carrier",
		unit: "kms",
		medium: "water",
		label: "Bulk carrier",
		weight: 100,
		weightUnit: "kgs",
	},
	{
		value: "general cargo",
		unit: "kms",
		medium: "water",
		label: "General cargo",
		weight: 100,
		weightUnit: "kgs",
	},
	{
		value: "container ship",
		unit: "kms",
		medium: "water",
		label: "Container ship",
		weight: 100,
		weightUnit: "kgs",
	},
	{
		value: "vehicle transport",
		unit: "kms",
		medium: "water",
		label: "Vehicle transport",
		weight: 100,
		weightUnit: "kgs",
	},
	{
		value: "RoRo-Ferry",
		unit: "kms",
		medium: "water",
		label: "RoRo-Ferry",
		weight: 100,
		weightUnit: "kgs",
	},
	{
		value: "Large RoPax ferry",
		unit: "kms",
		medium: "water",
		label: "Large RoPax ferry",
		weight: 1000,
		weightUnit: "kgs",
	},
	{ value: "Air", unit: "kms", medium: "air", label: "Air", weight: 100, weightUnit: "kgs" },
]
