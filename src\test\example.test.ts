import { describe, it, expect } from "vitest"

describe("TypeScript and Vitest Setup", () => {
	it("should work with TypeScript", () => {
		const message = "Hello, TypeScript!"
		expect(message).toBe("Hello, TypeScript!")
	})

	it("should support modern JavaScript features", () => {
		const numbers = [1, 2, 3, 4, 5]
		const doubled = numbers.map(n => n * 2)
		expect(doubled).toEqual([2, 4, 6, 8, 10])
	})

	it("should handle async operations", async () => {
		const promise = Promise.resolve("async test")
		const result = await promise
		expect(result).toBe("async test")
	})
})
