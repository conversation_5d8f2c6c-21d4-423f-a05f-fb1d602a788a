import React from "react"
import "../../styles/header.css"

export default function WorkingOnDataIndicator({ state = 1 }) {
	const divStyle = {
		height: 15,
		width: 15,
		borderRadius: "50%",
		marginRight: 3,
		backgroundColor: "black",
	}
	return (
		<div className="flex justify-center items-center flex-col">
			{state === 1 && (
				<>
					<div className="flex justify-center items-center ">
						<div style={{ ...divStyle, backgroundColor: "red" }}></div>
						<div style={{ ...divStyle }}></div>
						<div style={{ ...divStyle }}></div>
					</div>
					<div className="feedbackDiv" style={{ color: "red" }}>
						we are working on data
					</div>
				</>
			)}

			{state === 2 && (
				<>
					<div className="flex justify-center items-center ">
						<div style={{ ...divStyle }}></div>
						<div style={{ ...divStyle, backgroundColor: "orange" }}></div>
						<div style={{ ...divStyle }}></div>
					</div>
					<div className="feedbackDiv" style={{ color: "orange" }}>
						we are almost done
					</div>
				</>
			)}

			{state === 3 && (
				<>
					<div className="flex justify-center items-center ">
						<div style={{ ...divStyle }}></div>
						<div style={{ ...divStyle }}></div>
						<div style={{ ...divStyle, backgroundColor: "green" }}></div>
					</div>
					<div className="feedbackDiv" style={{ color: "green" }}>
						Wait for second
					</div>
				</>
			)}
		</div>
	)
}
