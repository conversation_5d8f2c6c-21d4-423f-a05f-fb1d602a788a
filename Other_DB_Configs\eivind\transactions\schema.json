{"title": "transaction", "properties": {"IsSplittedTransaction": {"bsonType": "bool"}, "TransactionID": {"bsonType": "string"}, "_id": {"bsonType": "objectId"}, "AccountDescription": {"bsonType": "string"}, "AccountID": {"bsonType": "string"}, "AccountIDInRange": {"bsonType": "bool"}, "Amount": {"bsonType": "number"}, "Description": {"bsonType": "array", "items": {"bsonType": "string"}}, "NaceCode": {"bsonType": "string"}, "Notes": {"bsonType": "string"}, "Period": {"bsonType": "string"}, "PeriodYear": {"bsonType": "string"}, "Production-related": {"bsonType": "bool"}, "ReferenceNumber": {"bsonType": "string"}, "RegistrationNumber": {"bsonType": "string"}, "Relation": {"bsonType": "string"}, "Scope": {"bsonType": "int"}, "Scope_1": {"bsonType": "double"}, "Scope_2": {"bsonType": "double"}, "Scope_3": {"bsonType": "double"}, "Scope_3_Category": {"bsonType": "int"}, "Status": {"bsonType": "int"}, "SupplierID": {"bsonType": "string"}, "SupplierName": {"bsonType": "string"}, "SystemID": {"bsonType": "string"}, "TransactionDate": {"bsonType": "string"}, "Type": {"bsonType": "string"}, "liter": {"bsonType": "double"}, "Auto": {"bsonType": "bool"}, "DescriptionDetails": {"bsonType": "string"}, "Flag": {"bsonType": "int"}, "Goods-type": {"bsonType": "string"}, "Lines": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"AccountID": {"bsonType": "string"}, "CreditAmount": {"bsonType": "object", "properties": {"Amount": {"bsonType": "string"}, "CurrencyAmount": {"bsonType": "string"}, "CurrencyCode": {"bsonType": "string"}, "ExchangeRate": {"bsonType": "string"}}}, "DebitAmount": {"bsonType": "object", "properties": {"Amount": {"bsonType": "string"}, "CurrencyAmount": {"bsonType": "string"}, "CurrencyCode": {"bsonType": "string"}, "ExchangeRate": {"bsonType": "string"}}}, "Description": {"bsonType": "string"}, "NaceCode": {"bsonType": "string"}, "RecordID": {"bsonType": "string"}, "ReferenceNumber": {"bsonType": "string"}, "Status": {"bsonType": "string"}, "SupplierID": {"bsonType": "string"}}}}, "Emissions": {"bsonType": "object", "additionalProperties": {"bsonType": "object", "title": "Scope1", "properties": {"Combustion": {"bsonType": "object", "additionalProperties": {"bsonType": "object", "title": "Combustion", "properties": {"consumption": {"bsonType": "string"}, "factor": {"bsonType": "double"}, "kgco2e": {"bsonType": "double"}, "scope3": {"bsonType": "double"}, "type": {"bsonType": "string"}, "units": {"bsonType": "string"}}}}, "Processes": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"factor": {"bsonType": "double"}, "kgco2e": {"bsonType": "double"}, "scope3": {"bsonType": "double"}, "type": {"bsonType": "string"}, "unit": {"bsonType": "string"}, "units": {"bsonType": "double"}}}}, "Refrigerants": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"factor": {"bsonType": "double"}, "gasType": {"bsonType": "string"}, "kgco2e": {"bsonType": "double"}, "scope3": {"bsonType": "double"}, "type": {"bsonType": "string"}, "unit": {"bsonType": "string"}, "weight": {"bsonType": "double"}}}}, "Vehicles": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"distance": {"bsonType": "double"}, "fuelEconomy": {"bsonType": "double"}, "fuelEconomyUnit": {"bsonType": "string"}, "fuelType": {"bsonType": "string"}, "kgco2e": {"bsonType": "double"}, "scope3": {"bsonType": "double"}, "type": {"bsonType": "string"}, "unit": {"bsonType": "string"}, "vehicleSize": {"bsonType": "string"}}}}}}, "Scope2": {"bsonType": "object", "properties": {"Electricity": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"consumption": {"bsonType": "double"}, "factor": {"bsonType": "double"}, "kgco2e": {"bsonType": "double"}, "scope3": {"bsonType": "double"}, "source": {"bsonType": "string"}, "type": {"bsonType": "string"}, "unit": {"bsonType": "string"}}}}, "Heat": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"consumption": {"bsonType": "double"}, "factor": {"bsonType": "double"}, "kgco2e": {"bsonType": "double"}, "scope3": {"bsonType": "double"}, "type": {"bsonType": "string"}, "unit": {"bsonType": "string"}}}}}}, "Scope3": {"bsonType": "object", "properties": {"Goods": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"consumption": {"bsonType": "double"}, "factor": {"bsonType": "double"}, "kgco2e": {"bsonType": "double"}, "source": {"bsonType": "string"}, "type": {"bsonType": "string"}, "unit": {"bsonType": "string"}}}}, "Waste": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"consumption": {"bsonType": "double"}, "factor": {"bsonType": "double"}, "kgco2e": {"bsonType": "double"}, "source": {"bsonType": "string"}, "type": {"bsonType": "string"}, "unit": {"bsonType": "string"}}}}}}}}}