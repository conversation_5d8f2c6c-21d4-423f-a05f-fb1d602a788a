{"properties": {"CompanyName": {"bsonType": "string"}, "RegistrationNumber": {"bsonType": "string"}, "Subscription": {"bsonType": "int", "default": {"$numberInt": "0"}}, "_id": {"bsonType": "objectId"}, "app_metadata": {"bsonType": "object", "properties": {"provider": {"bsonType": "string"}}}, "created_at": {"bsonType": "string"}, "email": {"bsonType": "string"}, "name": {"bsonType": "string"}, "netlifyID": {"bsonType": "string"}, "organizationId": {"bsonType": "string"}, "role": {"bsonType": "string"}, "saf-t_files": {"bsonType": "array", "items": {"bsonType": "string"}}, "user_metadata": {"bsonType": "object", "properties": {"avatarurl": {"bsonType": "string"}, "full_name": {"bsonType": "string"}}}}, "title": "user"}