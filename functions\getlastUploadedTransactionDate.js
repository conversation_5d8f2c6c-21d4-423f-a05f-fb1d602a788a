exports = async function ({ RegistrationNumber }) {
	try {
		const transactions = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("transaction")
			.find({ RegistrationNumber, ImportID: { $exists: 1 } })
			.toArray()

		for (const transaction of transactions) {
			transaction.date = new Date(transaction.TransactionDate)
		}

		transactions.sort((a, b) => b.date - a.date) // Sort in descending order

		return {
			success: true,
			date: transactions[0].TransactionDate,
		}
	} catch (err) {
		console.log("Error occurred while executing findOne:", err.message)

		return { error: err.message, success: false }
	}

	// To call other named functions:
	// var result = context.functions.execute("function_name", arg1, arg2);

	//return { result: findResult };
}
