exports = async function (arg) {
	const { RegistrationNumber, year, projectId, report } = arg

	try {
		let request = context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("projectReports")

		let reportFound = await request.findOne({ RegistrationNumber, year, projectId })

		if (reportFound) {
			await request.updateOne({ RegistrationNumber, year, projectId }, { $set: { report: { ...report } } })
		} else {
			await request.insertOne({ RegistrationNumber, year, projectId, report: { ...report } })
		}

		return { success: true }
	} catch (error) {
		console.log(error)
		return { success: false, message: error.message }
	}
}
