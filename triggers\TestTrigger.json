{"name": "TestTrigger", "type": "DATABASE", "disabled": false, "config": {"collection": "Batches", "database": "development", "full_document": false, "full_document_before_change": false, "match": {}, "maximum_throughput": false, "operation_types": ["INSERT"], "project": {}, "service_name": "mongodb-atlas", "skip_catchup_events": false, "tolerate_resume_errors": false, "unordered": false}, "event_processors": {"FUNCTION": {"config": {"function_name": "testTriggerForBatchUpdate"}}}}