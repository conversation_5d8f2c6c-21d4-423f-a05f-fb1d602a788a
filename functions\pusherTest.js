// functions/pusherTest.js
exports = async function(arg) {
  // 1. Import the built-in crypto module for generating the signature
  const crypto = require('crypto');

  // 2. Retrieve credentials securely from Realm Values
//   const appId = context.values.get("pusherAppId");
//   const key = context.values.get("pusherKey");
//   const secret = context.values.get("pusherSecret");
//   const cluster = context.values.get("pusherCluster"); // Used to construct the URL

const appId = "1981384"
const key = "bb52f79f5770bea80dc1"
const secret = "5452397579a625b63770"
const cluster = "eu"




  // Basic validation
  if (!appId || !key || !secret || !cluster) {
    console.error("Pusher credentials are not configured in Realm Values (pusherAppId, pusherKey, pusherSecret, pusherCluster).");
    return { success: false, error: "Pusher configuration missing." };
  }

  // 3. Define the event details
  const channelName = arg?.channel || 'private-test-channel'; // Can be a single channel or an array
  const eventName = arg?.event || 'rest-api-event';
  const dataToSend = arg?.data || { message: `Hello from Realm REST API! Triggered at ${new Date().toISOString()}` };
  const bodyPayload = JSON.stringify({
      name: eventName,
      channels: Array.isArray(channelName) ? channelName : [channelName], // API expects an array of channels
      data: JSON.stringify(dataToSend) // Data payload must be a string
  });

  // 4. Prepare for Authentication Signature
  const timestamp = Math.floor(Date.now() / 1000); // Pusher requires UNIX timestamp in seconds
  const method = 'POST';
  const path = `/apps/${appId}/events`;
  const queryParams = `auth_key=${key}&auth_timestamp=${timestamp}&auth_version=1.0&body_md5=${crypto.createHash('md5').update(bodyPayload).digest('hex')}`;

  // String to sign according to Pusher docs: METHOD\nPATH\nQUERY_PARAMS
  const stringToSign = `${method}\n${path}\n${queryParams}`;

  // 5. Generate the HMAC-SHA256 signature
  const authSignature = crypto.createHmac('sha256', secret)
                              .update(stringToSign)
                              .digest('hex');

  // 6. Construct the Pusher REST API URL and full query string
  const pusherHost = `api-${cluster}.pusher.com`; // Or api.pusher.com if no cluster specified
  const url = `https://${pusherHost}${path}?${queryParams}&auth_signature=${authSignature}`;

  console.log(`Attempting to trigger Pusher event via REST API: ${url}`);
  console.log(`Request Body: ${bodyPayload}`);

  try {
    // 7. Make the HTTP POST request using context.http
    const response = await context.http.post({
      url: url,
      body: bodyPayload,
      headers: {
        'Content-Type': ['application/json'] // Header values must be arrays
      }
      // encodeBodyAsJSON: true // Not needed as we manually stringify and set Content-Type
    });

    // Pusher REST API returns {} on success (status 200) or details on error (e.g., 4xx)
    // context.http throws an error for non-2xx responses by default.
    const responseBody = response.body.text(); // Read the response body text
    console.log(`Pusher API Response Status: ${response.statusCode}`);
    console.log(`Pusher API Response Body: ${responseBody}`); // Usually empty on success

    if (response.statusCode === 200) {
         return { success: true, message: "Pusher event triggered via REST API.", channel: channelName, event: eventName };
    } else {
        // This part might not be reached if context.http throws on non-2xx,
        // but included for completeness if that behavior changes or is configured differently.
        console.error(`Pusher API returned non-200 status: ${response.statusCode}`);
        return { success: false, error: "Pusher API request failed.", details: `Status ${response.statusCode}: ${responseBody}` };
    }

  } catch (error) {
    console.error("Error making HTTP request to Pusher API:", error);
    // Log the error details available from context.http exception
    let errorDetails = error.message;
    if (error.response) {
        errorDetails = `Status ${error.response.statusCode}: ${error.response.body?.text() || 'No body'}`;
        console.error("Pusher API Error Response Body:", error.response.body?.text());
    }
    return { success: false, error: "Failed to trigger Pusher event via REST API.", details: errorDetails };
  }
};