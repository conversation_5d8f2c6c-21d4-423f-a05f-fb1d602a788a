exports = async (args) => {
	const { RegistrationNumber, information } = args
	try {
		const company = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("company")
			.findOne({ RegistrationNumber: RegistrationNumber })
		let reports = company.reports
		let newReports = []
		if (reports === undefined) {
			newReports = [{ ...information }]
		} else {
			let oldRep = reports.filter((report) => {
				if (report.year == information.year) {
					return information
				}
			})

			if (oldRep.length > 0) {
				newReports = reports.map((report) => {
					if (report.year === information.year) {
						return information
					} else {
						return report
					}
				})
			} else {
				reports.push(information)
				newReports = reports
			}
		}

		const request = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("company")
			.updateOne({ RegistrationNumber: RegistrationNumber }, { $set: { reports: newReports } })
		if (request) {
			return { success: true }
		} else {
			return { success: false }
		}
	} catch (error) {
		console.log(error)
		return { success: false }
	}
}
