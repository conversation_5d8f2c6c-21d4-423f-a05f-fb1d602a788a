exports = async function (arg) {
	const { RegistrationNumber, year } = arg

	try {
		let request = context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("transaction")

		let data = await request.aggregate([
			{
				$match: { RegistrationNumber, PeriodYear: year },
			},
			{
				$unwind: {
					path: "$Analysis",
					preserveNullAndEmptyArrays: true,
				},
			},
			{
				$group: {
					_id: "$Analysis.AnalysisID",
					totalEmission: {
						$sum: {
							$add: ["$Scope_1", "$Scope_2", "$Scope_3"],
						},
					},
				},
			},
            { $sort: { totalEmission: -1 } },
		])

		return data
	} catch (error) {
		console.log(error)
		return []
	}
}
