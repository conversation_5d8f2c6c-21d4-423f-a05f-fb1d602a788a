/**
 * React Query Integration with Advanced Cache Manager
 * Provides seamless integration between React Query and our custom cache manager
 */

import { QueryClient, QueryKey, QueryFunction } from "@tanstack/react-query"
import { cacheManager, CacheConfig, CACHE_TAGS, CacheTag } from "./cache-manager"

export interface QueryCacheConfig extends Partial<CacheConfig> {
	deduplicationKey?: string
	warmOnMount?: boolean
	invalidateOnTags?: CacheTag[]
}

/**
 * Enhanced query function with deduplication and caching
 */
export function createCachedQueryFn<T>(
	originalQueryFn: QueryFunction<T>,
	config?: QueryCacheConfig
) {
	return async (context: any): Promise<T> => {
		const queryKey = Array.isArray(context.queryKey)
			? context.queryKey.join(":")
			: String(context.queryKey)

		const deduplicationKey = config?.deduplicationKey || queryKey

		// Use cache manager for deduplication and caching
		const result = await cacheManager.get<T>(deduplicationKey, () => originalQueryFn(context), {
			ttl: config?.ttl || 5 * 60 * 1000, // 5 minutes default
			tags: config?.tags || [],
			staleWhileRevalidate: config?.staleWhileRevalidate ?? true,
			priority: config?.priority || "medium",
		})

		return result!
	}
}

/**
 * Data relationship mapping for intelligent cache invalidation
 */
export const DATA_RELATIONSHIPS = {
	// When transactions change, invalidate related data
	[CACHE_TAGS.TRANSACTIONS]: [CACHE_TAGS.DASHBOARD, CACHE_TAGS.ANALYSIS, CACHE_TAGS.SUPPLIERS],

	// When suppliers change, invalidate related data
	[CACHE_TAGS.SUPPLIERS]: [CACHE_TAGS.TRANSACTIONS, CACHE_TAGS.DASHBOARD, CACHE_TAGS.ANALYSIS],

	// When organizations change, invalidate most data
	[CACHE_TAGS.ORGANIZATIONS]: [
		CACHE_TAGS.TRANSACTIONS,
		CACHE_TAGS.SUPPLIERS,
		CACHE_TAGS.DASHBOARD,
		CACHE_TAGS.ANALYSIS,
		CACHE_TAGS.SETTINGS,
	],

	// When user changes, invalidate user-specific data
	[CACHE_TAGS.USER]: [CACHE_TAGS.ORGANIZATIONS, CACHE_TAGS.SETTINGS],
} as const

/**
 * Enhanced Query Client with intelligent cache invalidation
 */
export class EnhancedQueryClient extends QueryClient {
	/**
	 * Invalidate queries with intelligent relationship-based invalidation
	 */
	invalidateQueriesWithRelationships(tags: CacheTag[]): Promise<void> {
		// Get all related tags
		const allTagsToInvalidate = new Set(tags)

		tags.forEach(tag => {
			const relatedTags = DATA_RELATIONSHIPS[tag] || []
			relatedTags.forEach(relatedTag => allTagsToInvalidate.add(relatedTag))
		})

		// Invalidate in cache manager
		cacheManager.invalidateByTags(Array.from(allTagsToInvalidate))

		// Invalidate in React Query
		const tagsArray = Array.from(allTagsToInvalidate)
		return this.invalidateQueries({
			predicate: query => {
				const queryTags = this.getQueryTags(query.queryKey)
				return queryTags.some(tag => tagsArray.includes(tag))
			},
		})
	}

	/**
	 * Warm critical queries on application start
	 */
	async warmCriticalQueries(app: any, currentOrganization: any): Promise<void> {
		if (!currentOrganization?.RegistrationNumber) return

		const currentYear = new Date().getFullYear()
		const registrationNumber = currentOrganization.RegistrationNumber

		const warmupStrategies = [
			// Warm dashboard data
			{
				key: `dashboard:${registrationNumber}:${currentYear}`,
				fetcher: () =>
					app.getYearlyChartData({
						RegistrationNumber: registrationNumber,
						year: currentYear,
					}),
				config: {
					tags: [CACHE_TAGS.DASHBOARD],
					priority: "high" as const,
					ttl: 10 * 60 * 1000, // 10 minutes for dashboard data
				},
			},

			// Warm suppliers data
			{
				key: `suppliers:${registrationNumber}:${currentYear}`,
				fetcher: () =>
					app.getSuppliers({
						RegistrationNumber: registrationNumber,
						year: currentYear,
					}),
				config: {
					tags: [CACHE_TAGS.SUPPLIERS],
					priority: "high" as const,
					ttl: 15 * 60 * 1000, // 15 minutes for suppliers
				},
			},

			// Warm recent transactions
			{
				key: `transactions:recent:${registrationNumber}`,
				fetcher: () =>
					app.getTransactions({
						RegistrationNumber: registrationNumber,
						Scope: -1,
						year: currentYear,
						limit: 100,
					}),
				config: {
					tags: [CACHE_TAGS.TRANSACTIONS],
					priority: "medium" as const,
					ttl: 5 * 60 * 1000, // 5 minutes for transactions
				},
			},

			// Warm NACE data (rarely changes)
			{
				key: `nace:${registrationNumber}`,
				fetcher: () => app.getNace({ RegistrationNumber: registrationNumber }),
				config: {
					tags: [CACHE_TAGS.NACE],
					priority: "low" as const,
					ttl: 60 * 60 * 1000, // 1 hour for NACE data
				},
			},
		]

		await cacheManager.warmCache(warmupStrategies)
	}

	/**
	 * Get cache tags from query key
	 */
	private getQueryTags(queryKey: QueryKey): CacheTag[] {
		const keyString = Array.isArray(queryKey)
			? queryKey.join(":").toLowerCase()
			: String(queryKey).toLowerCase()
		const tags: CacheTag[] = []

		// Map query keys to cache tags
		if (keyString.includes("transaction")) tags.push(CACHE_TAGS.TRANSACTIONS)
		if (keyString.includes("supplier")) tags.push(CACHE_TAGS.SUPPLIERS)
		if (keyString.includes("organization")) tags.push(CACHE_TAGS.ORGANIZATIONS)
		if (
			keyString.includes("dashboard") ||
			keyString.includes("chart") ||
			keyString.includes("analysis")
		) {
			tags.push(CACHE_TAGS.DASHBOARD)
		}
		if (keyString.includes("nace")) tags.push(CACHE_TAGS.NACE)
		if (
			keyString.includes("setting") ||
			keyString.includes("lock") ||
			keyString.includes("publish")
		) {
			tags.push(CACHE_TAGS.SETTINGS)
		}
		if (keyString.includes("user")) tags.push(CACHE_TAGS.USER)

		return tags
	}
}

/**
 * Request deduplication utility for non-React Query requests
 */
class RequestDeduplicator {
	private pendingRequests = new Map<string, Promise<any>>()

	async deduplicate<T>(
		key: string,
		requestFn: () => Promise<T>,
		ttl: number = 30000 // 30 seconds default
	): Promise<T> {
		// Check if request is already pending
		if (this.pendingRequests.has(key)) {
			return this.pendingRequests.get(key)!
		}

		// Create new request
		const promise = requestFn()
		this.pendingRequests.set(key, promise)

		// Clean up after completion or timeout
		const cleanup = () => {
			this.pendingRequests.delete(key)
		}

		// Set timeout for cleanup
		const timeoutId = setTimeout(cleanup, ttl)

		try {
			const result = await promise
			clearTimeout(timeoutId)
			cleanup()
			return result
		} catch (error) {
			clearTimeout(timeoutId)
			cleanup()
			throw error
		}
	}

	/**
	 * Clear all pending requests
	 */
	clear(): void {
		this.pendingRequests.clear()
	}

	/**
	 * Get pending request count
	 */
	getPendingCount(): number {
		return this.pendingRequests.size
	}
}

// Global request deduplicator instance
export const requestDeduplicator = new RequestDeduplicator()

/**
 * Cache warming strategies for different data types
 */
export const CACHE_WARMING_STRATEGIES = {
	// Critical data that should be loaded immediately
	CRITICAL: ["dashboard", "user-profile", "current-organization"],

	// Important data that should be loaded soon after
	IMPORTANT: ["suppliers", "recent-transactions", "nace-data"],

	// Nice-to-have data that can be loaded in background
	BACKGROUND: ["historical-data", "analytics", "settings"],
} as const

/**
 * Cache size management configuration
 */
export const CACHE_SIZE_CONFIG = {
	MAX_ENTRIES: 200,
	EVICTION_THRESHOLD: 0.8, // Start evicting when 80% full
	EVICTION_PERCENTAGE: 0.25, // Remove 25% of entries when evicting
	CLEANUP_INTERVAL: 5 * 60 * 1000, // 5 minutes
	MAX_AGE_MULTIPLIER: 2, // Consider entries expired after 2x their TTL
} as const
