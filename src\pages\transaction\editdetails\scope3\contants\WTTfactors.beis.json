[{"_id": {"$oid": "67abcdbf273f786abebfa91a"}, "ID": "24_914_3188_11_1", "Scope": "Scope 3", "Level 1": "WTT- business travel- sea", "Level 2": "WTT- ferry", "Level 3": "Foot passenger", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.00424}, {"_id": {"$oid": "67abcdbf273f786abebfa91b"}, "ID": "24_914_3189_11_1", "Scope": "Scope 3", "Level 1": "WTT- business travel- sea", "Level 2": "WTT- ferry", "Level 3": "Car passenger", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.02932}, {"_id": {"$oid": "67abcdbf273f786abebfa91c"}, "ID": "24_914_3190_11_1", "Scope": "Scope 3", "Level 1": "WTT- business travel- sea", "Level 2": "WTT- ferry", "Level 3": "Average (all passenger)", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.02555}, {"_id": {"$oid": "67abcdbf273f786abebfa941"}, "ID": "26_904_3045_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Small car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Diesel", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.03409}, {"_id": {"$oid": "67abcdbf273f786abebfa942"}, "ID": "26_904_3046_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Small car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Petrol", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.04015}, {"_id": {"$oid": "67abcdbf273f786abebfa943"}, "ID": "26_904_3047_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Small car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Hybrid", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.03026}, {"_id": {"$oid": "67abcdbf273f786abebfa944"}, "ID": "26_904_3050_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Small car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Unknown", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.03836}, {"_id": {"$oid": "67abcdbf273f786abebfa945"}, "ID": "26_904_3051_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Small car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Plug-in Hybrid Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.0152}, {"_id": {"$oid": "67abcdbf273f786abebfa946"}, "ID": "26_904_3052_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Small car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Battery Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.00948}, {"_id": {"$oid": "67abcdbf273f786abebfa947"}, "ID": "26_904_3053_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Medium car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Diesel", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.04103}, {"_id": {"$oid": "67abcdbf273f786abebfa948"}, "ID": "26_904_3054_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Medium car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Petrol", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.04957}, {"_id": {"$oid": "67abcdbf273f786abebfa949"}, "ID": "26_904_3055_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Medium car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Hybrid", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.02998}, {"_id": {"$oid": "67abcdbf273f786abebfa94a"}, "ID": "26_904_3058_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Medium car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Unknown", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.04518}, {"_id": {"$oid": "67abcdbf273f786abebfa94b"}, "ID": "26_904_3059_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Medium car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Plug-in Hybrid Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.02524}, {"_id": {"$oid": "67abcdbf273f786abebfa94c"}, "ID": "26_904_3060_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Medium car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Battery Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.01022}, {"_id": {"$oid": "67abcdbf273f786abebfa94d"}, "ID": "26_904_3061_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Large car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Diesel", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.0507}, {"_id": {"$oid": "67abcdbf273f786abebfa94e"}, "ID": "26_904_3062_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Large car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Petrol", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.07528}, {"_id": {"$oid": "67abcdbf273f786abebfa94f"}, "ID": "26_904_3063_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Large car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Hybrid", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.03961}, {"_id": {"$oid": "67abcdbf273f786abebfa950"}, "ID": "26_904_3066_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Large car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Unknown", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.05764}, {"_id": {"$oid": "67abcdbf273f786abebfa951"}, "ID": "26_904_3067_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Large car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Plug-in Hybrid Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.03221}, {"_id": {"$oid": "67abcdbf273f786abebfa952"}, "ID": "26_904_3068_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Large car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Battery Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.01088}, {"_id": {"$oid": "67abcdbf273f786abebfa953"}, "ID": "26_904_3069_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Average car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Diesel", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.04146}, {"_id": {"$oid": "67abcdbf273f786abebfa954"}, "ID": "26_904_3070_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Average car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Petrol", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.04599}, {"_id": {"$oid": "67abcdbf273f786abebfa955"}, "ID": "26_904_3071_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Average car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Hybrid", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.03315}, {"_id": {"$oid": "67abcdbf273f786abebfa956"}, "ID": "26_904_3074_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Average car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Unknown", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.04399}, {"_id": {"$oid": "67abcdbf273f786abebfa957"}, "ID": "26_904_3075_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Average car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Plug-in Hybrid Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.02934}, {"_id": {"$oid": "67abcdbf273f786abebfa958"}, "ID": "26_904_3076_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- cars (by size)", "Level 3": "Average car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Battery Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.01049}, {"_id": {"$oid": "67abcdbf273f786abebfa959"}, "ID": "26_905_3077_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- motorbike", "Level 3": "Small", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.02148}, {"_id": {"$oid": "67abcdbf273f786abebfa95a"}, "ID": "26_905_3078_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- motorbike", "Level 3": "Medium", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.02608}, {"_id": {"$oid": "67abcdbf273f786abebfa95b"}, "ID": "26_905_3079_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- motorbike", "Level 3": "Large", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.03469}, {"_id": {"$oid": "67abcdbf273f786abebfa95c"}, "ID": "26_905_3080_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- motorbike", "Level 3": "Average", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.02956}, {"_id": {"$oid": "67abcdbf273f786abebfa95d"}, "ID": "26_909_3141_11_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- taxis", "Level 3": "Electric taxi", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Battery Electric Vehicle", "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.015}, {"_id": {"$oid": "67abcdbf273f786abebfa95e"}, "ID": "26_909_3141_4_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- taxis", "Level 3": "Regular taxi", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.05176}, {"_id": {"$oid": "67abcdbf273f786abebfa95f"}, "ID": "26_910_3144_11_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- bus", "Level 3": "Electric bus", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Battery Electric Vehicle", "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.0028}, {"_id": {"$oid": "67abcdbf273f786abebfa960"}, "ID": "26_910_3145_11_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- bus", "Level 3": "Average local bus", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.02649}, {"_id": {"$oid": "67abcdbf273f786abebfa961"}, "ID": "26_910_3146_11_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- bus", "Level 3": "Coach", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.00656}, {"_id": {"$oid": "67abcdbf273f786abebfa962"}, "ID": "26_911_3147_11_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- rail", "Level 3": "National rail", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.00897}, {"_id": {"$oid": "67abcdbf273f786abebfa963"}, "ID": "26_911_3148_11_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- rail", "Level 3": "International rail", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.00117}, {"_id": {"$oid": "67abcdbf273f786abebfa964"}, "ID": "26_911_3149_11_1", "Scope": "Scope 3", "Level 1": "WTT- pass vehs & travel- land", "Level 2": "WTT- rail", "Level 3": "Light rail and tram", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.00749}]