exports = async (input) => {
    try {
        const request = context.services
            .get("mongodb-atlas")
            .db(context.environment.values.database)
            .collection("transaction");
        const result = await request.updateOne(
            {
                $and: [{ RegistrationNumber: user.RegistrationNumber }, { "Lines.RecordID": input.RecordID }],
            },
            {
                $set: {
                    "Lines.$.Description": input.Description,
                },
            }
        );
        return { status: "done", RecordID: input.RecordID };
    } catch (error) {
        console.log(error);
        return { status: "error" };
    }
    return {};
};
