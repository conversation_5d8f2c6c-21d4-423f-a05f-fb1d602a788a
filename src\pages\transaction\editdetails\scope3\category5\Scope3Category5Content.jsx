/* eslint-disable import/no-unresolved */
import React, { useState, useRef, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"
import Select from "react-select"

import wasteFactors from "../contants/wastefactors.beis.json"

import Button from "@/components/ui/Button" // Assuming alias setup
import Input from "@/components/ui/Input" // Assuming alias setup
import { useToast } from "@/hooks" // Assuming alias setup for hooks

function Scope3Category5Content(props) {
	const { category5Rows = [], updateCategory5Emission } = props

	const { t } = useTranslation("transactions")
	const weightInputRef = useRef()
	const toast = useToast()

	// Local state for the "Add New" form inputs
	const [materialType, setMaterialType] = useState(null)

	const [material, setMaterial] = useState("")
	const [level3Text, setLevel3Text] = useState("")
	const [columnTextlabel, setColumnTextlabel] = useState("")

	const [level3, setLevel3] = useState(null) // Level 3
	const [columnText, setColumnText] = useState(null) // Column Text
	const [factor, setFactor] = useState(0.0)
	const factorUnit = "Co2e" // Default unit - seems constant
	const [weight, setWeight] = useState(0.0)
	const weightUnit = "Tons" // Default unit - seems constant

	// State for dropdown options derived from wasteFactors
	const [materialTypeOptions, setMaterialTypeOptions] = useState([])
	const [level3Options, setLevel3Options] = useState([])
	const [columnTextOptions, setColumnTextOptions] = useState([])

	// --- Deriving Dropdown Options (will be empty if wasteFactors is empty) ---
	useEffect(() => {
		const uniqueLabelsMap = new Map()
		wasteFactors.forEach(factor => {
			const label = factor["Level 2"]
			// Use first encountered factor for a given Level 2 as representative
			if (label && !uniqueLabelsMap.has(label)) {
				uniqueLabelsMap.set(label, {
					label: label,
					value: label,
					factor: factor["GHG Conversion Factor"],
				})
			}
		})
		setMaterialTypeOptions(Array.from(uniqueLabelsMap.values()))
	}, [])

	const getLevel3Options = selectedLevel2Value => {
		const uniqueLabelsMap = new Map()
		wasteFactors.forEach(factor => {
			if (factor["Level 2"] === selectedLevel2Value) {
				const label = factor["Level 3"]
				if (label && !uniqueLabelsMap.has(label)) {
					uniqueLabelsMap.set(label, {
						label: label,
						value: label,
						factor: factor["GHG Conversion Factor"],
					})
				}
			}
		})
		setLevel3Options(Array.from(uniqueLabelsMap.values()))
		setColumnTextOptions([]) // Reset column text options when level 3 changes
	}

	const getColumnTextOptions = (selectedLevel2Value, selectedLevel3Value) => {
		const uniqueLabelsMap = new Map()
		wasteFactors.forEach(factor => {
			if (factor["Level 2"] === selectedLevel2Value && factor["Level 3"] === selectedLevel3Value) {
				const label = factor["Column Text"]
				if (label && !uniqueLabelsMap.has(label)) {
					uniqueLabelsMap.set(label, {
						label: label,
						value: label,
						factor: factor["GHG Conversion Factor"],
					})
				}
			}
		})
		setColumnTextOptions(Array.from(uniqueLabelsMap.values()))
	}

	// --- Input Handlers ---
	const handleMaterialTypeChange = selectedOption => {
		setMaterialType(selectedOption)
		setMaterial(selectedOption.label)
		setLevel3(null) // Reset dependent dropdowns
		setColumnText(null)
		setFactor(Number(selectedOption?.factor || 0.0).toFixed(2)) // Update factor
		getLevel3Options(selectedOption?.value)
		weightInputRef.current?.focus()
	}

	const handleLevel3Change = selectedOption => {
		setLevel3(selectedOption)
		setLevel3Text(selectedOption.label)
		setColumnText(null) // Reset dependent dropdown
		setFactor(Number(selectedOption?.factor || 0.0).toFixed(2)) // Update factor
		getColumnTextOptions(materialType?.value, selectedOption?.value)
	}

	const handleColumnTextChange = selectedOption => {
		setColumnText(selectedOption)
		setColumnTextlabel(selectedOption.label)
		setFactor(Number(selectedOption?.factor || 0.0).toFixed(2)) // Update factor
	}

	// --- Add/Delete/Calculate Logic ---
	const addCategory5Row = calculatedScope3 => {
		const newCategory5Row = {
			materialType: material,
			level3: level3Text,
			columnText: columnTextlabel,
			factor: Number(factor),
			factorUnit: factorUnit,
			weight: Number(weight),
			weightUnit: weightUnit,
			scope3: Number(calculatedScope3),
		}

		const newCategory5RowsList = [...category5Rows, newCategory5Row]

		updateCategory5Emission(newCategory5Row, false, "scope3Category5Rows", newCategory5RowsList, 5) // false indicates addition

		// Reset form fields
		setMaterialType(null)
		setLevel3(null)
		setColumnText(null)
		setFactor(0.0)
		setWeight(0.0)
		setLevel3Options([])
		setColumnTextOptions([])
	}

	// Calculates emission and triggers adding the row
	const handleAddEmission = () => {
		// Basic validation
		if (!materialType || !level3 || !columnText) {
			toast("error", t("material_level3_disposal_error"))
			return
		}
		if (parseFloat(factor) < 0 || parseFloat(weight) < 0) {
			toast("error", t("factor_weight_non_negative_error"))
			return
		}

		const numFactor = parseFloat(factor)
		const numWeight = parseFloat(weight)

		let recycledWeight = numWeight / 100

		let otherWeight = numWeight - recycledWeight

		// Calculation based on calculateCategory5Emissions: scope3 = weight * factor
		// The original calculation involving recycledShare is complex and its effect on the final scope sum is unclear.
		// Sticking to the simpler pattern: factor represents the emission per unit weight for the chosen disposal method.
		let calculatedScope3 = otherWeight * numFactor
		addCategory5Row(calculatedScope3)
	}

	// Deletes a row from the list by index
	const deleteCategory5Row = index => {
		const rowToDelete = category5Rows[index]
		const newCategory5RowsList = [...category5Rows]
		newCategory5RowsList.splice(index, 1)
		updateCategory5Emission(rowToDelete, true, "scope3Category5Rows", newCategory5RowsList, 5) // true indicates deletion
	}

	const handleFocus = event => event.target.select()

	return (
		<>
			{/* Display existing/saved rows */}
			{category5Rows.map((category5Item, index) => (
				<div key={index}>
					{/* Simplified saved view - adjust columns as needed */}
					<div className="grid grid-cols-7 gap-3 my-1 saved-emission">
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("MaterialType")}
							</label>
							<div>{category5Item.materialType || "N/A"}</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("Level 3")}
							</label>
							<div>{category5Item.level3 || "N/A"}</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("Disposal")}
							</label>{" "}
							{/* Changed label */}
							<div>{category5Item.columnText || "N/A"}</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("Factor")}
							</label>
							<div className="flex relative">
								<span>{Number(category5Item.factor).toFixed(2)}</span>
								<span className="text-nowrap custom-span-unit-value-save">
									{category5Item.factorUnit || "Co2e"}
								</span>
							</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("Weight")}
							</label>
							<div className="flex relative">
								<span>{category5Item.weight}</span>
								<span className="text-nowrap custom-span-unit-value-save">
									{category5Item.weightUnit || "Tons"}
								</span>
							</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("TotalCo2e")}
							</label>
							<div className="flex relative">
								<span>{Number(category5Item.scope3 || 0).toFixed(2)}</span>
								<span className="text-nowrap custom-span-unit-value-save">{t("kg")}</span>
							</div>
						</div>
						<div className="!items-center delete-icon">
							<span
								aria-hidden
								onClick={() => deleteCategory5Row(index)}
								style={{ cursor: "pointer" }}
							>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}

			{/* Form for adding new rows */}
			{/* Adjusted grid columns for the form */}
			<div className="grid grid-cols-3 gap-2 my-2">
				<div>
					<label
						className="text-slate-800 font-semibold whitespace-nowrap"
						htmlFor={"c5-material-type-new"}
					>
						{t("MaterialType")}
					</label>
					<Select
						id={"c5-material-type-new"}
						value={materialType}
						onChange={handleMaterialTypeChange}
						options={materialTypeOptions}
						placeholder={t("Select...")}
						isDisabled={wasteFactors.length === 0} // Disable if no factors loaded
					/>
				</div>
				<div>
					<label
						className="text-slate-800 font-semibold whitespace-nowrap"
						htmlFor={"c5-level3-new"}
					>
						{t("Level 3")}
					</label>
					<Select
						id={"c5-level3-new"}
						value={level3}
						onChange={handleLevel3Change}
						options={level3Options}
						placeholder={t("Select...")}
						isDisabled={!materialType || wasteFactors.length === 0}
					/>
				</div>
				<div>
					<label
						className="text-slate-800 font-semibold whitespace-nowrap"
						htmlFor={"c5-columntext-new"}
					>
						{t("Disposal Method")} {/* Changed label */}
					</label>
					<Select
						id={"c5-columntext-new"}
						value={columnText}
						onChange={handleColumnTextChange}
						options={columnTextOptions}
						placeholder={t("Select...")}
						isDisabled={!level3 || wasteFactors.length === 0}
					/>
				</div>
				<div>
					{/* Factor is derived from selections */}
					<Input
						label={t("Factor")}
						type="number"
						labelColor="text-slate-500" // Indicate read-only/derived
						readOnly
						unit={factorUnit}
						value={Number(factor).toFixed(2)}
					/>
				</div>
				<div>
					<Input
						label={t("Weight")}
						type="number"
						labelColor="text-sky-500"
						placeholder={t("Enter weight")}
						unit={weightUnit}
						value={weight}
						handleChange={e => setWeight(e.target.value)}
						handleFocus={handleFocus}
						ref={weightInputRef}
						min="0"
						step="any"
					/>
				</div>
				<div className="self-end mb-1">
					<Button title={`${t("Add")} | +`} handleClick={handleAddEmission} color="sky-500" />
				</div>
			</div>
		</>
	)
}

export default Scope3Category5Content
