<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="/favicon.ico" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<!-- <meta name="theme-color" content="#000000" /> -->
		<meta name="description" content="Scope321" />
		<!-- <link href="https://cdn.syncfusion.com/ej2/tailwinds.css" rel="stylesheet"> -->
		<link href="/tailwind.css" rel="stylesheet" />

		<link rel="apple-touch-icon" href="/logo192.png" />
		<!-- src/styles/tailwind.css
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
		<link rel="manifest" href="/manifest.json" />
		<!--
      Notice the use of  in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
		<title>Scope321 App</title>
	</head>

	<body>
		<noscript>You need to enable JavaScript to run this app.</noscript>
		<form name="subscribe" netlify netlify-honeypot="bot-field" hidden>
			<input type="text" name="name" />
			<input type="email" name="email" />
			<input type="text" name="phonenumber" />
			<textarea name="message"></textarea>
		</form>
		<form name="addOnAccess" netlify netlify-honeypot="bot-field" hidden>
			<input type="text" name="name" />
			<input type="email" name="email" />
			<input type="text" name="phonenumber" />
		</form>
		<form name="FileIssue" netlify netlify-honeypot="bot-field" hidden>
			<input type="email" name="User Email" />
			<input type="text" name="Registration Number" />
			<input type="text" name="User ID" />
			<input type="text" name="Error Message" />
		</form>
		<div id="root"></div>
		<div class="bg-red-200 hidden"></div>
		<script type="module" src="/src/index.jsx"></script>
		<!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
	</body>
</html>
