const fs = require("fs")
const path = require("path")

require("dotenv").config()
const { Storage } = require("@google-cloud/storage")

// Determine credentials source
let credentials = null

// If running on Netlify (JSON stored in env variable)
if (process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON) {
	credentials = JSON.parse(process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON)
}
// If running locally (Read from file)
else if (process.env.VITE_GOOGLE_APPLICATION_CREDENTIALS) {
	const credentialsPath = path.resolve(process.env.VITE_GOOGLE_APPLICATION_CREDENTIALS)
	if (fs.existsSync(credentialsPath)) {
		credentials = JSON.parse(fs.readFileSync(credentialsPath, "utf-8"))
	} else {
		throw new Error(`Credentials file not found at: ${credentialsPath}`)
	}
}
// No credentials found
else {
	throw new Error(
		"Missing credentials: Define GOOGLE_APPLICATION_CREDENTIALS_JSON for Netlify or VITE_GOOGLE_APPLICATION_CREDENTIALS for local."
	)
}

// Initialize Google Cloud Storage
const storage = new Storage({ credentials })

const BUCKET_NAME = process.env.CLOUD_STORAGE_BUCKET

async function generateSignedUrl({ fileName, contentType }) {
	console.log(process.env.BRANCH, process.env.CLOUD_STORAGE_BUCKET)
	try {
		if (!BUCKET_NAME) {
			throw new Error("GCS_BUCKET_NAME environment variable is not set")
		}

		if (!fileName || !contentType) {
			throw new Error("fileName and contentType are required")
		}

		const bucket = storage.bucket(BUCKET_NAME)
		const file = bucket.file(fileName)

		const [signedUrl] = await file.getSignedUrl({
			version: "v4",
			action: "write",
			expires: Date.now() + 15 * 60 * 1000, // 15 minutes
			contentType,
		})

		return {
			signedUrl,
			fileId: fileName,
			fileName,
			contentType,
			publicUrl: `https://storage.googleapis.com/${BUCKET_NAME}/${fileName}`,
		}
	} catch (error) {
		console.error("Error generating signed URL:", error)
		throw error
	}
}

const completeUpload = async (req, res) => {
	try {
		if (!BUCKET_NAME) {
			return res.status(500).json({ error: "GCS_BUCKET_NAME environment variable is not set" })
		}

		const { fileId, fileName, contentType, fileSize } = req.body

		if (!fileId || !fileName) {
			return res.status(400).json({ error: "fileId and fileName are required" })
		}

		const fileInfo = {
			id: fileId,
			name: fileName,
			url: `https://storage.googleapis.com/${BUCKET_NAME}/${fileId}`,
			size: parseInt(fileSize) || 0,
			contentType,
			uploadedAt: new Date().toISOString(),
		}

		return res.status(200).json(fileInfo)
	} catch (error) {
		console.error("Error completing upload:", error)
		return res.status(500).json({ error: "Failed to complete upload", details: error.message })
	}
}

const getFiles = async (req, res) => {
	try {
		if (!BUCKET_NAME) {
			return res.status(500).json({ error: "GCS_BUCKET_NAME environment variable is not set" })
		}

		const bucket = storage.bucket(BUCKET_NAME)
		const [files] = await bucket.getFiles()

		const fileInfos = await Promise.all(
			files.map(async (file) => {
				const [metadata] = await file.getMetadata()
				return {
					id: file.name,
					name: file.name.split("-").slice(1).join("-"),
					url: `https://storage.googleapis.com/${BUCKET_NAME}/${file.name}`,
					size: parseInt(metadata.size),
					contentType: metadata.contentType || "application/octet-stream",
					uploadedAt: metadata.timeCreated,
				}
			})
		)

		fileInfos.sort((a, b) => new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime())

		return res.status(200).json(fileInfos)
	} catch (error) {
		console.error("Get files error:", error)
		return res.status(500).json({ error: "Failed to retrieve files", details: error.message })
	}
}

const deleteFile = async (req, res) => {
	console.log(process.env.BRANCH, process.env.CLOUD_STORAGE_BUCKET)

	console.log(BUCKET_NAME)
	try {
		if (!BUCKET_NAME) {
			return res.status(500).json({ error: "CLOUD_STORAGE_BUCKET environment variable is not set" })
		}

		const fileId = req.params.fileId
		if (!fileId) {
			return res.status(400).json({ error: "File ID is required" })
		}

		console.log("\n\n File Id 11:", fileId)

		const bucket = storage.bucket(BUCKET_NAME)
		await bucket.file(fileId).delete()

		return res.status(200).json({ message: "File deleted successfully" })
	} catch (error) {
		console.error("Delete file error:", error)
		return res.status(500).json({ error: "Failed to delete file", details: error.message })
	}
}

module.exports = { generateSignedUrl, completeUpload, getFiles, deleteFile }
