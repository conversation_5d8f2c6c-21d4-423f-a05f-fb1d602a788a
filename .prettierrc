{"semi": false, "trailingComma": "es5", "singleQuote": false, "printWidth": 100, "tabWidth": 2, "useTabs": true, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "experimentalTernaries": false}