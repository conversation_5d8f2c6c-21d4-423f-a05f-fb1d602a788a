exports = async (args) => {
	const { RegistrationNumber, Name, AuthId, ContactPerson, Industry, NaceCode, Subscription } = args

	try {
		const payload = { RegistrationNumber, Name, AuthId, ContactPerson, Industry, NaceCode, Subscription }

		const companyCollection = context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("company")

		// Check if a company with the same Registration Number already exists
		const existingCompany = await companyCollection.findOne({ RegistrationNumber })

		if (existingCompany) {
			// If a company with the same Registration Number already exists, do not add a new one.
			return { success: false, status: 201 }
		}

		// If the company doesn't exist, insert it into the database
		await companyCollection.insertOne(payload)

		// Update the user document with RegistrationNumber and CompanyName
		const userCollection = context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("user")

		await userCollection.updateOne({ netlifyID: AuthId }, { $set: { RegistrationNumber, CompanyName: Name } })

		return { success: true }
	} catch (error) {
		console.log(error)
		return { success: false, message: error.message }
	}
}
