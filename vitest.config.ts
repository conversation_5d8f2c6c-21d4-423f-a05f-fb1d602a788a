/// <reference types="vitest" />
import path from "path"

import react from "@vitejs/plugin-react"
import { defineConfig } from "vitest/config"

export default defineConfig({
	plugins: [react()],
	test: {
		globals: true,
		environment: "jsdom",
		setupFiles: ["./src/test/setup.ts"],
		css: true,
		include: ["src/**/*.{test,spec}.{js,jsx,ts,tsx}"],
		exclude: [
			"**/node_modules/**",
			"**/dist/**",
			"**/build/**",
			"**/.{idea,git,cache,output,temp}/**",
			"**/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build}.config.*",
		],
		coverage: {
			provider: "v8",
			reporter: ["text", "json", "html", "lcov"],
			reportsDirectory: "./coverage",
			exclude: [
				"node_modules/",
				"src/test/",
				"**/*.d.ts",
				"**/*.config.*",
				"**/coverage/**",
				"**/dist/**",
				"**/build/**",
				"**/*.stories.*",
				"**/*.story.*",
				"**/index.{js,ts,jsx,tsx}",
				"src/main.tsx",
				"src/vite-env.d.ts",
			],
			thresholds: {
				global: {
					branches: 80,
					functions: 80,
					lines: 80,
					statements: 80,
				},
			},
			all: true,
			skipFull: false,
		},
		// Enhanced test configuration
		testTimeout: 10000,
		hookTimeout: 10000,
		teardownTimeout: 5000,
		isolate: true,
		pool: "threads",
		poolOptions: {
			threads: {
				singleThread: false,
			},
		},
		// Better error reporting
		reporters: ["verbose", "json", "html"],
		outputFile: {
			json: "./test-results/results.json",
			html: "./test-results/results.html",
		},
	},
	resolve: {
		alias: {
			"@": path.resolve(__dirname, "src"),
		},
	},
})
