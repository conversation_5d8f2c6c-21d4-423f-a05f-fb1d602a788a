exports = async (input) => {
	try {
		var {
			NaceCode,
			Description,
			Scope_1,
			Scope_2,
			Scope_3,
			marketBased,
			Notes,
			Scope_3_Category,
			Status,
			supplierFactor,
			SupplierName,
			SupplierID,
		} = input

		// Update trasactions.
		const request = context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("transaction")
		let transactionId = new BSON.ObjectId(input._id)
		let payload = { NaceCode, Description, Scope_1, Scope_2, Scope_3, Notes, Scope_3_Category, Status, marketBased }
		if (SupplierID) {
			payload.SupplierID = SupplierID
			payload.SupplierName = SupplierName
			payload.supplierFactor = supplierFactor
		}

		const result2 = await request.updateOne(
			{
				_id: transactionId,
			},
			{
				$set: {
					...payload,
				},
			}
		)
		return { status: "done", TransactionID: input.TransactionID, Scope_1, Scope_2, Scope_3 }
	} catch (err) {
		console.log(err)
		return { status: "error" }
	}
}
