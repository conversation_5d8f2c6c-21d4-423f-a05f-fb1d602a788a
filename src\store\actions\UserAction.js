export function UserAction(userData) {
	return {
		type: "user_data",
		payload: userData,
	}
}

export function currentOrganizationAction(organizationData) {
	return {
		type: "current_organization",
		payload: organizationData,
	}
}

export function refetchOrg(refetchOrg) {
	return {
		type: "refetch_org",
		payload: refetchOrg,
	}
}

export function fileUploaded(fileUploaded) {
	return {
		type: "file_uploaded",
		payload: fileUploaded,
	}
}

export const handleSetReportingYear = year => {
	return {
		type: "reporting_year",
		payload: year,
	}
}

export const handleWorkingOnData = working => {
	return {
		type: "working_on_data",
		payload: working,
	}
}

export const openSubscriptionModal = () => {
	return {
		type: "OPEN_SUBSCRIPTION_MODAL",
	}
}

export const closeSubscriptionModal = () => {
	return {
		type: "CLOSE_SUBSCRIPTION_MODAL",
	}
}

export const isCompanyRegistered = isRegistered => {
	if (isRegistered) {
		return {
			type: "COMPANY_REGISTERED",
			payload: true,
		}
	} else {
		return {
			type: "COMPANY_NOT_REGISTERED",
			payload: false,
		}
	}
}

export const openReportingEntityModal = () => {
	return {
		type: "OPEN_REPORTING_ENTITY_MODAL",
	}
}

export const closeReportingEntityModal = () => {
	return {
		type: "CLOSE_REPORTING_ENTITY_MODAL",
	}
}

export const handleSetScopeData = scopeData => {
	return {
		payload: scopeData,
		type: "SET_SCOPE_DATA",
	}
}

export const handleProjectReportAccess = hasAcess => {
	return {
		payload: hasAcess,
		type: "SET_PROJECT_REPORT_ACCESS",
	}
}
