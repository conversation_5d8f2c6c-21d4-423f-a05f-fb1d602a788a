exports = async (input) => {
  // First get all the supplier from "companies" collection for perticular user.
  const request = context.services
    .get("mongodb-atlas")
    .db(context.environment.values.database)
    .collection("company")
  let suppliers = []
  try {
    const allSuppliers = await request
      .find(
        { RegistrationNumber: input.RegistrationNumber },
        {
          _id: 0,
          "Suppliers.SupplierID": 1,
          "Suppliers.Name": 1,
          "Suppliers.SupplierCo2Intensity": 1,
          "Suppliers.IndustryCo2Intensity":1,
          "Suppliers.Industry":1,
          "Suppliers.NaceCode":1
        }
      )
      .toArray()
    if (!allSuppliers || !allSuppliers.length) {
      console.error(input.RegistrationNumber, "Supplier not found")
      return { success: false, data: [] }
    }

    // company._id = allSuppliers[0]._id
    // Concat all the suppliers.

    suppliers = allSuppliers[0].Suppliers.map((supplier) => {
      return {
        label: supplier.Name,
        value: supplier.SupplierID,
        SupplierCo2Intensity: supplier.SupplierCo2Intensity,
        IndustryCo2Intensity: supplier.IndustryCo2Intensity,
        Industry: supplier.Industry,
        NaceCode: supplier.NaceCode
      }
    })
  } catch (err) {
    console.log(err)
    return { success: false, data: [] }
  }
  return { success: true, data: suppliers }
}
