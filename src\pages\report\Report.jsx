/* eslint-disable import/order */
/* eslint-disable import/no-unresolved */
import { ProgressBarComponent } from "@syncfusion/ej2-react-progressbar"
import {
	HtmlEditor,
	Image,
	Inject,
	Link,
	QuickToolbar,
	RichTextEditorComponent,
	Toolbar,
	Table,
} from "@syncfusion/ej2-react-richtexteditor"
import { Card, Flex, Metric } from "@tremor/react"
import { ref, uploadBytesResumable, getDownloadURL } from "firebase/storage"
import React, { useState, useEffect, useRef, Fragment } from "react"
import { useTranslation } from "react-i18next"
import { RiEdit2Line } from "react-icons/ri"
import { useSelector, useDispatch } from "react-redux"
import Select from "react-select"
import { v1 as uuidv1 } from "uuid"

import AddReportingEntityModal from "../../components/AddReportingEntityModal.component"
import RichTextEditor from "../../components/RichTextEditor.jsx"
import PieChart from "../../components/ui/PieChart"
import PrimaryDataMetric from "../../components/ui/PrimaryDataMetric"
import ScopeMetricCard from "../../components/ui/ScopeMetricCard"
import Spinner from "../../components/ui/Spinner"
import {
	useGetScopeChartData,
	useGetTopSuppliers,
	useGetScopeCategoryData,
	useGetCO2AddedData,
	useGetMonthlyChartData,
	useGetYearlyChartData,
} from "../../hooks"
import { useRealmApp } from "../../realm/RealmAppProvider"
import "../../styles/dashboard.css"
import "../../styles/sidebar.css"
import "./index.css"
import { getPrimaryDataColors } from "../../services/helper"
import { currentOrganizationAction } from "../../store/actions/UserAction"
import BarChartComponent from "../dashboard/BarChart.component"
import { dynamicStorage } from "../firebase/firebase"
import useFullPageLoader from "../loader/useFullPageLoader"

import { reportdata } from "./REPORT_CONSTANTS"

import { Dialog, Transition } from "@headlessui/react"

const ImportReport = ({
	t,
	show,
	handleClose,
	handleContinue,
	yearToImportReport,
	setYearToImportReport,
	options,
}) => {
	return (
		<Transition show={show} as={Fragment}>
			<Dialog as="div" className="relative z-50 max-w-[400px]" onClose={handleClose}>
				<Transition.Child
					as={Fragment}
					enter="ease-out duration-300"
					enterFrom="opacity-0"
					enterTo="opacity-100"
					leave="ease-in duration-200"
					leaveFrom="opacity-100"
					leaveTo="opacity-0"
				>
					<div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
				</Transition.Child>

				<div className="fixed inset-0 z-10 overflow-y-auto">
					<div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
						<Transition.Child
							as={Fragment}
							enter="ease-out duration-300"
							enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
							enterTo="opacity-100 translate-y-0 sm:scale-100"
							leave="ease-in duration-200"
							leaveFrom="opacity-100 translate-y-0 sm:scale-100"
							leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
						>
							<Dialog.Panel className="relative transform rounded-lg bg-white px-4 pb-4 pt-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
								<div className="sm:flex sm:items-start">
									<div className=" sm:ml-4 sm:mt-0 sm:text-left">
										<Dialog.Title as="h2" className="text-lg font-semibold leading-6 text-black">
											{t("importdata")}
										</Dialog.Title>
										<div className="">
											<p className="text-sm text-slate-700 mb-0">{t("importText")}</p>
										</div>
									</div>
								</div>

								<div className="z-50">
									<label className="mb-3 ml-4 mt-4 text-base font-semibold" htmlFor={"year"}>
										{t("year")}
									</label>
									<Select
										className="w-4/5 px-4 mt-3"
										id={"year"}
										value={yearToImportReport}
										onChange={e => {
											//(e)
											setYearToImportReport(e)
										}}
										options={options}
									/>
								</div>

								<div className=" sm:flex mt-3 sm:flex-row-reverse">
									<button
										onClick={handleContinue}
										className="mt-3 inline-flex w-full justify-center rounded-md bg-sky-600 px-3 py-2 ml-2 text-sm font-semibold text-white shadow-sm border border-inset hover:bg-sky-600 sm:mt-0 sm:w-auto"
									>
										{t("import")}
									</button>
									<button
										onClick={handleClose}
										className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-sky-700 shadow-sm border border-inset border-sky-400 hover:bg-sky-50 sm:mt-0 sm:w-auto"
									>
										{t("cancel")}
									</button>
								</div>
							</Dialog.Panel>
						</Transition.Child>
					</div>
				</div>
			</Dialog>
		</Transition>
	)
}

const Report = props => {
	const { t, i18n } = useTranslation("report")
	const app = useRealmApp()
	const currentOrganization = useSelector(state => state.user.currentOrganization)
	const reportingYear = useSelector(state => state.reporting)

	const scrollToTop = useRef(null)

	const isCompanyRegistered = useSelector(state => state.company)

	const dispatch = useDispatch()

	const RegistrationNumber = currentOrganization ? currentOrganization.RegistrationNumber : ""
	const colorCode = [
		"#FF0000",
		"#FFCC00",
		"#FF6600",
		"#990000",
		"#330000",
		"#1A237E",
		"#33CC00",
		"#006064",
	]
	const [scopeData, setScopeData] = useState({
		Scope_1: 0.0,
		Scope_2: 0.0,
		Scope_3: 0.0,
		total: 0.0,
		isDefault: true,
	})

	const [scopeDataLocationBased, setScopeDataLocationBased] = useState({
		Scope_1: 0.0,
		Scope_2: 0.0,
		Scope_3: 0.0,
		total: 0.0,
		isDefault: true,
	})

	const [scopeCategoryData, setScopeCategoryData] = useState([])
	const [isScopeCategoryData, setIsScopeCategoryData] = useState(false)
	const [topSuppliers, setTopSuppliers] = useState([])

	//const [co2Added, setCo2Added] = useState([])
	//const [totalCo2Added, setTotalCo2Added] = useState(0)
	const [isTotalCo2Added, setIsTotalCo2Added] = useState(false)
	const [totalEnergy, setTotalEnergy] = useState(0)
	const [energyData, setEnergyData] = useState([])
	const [energyChartValues, setEnergyChartValues] = useState([])
	const isUnmounted = useRef(false)

	const [loader, showLoader, hideLoader] = useFullPageLoader()
	const [scope2Data, setScope2Data] = useState([])

	const rteRef = useRef(null)

	const [companyInfoData, setCompanyInfoData] = useState({ ...reportdata })
	const [hasReport, setHasReport] = useState(true)
	const [selectedKey, setSelectedKey] = useState("")
	const [companyInfo, setCompanyInfo] = useState([])
	const fileChooseRef = useRef()
	const [progress, setProgress] = useState(0)
	const [logoUploading, setLogoUploading] = useState(false)
	const [imageUploading, setImageUploading] = useState(false)
	const [yearlyData, setYearlyData] = useState([])

	const [yearToImportReport, setYearToImportReport] = useState({ label: "2023", value: "2023" })

	const [scopeCategoryPrimaryData, setScopeCategoryPrimaryData] = useState([])

	const [availableYearsToImportReport, setAvailableYearsToImportReport] = useState([])

	const [scopePrimaryData, setScopePrimaryData] = useState([])

	const [loading, setLoading] = useState(true)

	const [showImportDailogue, setShowImportDailogue] = useState(false)

	const [totalPrimaryScope, setTotalPrimaryScope] = useState(null)

	const [downStreamCatagoryEmission, setDownStreamCatagoryEmission] = useState(null)

	const scope_Data = useGetScopeChartData(app, { RegistrationNumber, year: reportingYear })

	const top_Suppliers = useGetTopSuppliers(app, { RegistrationNumber, year: reportingYear })

	const yearlyEmissionData = useGetYearlyChartData(app, { RegistrationNumber })

	const monthlyChartData = useGetMonthlyChartData(app, { RegistrationNumber, year: reportingYear })

	const scope_catagory_data = useGetScopeCategoryData(app, {
		RegistrationNumber,
		year: reportingYear,
	})

	const CO2AddedData = useGetCO2AddedData(app, {
		RegistrationNumber,
		year: reportingYear,
	})

	const [companyInfoEdit, setCompanyInfoEdit] = useState({
		ceomessage: false,
		ourClimateCommitment: false,
		energy: false,
		totalco2emission: false,
		scope1emission: false,
		scope2emission: false,
		scope3emission: false,
		suppliers: false,
		c1: false,
		c2: false,
		c3: false,
		c4: false,
		c5: false,
		c6: false,
		c7: false,
		c8: false,
		c9: false,
		Metodikk: false,
		refer: false,
	})
	const toolbarSettings = {
		items: [
			"Bold",
			"FontName",
			"FontSize",
			"FontColor",
			"Formats",
			"CreateLink",
			"Alignments",
			"NumberFormatList",
			"BulletFormatList",
			"CreateTable",
			"Image",
			"SourceCode",
		],
	}

	const generateColorCode = () => {
		const randomColor = Math.floor(Math.random() * 16777215).toString(16)
		return randomColor
	}

	const handleUpdateCompanyInfo = async (isPic = false, data) => {
		let report = { ...companyInfoData }
		if (isPic) {
			report = data
		}
		try {
			showLoader()
			const oldData = [{ ...companyInfo }, report]
			const newData = [...new Map(oldData.map(item => [item["year"], item])).values()]
			setCompanyInfo(newData)

			await app.updateCompanyReport(RegistrationNumber, { ...report, year: reportingYear })

			hideLoader()
		} catch (error) {
			// handle error
		}
	}

	const updateCompanyLogo = async link => {
		try {
			await app.updateCompanyInfo(RegistrationNumber, { Logo: link })
			const newData = { ...currentOrganization }
			newData.company["Logo"] = link
			dispatch(currentOrganizationAction(newData))
		} catch (error) {
			// handle error
		}
	}

	useEffect(() => {
		isUnmounted.current = false
	}, [currentOrganization.organizationId])

	const prevValues = useRef({ year: null, orgId: null })

	const getCompanyReport = async year => {
		setLoading(true)
		const data = await app.getCompanyReport(RegistrationNumber, year)
		setLoading(false)

		if (!data.success) {
			setHasReport(false)
			return
		}

		const { yearsToImportReport, report, reportFound, canImportReport } = data

		setHasReport(canImportReport)

		if (reportFound) {
			setCompanyInfo(report)
			setCompanyInfoData(report)
		}

		setAvailableYearsToImportReport(yearsToImportReport)

		// if (data.success) {
		// 	let info = data.company.report !== null ? data.company.report : ""
		// 	setCompanyInfo(info)
		// 	delete info["__typename"]
		// 	setAvailableYearsToImportReport(data?.company?.years)
		// 	setCompanyInfoData(info)
		// } else {
		// 	if (data?.hasReports === false) {
		// 		setHasReport(false)
		// 		return
		// 	}

		// 	toast("error", `Report for year ${year} not found.`)

		// 	setCompanyInfoData({ ...reportdata })
		// }
	}

	useEffect(() => {
		if (
			prevValues.current.year !== reportingYear ||
			prevValues.current.orgId !== currentOrganization.organizationId
		) {
			getCompanyReport(reportingYear)
			prevValues.current = {
				year: reportingYear,
				orgId: currentOrganization.organizationId,
			}
		}
	}, [reportingYear, currentOrganization.organizationId])

	useEffect(() => {
		const getCo2Added = async () => {
			try {
				/* eslint react-hooks/exhaustive-deps: 0 */
				setIsTotalCo2Added(false)
				const res = CO2AddedData
				//		if (res.data.success) {
				const total =
					Math.round(res.data?.totalNonRenewable || 0) +
						Math.round(res.data?.totalRenewable || 0) +
						Math.round(res.data?.totalMobile || 0) +
						Math.round(res.data?.totalStationary || 0) +
						Math.round(res.data?.totalNuclear || 0) || 0

				const energyData = [
					{
						name: "Stationary combustion",
						value: Math.round(res.data?.totalStationary || 0),
						percentage: (Math.round(res.data?.totalStationary || 0) / total) * 100 || 0,
					},
					{
						name: "Mobile combustion",
						value: Math.round(res.data?.totalMobile || 0),
						percentage: (Math.round(res.data?.totalMobile || 0) / total) * 100 || 0,
					},
					{
						name: "Renewable electricity",
						value: Math.round(res.data?.totalRenewable || 0),
						percentage: (Math.round(res.data?.totalRenewable || 0) / total) * 100 || 0,
					},
					{
						name: "Non-Renewable electricity",
						value: Math.round(res.data?.totalNonRenewable || 0),
						percentage: (Math.round(res.data?.totalNonRenewable || 0) / total) * 100 || 0,
					},
					{
						name: "Nuclear energy",
						value: Math.round(res.data?.totalNuclear || 0),
						percentage: (Math.round(res.data?.totalNuclear || 0) / total) * 100 || 0,
					},
				]

				setEnergyChartValues({
					"Stationary combustion": Math.round(res.data?.totalStationary || 0),
					"Mobile combustion": Math.round(res.data?.totalMobile || 0),
					"Renewable electricity": Math.round(res.data?.totalRenewable || 0),
					"Non-Renewable electricity": Math.round(res.data?.totalNonRenewable || 0),
					"Nuclear energy": Math.round(res.data?.totalNuclear || 0),
				})

				//setTotalCo2Added(co2Added)
				//setCo2Added(data)
				setTotalEnergy(total)
				setEnergyData(energyData)
				//	}
				setIsTotalCo2Added(true)

				setTotalPrimaryScope((res?.data?.totalPrimaryScope / res.data?.totalScope) * 100)

				setScopePrimaryData(res?.data?.scopePercentageWithStatus1 || [])
			} catch (error) {
				// Handle Error
				setIsTotalCo2Added(true)
			}
		}

		//if (!scopeData.isDefault) {
		getCo2Added()
		//}
	}, [CO2AddedData.data])

	useEffect(() => {
		try {
			if (isScopeCategoryData) {
				let heightOne = 0
				heightOne += document.getElementById("scope-sub-container").clientHeight
				document.getElementById("bar-chart-container").style.maxHeight = heightOne + 24 + "px"
				document.getElementById("bar-chart-container").style.paddingTop = 24 + "px"
			}
		} catch (error) {
			// Handle Error
		}
	}, [isScopeCategoryData])

	useEffect(() => {
		if (top_Suppliers.data && top_Suppliers.data.success) {
			setTopSuppliers(top_Suppliers.data.topSuppliers)
		}
	}, [top_Suppliers.data])

	useEffect(() => {
		//setIsScopeCategoryData(false)
		const getScopeCategoryData = async () => {
			try {
				/* eslint react-hooks/exhaustive-deps: 0 */

				const res = scope_catagory_data.data
				//if (isUnmounted.current) return
				//	if (res.success) {
				if (res && res.categoryData && res.categoryData.length > 8) {
					for (let i = 8; i < res.categoryData.length; i++) {
						/* eslint react-hooks/exhaustive-deps: 0 */
						colorCode.push(`#${generateColorCode()}`)
					}
				}

				setDownStreamCatagoryEmission(res?.downStreamTotalEmissionCatagory || 0)

				setScopeCategoryPrimaryData(res?.categoriesWithStatus1 || [])
				setScopeCategoryData(res.categoryData)
				setIsScopeCategoryData(true)
			} catch (error) {
				// Handle Error
			}
		}

		//getScopeData()
		// getTopSuppliers()
		setIsScopeCategoryData(false)
		getScopeCategoryData()
		return () => {
			isUnmounted.current = true
		}
	}, [scope_catagory_data.data])

	useEffect(() => {
		if (scope_Data.data && scope_Data.data && scope_Data.data.scope) {
			const scopeEmissionChartData = scope_Data.data

			const { Scope_1, Scope_2, Scope_3, locationBased, consumptionBased } =
				scopeEmissionChartData.scope

			const total = Scope_1 + Scope_2 + Scope_3

			const data = {
				Scope_1: parseFloat(Scope_1),
				Scope_2: parseFloat(Scope_2),
				Scope_3: parseFloat(Scope_3),
				total: parseFloat(total),
				isDefault: false,
			}

			setScopeData(data)
			const Emdata = {
				combustionBased: consumptionBased ? Number(consumptionBased).toFixed(2) : 0,
				locationBased: locationBased ? Number(locationBased).toFixed(2) : 0,
				marketBased: Number(Scope_2).toFixed(2) || 0,
			}

			setScopeDataLocationBased({
				Scope_1: parseFloat(Scope_1),
				Scope_2: locationBased ? parseFloat(locationBased) : 0,
				Scope_3: parseFloat(Scope_3),
				total,
			})

			setScope2Data(Emdata)
		}
	}, [scope_Data.data])

	useEffect(() => {
		if (yearlyEmissionData?.data?.success) {
			setYearlyData(yearlyEmissionData.data.Data)
		}
	}, [yearlyEmissionData.data])

	const handleOpenEditor = key => {
		const newData = { ...companyInfoEdit }
		newData[key] = !newData[key]
		setCompanyInfoEdit(newData)
	}

	const handleFileUploading = async (e, key, data) => {
		try {
			if (key === "logo") {
				setLogoUploading(true)
			} else {
				setImageUploading(true)
			}

			const storage = dynamicStorage("gs://report-images-dev")
			const storageRef = ref(storage, `/images/${e.name}`)
			const uploadTask = uploadBytesResumable(storageRef, e)

			uploadTask.on(
				"state_changed",
				snapshot => {
					const progress = Math.round((snapshot.bytesTransferred / snapshot.totalBytes) * 100)
					setProgress(progress)
				},
				err => {
					// Handle error
					console.error(err)
				},
				() => {
					getDownloadURL(uploadTask.snapshot.ref).then(fireBaseUrl => {
						if (key === "logo") {
							updateCompanyLogo(fireBaseUrl)
							setLogoUploading(false)
							setProgress(0)
							return
						}
						setCompanyInfoData({ ...data, [key]: fireBaseUrl })
						handleUpdateCompanyInfo(true, { ...data, [key]: fireBaseUrl })
						setImageUploading(false)
						setProgress(0)
					})
				}
			)
		} catch (error) {
			// handle error
		}
	}

	const handleImageUploadSuccess = async args => {
		if (!args.e.target.response) return

		const s = JSON.parse(args.e.target.response)
		const path1 = `${args.e.target.responseURL}?alt=media&token=${s.downloadTokens}`

		// Access the editor instance correctly
		if (rteRef.current) {
			const selection = document.getSelection()
			const range = selection.getRangeAt(0)
			const img = document.createElement("img")
			img.src = path1
			img.width = 200
			img.height = 150
			range.insertNode(img)
		}

		const filename = document.querySelector(".e-file-name")
		const fileType = document.querySelector(".e-file-type")

		if (filename && fileType) {
			filename.innerHTML = args.file.name.replace(fileType.innerHTML, "")
			filename.title = path1
		}
	}

	const handleChangeContent = (key, content) => {
		const newData = { ...companyInfoData }

		newData[key] = content
		setCompanyInfoData(() => newData)
	}

	useEffect(() => {
		scrollToTop?.current?.scrollIntoView()
	}, [])

	const getDropDownOptions = () => {
		return availableYearsToImportReport.map(year => {
			return { label: String(year), value: String(year) }
		})
	}

	if (
		(top_Suppliers.isLoading ||
			scope_Data.isLoading ||
			scope_catagory_data.isLoading ||
			CO2AddedData.isLoading ||
			loading) &&
		isCompanyRegistered
	) {
		return (
			<div className="mainContainer">
				<Spinner />
			</div>
		)
	} else {
		return (
			<div ref={scrollToTop}>
				{!isCompanyRegistered ? (
					<AddReportingEntityModal props={props} />
				) : (
					<div className="max-w-5xl mx-auto">
						<ImportReport
							handleClose={() => setShowImportDailogue(false)}
							show={showImportDailogue}
							t={t}
							handleContinue={() => {
								setShowImportDailogue(false)
								getCompanyReport(yearToImportReport.value)
							}}
							yearToImportReport={yearToImportReport}
							setYearToImportReport={setYearToImportReport}
							options={getDropDownOptions()}
						/>
						<p
							aria-hidden
							onClick={() => window.print()}
							className="absolute ml-[-125px] mt-[-70px] p-0 text-sky-600 text-base font-bold cursor-pointer print:hidden"
						>
							{t("printPage")}
						</p>

						<p
							aria-hidden
							onClick={() => {
								if (hasReport) setShowImportDailogue(true)
							}}
							className={
								hasReport
									? "absolute ml-[-125px] mt-[-30px] p-0 text-sky-600 text-base font-bold cursor-pointer print:hidden"
									: "text-slate-300 cursor-not-allowed absolute ml-[-125px] mt-[-30px]"
							}
						>
							IMPORT ...
						</p>
						<input
							ref={fileChooseRef}
							type="file"
							hidden
							onChange={e => {
								handleFileUploading(e.target.files[0], selectedKey, companyInfoData)
							}}
						/>

						{/* Page 1*/}
						<section className="mt-24 px-3 py-2 shadow-md print:shadow-none bg-white/50 print:mt-0">
							<div className="flex justify-center">
								<h1 className="text-slate-800 font-extrabold">
									{t("Title")} {reportingYear}
								</h1>
							</div>

							{/* company logo */}
							<div className="mx-auto my-16 flex relative max-w-2xl flex-col items-center">
								{currentOrganization?.company?.Logo != null ? (
									<img
										src={currentOrganization.company.Logo}
										alt="not found"
										className="max-h-72 mx-auto object-contain print:block"
									/>
								) : (
									<div className="h-48 flex flex-col justify-center items-center print:hidden">
										<h5 className={logoUploading ? "hidden" : "align-middle"}>{t("Logo")}</h5>
									</div>
								)}
								<div className="h-full w-80 absolute top-0 group print:hidden">
									<div
										className={logoUploading ? "h-48 w-2/3" : "h-48 w-2/3 hidden group-hover:block"}
									/>
									{logoUploading ? (
										<div className="absolute top-2/4 left-2/4 -translate-x-2/4 -translate-y-2/4">
											<ProgressBarComponent
												id="logoProgress"
												type="Circular"
												height="160px"
												width="90%"
												segmentCount={10}
												trackThickness={10}
												progressThickness={10}
												value={progress}
												labelStyle={{ color: "black", fontWeight: "bold" }}
												enableRtl={false}
												showProgressValue={true}
												trackColor="black"
												radius="100%"
												progressColor="green"
												cornerRadius="Round"
												animation={{
													enable: true,
													duration: 2000,
													delay: 0,
												}}
											></ProgressBarComponent>
										</div>
									) : (
										<button
											onClick={() => {
												setSelectedKey("logo")
												fileChooseRef.current.click()
											}}
											className="absolute bg-sky-100 px-4 py-2 ring-2 ring-sky-600 font-semibold rounded-md text-slate-900 top-2/4 left-2/4 hidden group-hover:block -translate-x-2/4 -translate-y-2/4"
										>
											Upload Logo
										</button>
									)}
								</div>
							</div>

							{/* Message from Ceo <h2 className='text-2xl font-bold print:text-3xl'>{t("Ceo")}</h2>*/}
							<div className="purple m-4">
								<div className="indigo flex flex-row justify-between gap-x-8">
									<div
										className={
											companyInfoData.ceoPicLink != null
												? "l-blue w-2/3"
												: "l-blue w-2/3 print:w-full"
										}
									>
										<div
											className="green-1 text-base "
											dangerouslySetInnerHTML={{
												__html: companyInfoData.MESSAGE_FROM_CEO,
											}}
										/>
										<div className="green-2 print:hidden">
											<RiEdit2Line
												onClick={() => {
													handleOpenEditor("ceomessage")
												}}
												className="text-slate-800 w-6 h-6 hover:cursor-pointer"
											/>
										</div>
									</div>
									<div
										className={
											companyInfoData.ceoPicLink != null
												? "l-blue w-1/3 object-contain relative"
												: "l-blue w-1/3 object-contain relative print:hidden print:w-0"
										}
									>
										{companyInfoData.ceoPicLink != null ? (
											<img
												className="object-contain"
												src={companyInfoData.ceoPicLink}
												alt="not found"
											/>
										) : (
											<div className=" h-64 object-cover border border-c5 flex flex-col justify-center items-center print:hidden">
												<h5 className={imageUploading ? "hidden" : "align-middle"}>
													{t("Picture")}
												</h5>
											</div>
										)}

										<div className="h-64 w-80 absolute top-0 group print:hidden">
											<div
												className={
													imageUploading ? "h-64 w-80" : "h-64 w-80 hidden group-hover:block"
												}
											/>
											{imageUploading ? (
												<div className="absolute top-2/4 left-2/4 -translate-x-2/4 -translate-y-2/4">
													<ProgressBarComponent
														id="ceoImageProgress"
														type="Circular"
														height="160px"
														width="90%"
														segmentCount={10}
														trackThickness={10}
														progressThickness={10}
														value={progress}
														labelStyle={{ color: "black", fontWeight: "bold" }}
														enableRtl={false}
														showProgressValue={true}
														trackColor="white"
														radius="100%"
														progressColor="green"
														cornerRadius="Round"
														animation={{
															enable: true,
															duration: 2000,
															delay: 0,
														}}
													></ProgressBarComponent>
												</div>
											) : (
												<button
													onClick={() => {
														setSelectedKey("ceoPicLink")
														fileChooseRef.current.click()
													}}
													className="absolute bg-sky-100 px-4 py-2 ring-2 ring-sky-600 rounded-md text-slate-900 font-semibold top-2/4 left-2/4 hidden group-hover:block -translate-x-2/4 -translate-y-2/4"
												>
													Upload Picture
												</button>
											)}
										</div>
									</div>
								</div>

								{companyInfoEdit.ceomessage && (
									<>
										<div className="my-2 ml-8 print:hidden">
											<RichTextEditorComponent
												ref={rteRef}
												tableSettings={{
													styles: [
														{
															text: "Toggle Border",
															cssClass: "e-dashed-borders",
															command: "Table",
															subCommand: "Dashed",
														},
														{
															text: "Alternate Rows",
															cssClass: "e-alternate-rows",
															command: "Table",
															subCommand: "Alternate",
														},
													],
												}}
												imageUploadSuccess={e => {
													handleImageUploadSuccess(e)
												}}
												insertImageSettings={{
													saveUrl: `https://firebasestorage.googleapis.com/v0/b/report-images-dev/o/image${uuidv1()}`,
													width: 200,
													height: 150,
												}}
												value={companyInfoData.MESSAGE_FROM_CEO}
												change={content => {
													handleChangeContent("MESSAGE_FROM_CEO", content.value)
												}}
												toolbarSettings={toolbarSettings}
											>
												<Inject
													services={[Toolbar, Image, Link, HtmlEditor, QuickToolbar, Table]}
												/>
											</RichTextEditorComponent>
										</div>
										<div className="flex justify-end mt-2">
											<button
												onClick={() => {
													handleUpdateCompanyInfo()
													handleOpenEditor("ceomessage")
												}}
												className="bg-blue_d hover:bg-blue_d text-white font-semibold hover:text-white py-2 px-4 border border-blue hover:border-transparent rounded print:hidden"
											>
												SAVE
											</button>
										</div>
									</>
								)}
							</div>

							{/* Our climate commitment <h1 className='text-2xl font-bold print:text-3xl'>{t("Commitment")}</h1>*/}

							<div className="mx-4">
								<RichTextEditor
									html={companyInfoData.OUR_CLIMATE_COMMITMENT}
									show={companyInfoEdit.ourClimateCommitment}
									handleImageUploadSuccess={handleImageUploadSuccess}
									handleSaveProjectReport={handleUpdateCompanyInfo}
									handleChangeContent={content =>
										handleChangeContent("OUR_CLIMATE_COMMITMENT", content.value)
									}
									handleOpenEditor={() => handleOpenEditor("ourClimateCommitment")}
								/>
							</div>
						</section>

						{/* Page no 2 */}

						<section className="mt-12 py-8 px-7 print:mt-2 shadow-md print:shadow-none bg-white/50 print:break-before-page">
							<h1 className="text-2xl font-bold print:text-3xl">
								{currentOrganization?.companyName} {t("Energy")}
							</h1>
							<div className="overflow-hidden border-t-4 border-sky-600 shadow-md ring-1 ring-slate-200 sm:rounded-lg">
								<table className="min-w-full divide-y divide-slate-300">
									<thead className="bg-slate-100">
										<tr>
											<th className="px-4 py-3 text-left text-base font-semibold text-slate-900 ">
												{t("Energy source")}
											</th>
											<th className="px-6 py-3 text-right text-base font-semibold text-slate-900">
												{t("Percentage")}
											</th>
											<th className="float-right whitespace-nowrap py-3 pr-4 text-left text-base font-semibold text-slate-900">
												{t("Kilowatt hours")}
											</th>
										</tr>
									</thead>
									<tbody className="divide-y divide-slate-200 bg-white">
										{isTotalCo2Added ? (
											energyData.map((co2, index) => {
												return (
													<tr className="table-light" key={index}>
														<td
															className="truncate py-2.5 px-4 text-base text-slate-700 "
															style={{ maxWidth: "100px" }}
														>
															{t(co2.name)}
														</td>
														<td className="px-6 py-2.5 text-right text-base text-slate-700">
															{co2?.percentage.toFixed(2) || 0} %
														</td>
														<td className="w-24 whitespace-nowrap px-4 py-2.5 text-right text-base text-slate-700">
															{parseInt(co2.value).toLocaleString()}
														</td>
													</tr>
												)
											})
										) : (
											<tr>
												<td className="custom-td" colSpan={2}>
													<Spinner type="md" />
												</td>
											</tr>
										)}
									</tbody>
									<tfoot>
										<tr className="border-t-2 border-slate-300">
											<td className="px-4 text-left text-base font-semibold text-slate-900">
												{t("Total energy to output")}
											</td>
											<td className="px-6 py-3 text-right text-base font-semibold text-slate-900">
												100.00 %
											</td>
											<td className="py-3 pr-4 text-right text-base font-semibold text-slate-900">
												{parseInt(totalEnergy).toLocaleString()}
											</td>
										</tr>
									</tfoot>
								</table>
							</div>
							<div className="grid justify-items-stretch break-inside-avoid">
								<div id="scope-chart-details" className="w-1/2 justify-self-center mt-5">
									<Flex alignItems="center" flexDirection="row">
										<PieChart
											data={energyChartValues}
											t={t}
											colors={["stone", "rose", "teal", "amber", "lime"]}
										/>
									</Flex>
								</div>
							</div>

							<RichTextEditor
								html={companyInfoData.ENERGY}
								show={companyInfoEdit.energy}
								handleImageUploadSuccess={handleImageUploadSuccess}
								handleSaveProjectReport={handleUpdateCompanyInfo}
								handleChangeContent={content => handleChangeContent("ENERGY", content.value)}
								handleOpenEditor={() => handleOpenEditor("energy")}
							/>
						</section>

						{/* Page 3  */}
						<section className="mt-12 py-8 px-7 print:mt-2 shadow-md print:shadow-none bg-white/50 print:break-before-page">
							<h1 className="text-2xl font-bold print:text-3xl">
								{currentOrganization?.companyName} {t("Total")} {reportingYear}
							</h1>
							<PrimaryDataMetric
								getPrimaryDataColors={getPrimaryDataColors}
								value={totalPrimaryScope}
								t={t}
							/>
							<div className="grid grid-cols-2 gap-4 justify-items-stretch mt-4">
								<Card className="!py-3">
									<Flex alignItems="" flexDirection="col">
										<div className="text-slate-600 font-bold text-md">{t("Location")}</div>
										<Flex
											justifyContent="start"
											alignItems="baseline"
											flexDirection="col"
											className="space-x-1 truncate gap-3"
										>
											{/* as dwonstream emission is also included in scope 3 emission so 
											have to subtract it from scope 3 emission */}
											<div>
												<Metric>
													{Intl.NumberFormat(i18n.language, {
														maximumFractionDigits: 2,
													}).format(
														parseFloat(
															scopeDataLocationBased.Scope_1 +
																scopeDataLocationBased.Scope_2 +
																(scopeDataLocationBased.Scope_3 - (downStreamCatagoryEmission || 0))
														).toFixed(2) || 0
													)}
													<span className="text-slate-500 text-sm"> tCo2e</span>
												</Metric>
											</div>
											<Flex alignItems="start" flexDirection="col">
												<Flex alignItems="center" flexDirection="row">
													<PieChart
														data={{
															"Scope 1": scopeDataLocationBased?.Scope_1,
															"Scope 2": scopeDataLocationBased?.Scope_2,
															"Scope 3":
																scopeDataLocationBased.Scope_3 - (downStreamCatagoryEmission || 0),
														}}
														showLengend={true}
														t={t}
													/>
												</Flex>
											</Flex>

											{downStreamCatagoryEmission ? (
												<div>
													<span className="mr-2 text-sm font-bold text-slate-500">
														{t("Including_dwonstream")}
													</span>
													<Metric>
														{Intl.NumberFormat(i18n.language, {
															maximumFractionDigits: 2,
														}).format(
															parseFloat(
																scopeDataLocationBased.Scope_1 +
																	scopeDataLocationBased.Scope_2 +
																	scopeDataLocationBased.Scope_3
															).toFixed(2) || 0
														)}
														<span className="text-slate-500 text-sm"> tCo2e</span>
													</Metric>
												</div>
											) : (
												<div></div>
											)}
										</Flex>
									</Flex>
								</Card>
								<Card className="!py-3">
									<Flex alignItems="" flexDirection="col">
										<div className="text-slate-600 font-bold text-md">{t("Market")}</div>

										<Flex
											justifyContent="start"
											alignItems="baseline"
											flexDirection="col"
											className="space-x-1 truncate gap-3"
										>
											<div>
												<Metric>
													{Intl.NumberFormat(i18n.language, {
														maximumFractionDigits: 2,
													}).format(
														parseFloat(
															scopeData.Scope_1 +
																scopeData.Scope_2 +
																(scopeData.Scope_3 - (downStreamCatagoryEmission || 0))
														).toFixed(2) || 0
													)}
													<span className="text-slate-500 text-sm"> tCo2e</span>
												</Metric>
											</div>

											<Flex alignItems="start" className="self-center" flexDirection="col">
												<Flex alignItems="center" flexDirection="row">
													<PieChart
														data={{
															"Scope 1": scopeData?.Scope_1,
															"Scope 2": scopeData?.Scope_2,
															"Scope 3": scopeData.Scope_3 - (downStreamCatagoryEmission || 0),
														}}
														showLengend={true}
														t={t}
													/>
												</Flex>
											</Flex>

											{downStreamCatagoryEmission ? (
												<div>
													<span className="mr-2 text-sm font-bold text-slate-500">
														{t("Including_dwonstream")}
													</span>
													<Metric>
														{Intl.NumberFormat(i18n.language, {
															maximumFractionDigits: 2,
														}).format(
															parseFloat(
																scopeData.Scope_1 + scopeData.Scope_2 + scopeData.Scope_3
															).toFixed(2)
														) || 0}
														<span className="text-slate-500 text-sm"> tCo2e</span>
													</Metric>
												</div>
											) : (
												<div></div>
											)}
										</Flex>
									</Flex>
								</Card>
							</div>

							<RichTextEditor
								html={companyInfoData.TOTAL_CEO2_EMISSION}
								show={companyInfoEdit.totalco2emission}
								handleImageUploadSuccess={handleImageUploadSuccess}
								handleSaveProjectReport={handleUpdateCompanyInfo}
								handleChangeContent={content =>
									handleChangeContent("TOTAL_CEO2_EMISSION", content.value)
								}
								handleOpenEditor={() => handleOpenEditor("totalco2emission")}
							/>

							{/* years chart */}

							<div className="mt-2 flex justify-between break-inside-avoid">
								<div className="w-2/3">
									<BarChartComponent
										language={i18n.language === "en" ? "en" : "no-NO"}
										title={"Monthly chart data"}
										showLegend={false}
										height="240px"
										showCustomLegend={true}
										Data={monthlyChartData?.data?.EmissionData}
									/>
								</div>

								<div className="ml-2 w-1/3">
									<BarChartComponent
										showLegend={false}
										language={i18n.language === "en" ? "en" : "no-NO"}
										title={"Yearly chart data"}
										index="_id"
										Data={yearlyData}
									/>
								</div>
							</div>
						</section>

						{/* Page 4  */}

						<section className="mt-12 py-8 px-7 print:mt-2 shadow-md print:shadow-none bg-white/50 print:break-before-page">
							<div className="border-b-2 border-darkBlack break-inside-avoid">
								<div className="text-2xl font-bold print:text-3xl mb-5 mt-2">
									{t("report:Scope1")}
									<PrimaryDataMetric value={scopePrimaryData?.Scope_1 || 0} t={t} />
								</div>

								<Flex
									flexDirection="row"
									className="gap-2"
									justifyContent="space-between"
									alignItems="center"
								>
									<ScopeMetricCard
										title={t("report:Market_based")}
										total={scopeData.total}
										colors={["rose", "slate"]}
										value={scopeData.Scope_1 || 0}
									/>

									<ScopeMetricCard
										title={t("report:Location_based")}
										showValue={false}
										total={scopeData.Scope_1 + scopeDataLocationBased.Scope_2 + scopeData.Scope_3}
										colors={["rose", "slate"]}
										value={scopeData.Scope_1 || 0}
									/>
								</Flex>

								<RichTextEditor
									html={companyInfoData.SCOPE1_EMISSION}
									show={companyInfoEdit.scope1emission}
									handleImageUploadSuccess={handleImageUploadSuccess}
									handleSaveProjectReport={handleUpdateCompanyInfo}
									handleChangeContent={content =>
										handleChangeContent("SCOPE1_EMISSION", content.value)
									}
									handleOpenEditor={() => handleOpenEditor("scope1emission")}
								/>
							</div>

							{/* scope 2 emisson */}

							<div className="border-b-2 border-darkBlack break-inside-avoid">
								<div className="text-2xl font-bold print:text-3xl mb-5 mt-2">
									{t("report:Scope2")}
									<PrimaryDataMetric value={scopePrimaryData?.Scope_2 || 0} t={t} />
								</div>

								<Flex
									flexDirection="row"
									className="gap-2"
									justifyContent="space-between"
									alignItems="center"
								>
									<ScopeMetricCard
										title={t("report:Market_based")}
										total={scopeData.total}
										colors={["rose", "slate"]}
										value={scope2Data.marketBased || 0}
									/>

									<ScopeMetricCard
										title={t("report:Location_based")}
										showValue={true}
										total={scopeData.Scope_2 + scopeDataLocationBased.Scope_2 + scopeData.Scope_3}
										colors={["rose", "slate"]}
										value={scope2Data.locationBased || 0}
									/>
								</Flex>

								<RichTextEditor
									html={companyInfoData.SCOPE2_EMISSION}
									show={companyInfoEdit.scope2emission}
									handleImageUploadSuccess={handleImageUploadSuccess}
									handleSaveProjectReport={handleUpdateCompanyInfo}
									handleChangeContent={content =>
										handleChangeContent("SCOPE2_EMISSION", content.value)
									}
									handleOpenEditor={() => handleOpenEditor("scope2emssion")}
								/>
							</div>

							{/* scope 3 emisson */}

							<div className="break-inside-avoid">
								<div className="text-2xl font-bold print:text-3xl mb-5 mt-2">
									{t("report:Scope3")}
									<PrimaryDataMetric value={scopePrimaryData?.Scope_3 || 0} t={t} />
								</div>

								<Flex
									flexDirection="row"
									className="gap-2"
									justifyContent="space-between"
									alignItems="center"
								>
									<ScopeMetricCard
										title={t("report:Market_based")}
										total={scopeData.total}
										colors={["rose", "slate"]}
										value={scopeData.Scope_3 || 0}
									/>

									<ScopeMetricCard
										title={t("report:Location_based")}
										showValue={false}
										total={scopeData.Scope_1 + scopeDataLocationBased.Scope_2 + scopeData.Scope_3}
										colors={["rose", "slate"]}
										value={scopeData.Scope_3 || 0}
									/>
								</Flex>

								<RichTextEditor
									html={companyInfoData.SCOPE3_EMISSION}
									show={companyInfoEdit.scope3emission}
									handleImageUploadSuccess={handleImageUploadSuccess}
									handleSaveProjectReport={handleUpdateCompanyInfo}
									handleChangeContent={content =>
										handleChangeContent("SCOPE3_EMISSION", content.value)
									}
									handleOpenEditor={() => handleOpenEditor("scope3emission")}
								/>
							</div>
						</section>

						{/* Page 5  */}

						<section className="mt-12 py-8 px-7 print:mt-2 shadow-md print:shadow-none bg-white/50 print:break-before-page">
							<h1 className="mt-10 text-2xl font-bold print:text-3xl">
								{currentOrganization?.companyName} {t("Suppliers")}
							</h1>
							<div className="overflow-hidden border-t-4 border-sky-600 shadow-md ring-1 ring-slate-200 sm:rounded-lg">
								<table className="min-w-full divide-y divide-slate-300">
									<thead className="bg-slate-100">
										<tr>
											<th className="py-2 px-4 text-left text-base font-semibold text-slate-900 ">
												{t("Supplier Name")}
											</th>
											<th className="px-4 py-2 text-left text-base font-semibold text-slate-900">
												{t("Emissions")}
												<span className="text-slate-500"> tCo2e</span>
											</th>
										</tr>
									</thead>
									<tbody className="divide-y divide-slate-200 bg-white">
										{!top_Suppliers.isLoading ? (
											topSuppliers.map((supplier, index) => {
												return (
													<tr key={index}>
														<td
															className="text-base text-slate-700 truncate py-2.5 px-4"
															style={{ maxWidth: "100px" }}
														>
															{supplier.SupplierName}
														</td>
														<td className="w-24 whitespace-nowrap px-4 py-2.5 text-right text-base text-slate-700">
															{parseFloat(supplier.Emission).toFixed(2)}
														</td>
													</tr>
												)
											})
										) : (
											<tr>
												<td className="custom-td" colSpan={2}>
													<Spinner type="md" />
												</td>
											</tr>
										)}
									</tbody>
								</table>
							</div>

							<RichTextEditor
								html={companyInfoData.suppliersInfo}
								show={companyInfoEdit.suppliers}
								handleImageUploadSuccess={handleImageUploadSuccess}
								handleSaveProjectReport={handleUpdateCompanyInfo}
								handleChangeContent={content => handleChangeContent("suppliersInfo", content.value)}
								handleOpenEditor={() => handleOpenEditor("suppliers")}
							/>
						</section>

						{/* Categories details Page 6*/}

						<section className="mt-12 py-8 px-7 print:mt-2 shadow-md print:shadow-none bg-white/50 print:break-before-page">
							<div className="print:break-inside-avoid-page">
								<div className="mt-10 border-2 border-c_border px-2 py-2 rounded-md">
									<div className="text-xl font-bold print:text-2xl">{t("Cat1")}</div>
									<div className="flex justify-between items-center">
										<div className="text-lg font-bold print:text-2xl">
											{parseFloat(scopeCategoryData[0]?.Emission || 0).toFixed(2)} tCo2e
											<PrimaryDataMetric
												getPrimaryDataColors={getPrimaryDataColors}
												value={scopeCategoryPrimaryData[0]?.Percentage || 0}
												t={t}
											/>
										</div>

										<p className="text-white text-md bg-C1 !px-2 py-1 rounded-md">
											{parseFloat(scopeCategoryData[0]?.Percentage || 0).toFixed(2)} %
										</p>
									</div>
								</div>

								<RichTextEditor
									html={companyInfoData.c1_goods_and_services}
									show={companyInfoEdit.c1}
									handleImageUploadSuccess={handleImageUploadSuccess}
									handleSaveProjectReport={handleUpdateCompanyInfo}
									handleChangeContent={content =>
										handleChangeContent("c1_goods_and_services", content.value)
									}
									handleOpenEditor={() => handleOpenEditor("c1")}
								/>
							</div>
							<div className="print:break-inside-avoid-page">
								<div className="mt-16 border-2 border-c_border px-2 py-2 rounded-md">
									<h1 className="text-xl font-bold print:text-2xl"> {t("Cat2")} </h1>
									<div className="flex justify-between items-center">
										<div>
											<h1 className="text-lg font-bold print:text-2xl">
												{parseFloat(scopeCategoryData[1]?.Emission || 0).toFixed(2)} tCo2e
											</h1>
											<PrimaryDataMetric
												getPrimaryDataColors={getPrimaryDataColors}
												value={scopeCategoryPrimaryData[1]?.Percentage || 0}
												t={t}
											/>
										</div>

										<p className="text-white text-md bg-C2 !px-2 py-1 rounded-md">
											{parseFloat(scopeCategoryData[1]?.Percentage || 0).toFixed(2)}%
										</p>
									</div>
								</div>

								<RichTextEditor
									html={companyInfoData.c2_capital_goods}
									show={companyInfoEdit.c2}
									handleImageUploadSuccess={handleImageUploadSuccess}
									handleSaveProjectReport={handleUpdateCompanyInfo}
									handleChangeContent={content =>
										handleChangeContent("c2_capital_goods", content.value)
									}
									handleOpenEditor={() => handleOpenEditor("c2")}
								/>
							</div>
							<div className="print:break-inside-avoid-page">
								<div className="mt-10 border-2 border-c_border px-2 py-2 rounded-md">
									<h1 className="text-xl font-bold print:text-2xl">{t("Cat3")}</h1>
									<div className="flex justify-between items-center">
										<div>
											<h1 className="text-lg font-bold print:text-2xl">
												{parseFloat(scopeCategoryData[2]?.Emission || 0).toFixed(2)} tCo2e
											</h1>
											<PrimaryDataMetric
												getPrimaryDataColors={getPrimaryDataColors}
												value={scopeCategoryPrimaryData[2]?.Percentage || 0}
												t={t}
											/>
										</div>

										<p className="text-white text-md bg-C3 !px-2 py-1 rounded-md">
											{parseFloat(scopeCategoryData[2]?.Percentage || 0).toFixed(2)}%
										</p>
									</div>
								</div>

								<RichTextEditor
									html={companyInfoData.c3_feul_and_energy}
									show={companyInfoEdit.c3}
									handleImageUploadSuccess={handleImageUploadSuccess}
									handleSaveProjectReport={handleUpdateCompanyInfo}
									handleChangeContent={content =>
										handleChangeContent("c3_feul_and_energy", content.value)
									}
									handleOpenEditor={() => handleOpenEditor("c3")}
								/>
							</div>

							<div className="print:break-inside-avoid-page">
								<div className="mt-10 border-2 border-c_border px-2 py-2 rounded-md">
									<h1 className="text-xl font-bold print:text-2xl">{t("Cat4")}</h1>
									<div className="flex justify-between items-center">
										<div>
											<h1 className="text-lg font-bold print:text-2xl">
												{parseFloat(scopeCategoryData[3]?.Emission || 0).toFixed(2)} tCo2e
											</h1>
											<PrimaryDataMetric
												getPrimaryDataColors={getPrimaryDataColors}
												value={scopeCategoryPrimaryData[3]?.Percentage || 0}
												t={t}
											/>
										</div>

										<p className="text-white text-md bg-C4 !px-2 py-1 rounded-md">
											{parseFloat(scopeCategoryData[3]?.Percentage || 0).toFixed(2)}%
										</p>
									</div>
								</div>

								<RichTextEditor
									html={companyInfoData.c4_upstream_transformation}
									show={companyInfoEdit.c4}
									handleImageUploadSuccess={handleImageUploadSuccess}
									handleSaveProjectReport={handleUpdateCompanyInfo}
									handleChangeContent={content =>
										handleChangeContent("c4_upstream_transformation", content.value)
									}
									handleOpenEditor={() => handleOpenEditor("c4")}
								/>
							</div>
							<div className="print:break-inside-avoid-page">
								<div className="mt-10 border-2 border-c_border px-2 py-2 rounded-md">
									<h1 className="text-xl font-bold print:text-2xl">{t("Cat5")}</h1>
									<div className="flex justify-between items-center">
										<div>
											<h1 className="text-lg font-bold print:text-2xl">
												{parseFloat(scopeCategoryData[4]?.Emission || 0).toFixed(2)} tCo2e
											</h1>
											<PrimaryDataMetric
												getPrimaryDataColors={getPrimaryDataColors}
												value={scopeCategoryPrimaryData[4]?.Percentage || 0}
												t={t}
											/>
										</div>
										<p className="text-white text-md bg-C5 !px-2 py-1 rounded-md">
											{parseFloat(scopeCategoryData[4]?.Percentage || 0).toFixed(2)}%
										</p>
									</div>
								</div>

								<RichTextEditor
									html={companyInfoData.c5_waste_generation}
									show={companyInfoEdit.c5}
									handleImageUploadSuccess={handleImageUploadSuccess}
									handleSaveProjectReport={handleUpdateCompanyInfo}
									handleChangeContent={content =>
										handleChangeContent("c5_waste_generation", content.value)
									}
									handleOpenEditor={() => handleOpenEditor("c5")}
								/>
							</div>
							<div className="print:break-inside-avoid-page">
								<div className="mt-10 border-2 border-c_border px-2 py-2 rounded-md">
									<div className="text-xl font-bold print:text-2xl">{t("Cat6")}</div>
									<div className="flex justify-between items-center">
										<div>
											<h1 className="text-lg font-bold print:text-2xl">
												{parseFloat(scopeCategoryData[5]?.Emission || 0).toFixed(2)} tCo2e
											</h1>
											<PrimaryDataMetric
												getPrimaryDataColors={getPrimaryDataColors}
												value={scopeCategoryPrimaryData[5]?.Percentage || 0}
												t={t}
											/>
										</div>

										<p className="text-white text-md bg-C6 !px-2 py-1 rounded-md">
											{parseFloat(scopeCategoryData[5]?.Percentage || 0).toFixed(2)} %
										</p>
									</div>
								</div>

								<RichTextEditor
									html={companyInfoData.c6_business_travel}
									show={companyInfoEdit.c6}
									handleImageUploadSuccess={handleImageUploadSuccess}
									handleSaveProjectReport={handleUpdateCompanyInfo}
									handleChangeContent={content =>
										handleChangeContent("c6_business_travel", content.value)
									}
									handleOpenEditor={() => handleOpenEditor("c6")}
								/>
							</div>
							<div className="print:break-inside-avoid-page">
								<div className="mt-10 border-2 border-c_border px-2 py-2 rounded-md">
									<div className="text-xl font-bold print:text-2xl">{t("Cat7")}</div>
									<div className="flex justify-between items-center">
										<div>
											<h1 className="text-lg font-bold print:text-2xl">
												{parseFloat(scopeCategoryData[6]?.Emission || 0).toFixed(2)} tCo2e
											</h1>
											<PrimaryDataMetric
												getPrimaryDataColors={getPrimaryDataColors}
												value={scopeCategoryPrimaryData[6]?.Percentage || 0}
												t={t}
											/>
										</div>

										<p className="text-white text-md bg-C7 !px-2 py-1 rounded-md">
											{parseFloat(scopeCategoryData[6]?.Percentage || 0).toFixed(2)} %
										</p>
									</div>
								</div>

								<RichTextEditor
									html={companyInfoData.c7_employee_commitee}
									show={companyInfoEdit.c7}
									handleImageUploadSuccess={handleImageUploadSuccess}
									handleSaveProjectReport={handleUpdateCompanyInfo}
									handleChangeContent={content =>
										handleChangeContent("c7_employee_commitee", content.value)
									}
									handleOpenEditor={() => handleOpenEditor("c7")}
								/>
							</div>
							<div className="print:break-inside-avoid-page">
								<div className="mt-10 border-2 border-c_border px-2 py-2 rounded-md">
									<div className="text-xl font-bold print:text-2xl">{t("Cat8")}</div>
									<div className="flex justify-between items-center">
										<div>
											<h1 className="text-lg font-bold print:text-2xl">
												{parseFloat(scopeCategoryData[7]?.Emission || 0).toFixed(2)} tCo2e
											</h1>
											<PrimaryDataMetric
												getPrimaryDataColors={getPrimaryDataColors}
												value={scopeCategoryPrimaryData[7]?.Percentage || 0}
												t={t}
											/>
										</div>

										<p className="text-white text-md bg-C8 !px-2 py-1 rounded-md">
											{parseFloat(scopeCategoryData[7]?.Percentage || 0).toFixed(2)} %
										</p>
									</div>
								</div>

								<RichTextEditor
									html={companyInfoData.c8_upstream_leased_assets}
									show={companyInfoEdit.c8}
									handleImageUploadSuccess={handleImageUploadSuccess}
									handleSaveProjectReport={handleUpdateCompanyInfo}
									handleChangeContent={content =>
										handleChangeContent("c8_upstream_leased_assets", content.value)
									}
									handleOpenEditor={() => handleOpenEditor("c8")}
								/>
							</div>
							<div className="print:break-inside-avoid-page">
								<div className="mt-10 border-2 border-c_border px-2 py-2 rounded-md">
									<div className="text-xl font-bold print:text-2xl">{t("Category9_15")}</div>
									<div className="flex justify-between items-center">
										<div>
											<h1 className="text-lg font-bold print:text-2xl">
												{parseFloat(downStreamCatagoryEmission || 0).toFixed(2)} tCo2e
											</h1>
										</div>
									</div>
								</div>

								<RichTextEditor
									html={companyInfoData.c9_15_downstream_emissions}
									show={companyInfoEdit.c9}
									handleImageUploadSuccess={handleImageUploadSuccess}
									handleSaveProjectReport={handleUpdateCompanyInfo}
									handleChangeContent={content =>
										handleChangeContent("c9_15_downstream_emissions", content.value)
									}
									handleOpenEditor={() => handleOpenEditor("c9")}
								/>
							</div>
						</section>

						{/* Page 7  */}

						<section className="mt-12 py-8 px-7 print:mt-2 shadow-md print:shadow-none bg-white/50 print:break-before-page">
							<div>
								<h1 className="font-bold print:text-2xl">{t("Methodology")}</h1>

								<RichTextEditor
									html={companyInfoData.Metodikk}
									show={companyInfoEdit.Metodikk}
									handleImageUploadSuccess={handleImageUploadSuccess}
									handleSaveProjectReport={handleUpdateCompanyInfo}
									handleChangeContent={content => handleChangeContent("Metodikk", content.value)}
									handleOpenEditor={() => handleOpenEditor("Metodikk")}
								/>
							</div>
						</section>

						{/* Page 8  */}
						<section className="mt-12 py-8 px-7 print:mt-2 shadow-md print:shadow-none bg-white/50 print:break-before-page">
							<div>
								<h1 className="font-bold print:text-2xl">{t("References")}</h1>

								<RichTextEditor
									html={companyInfoData.Referanser}
									show={companyInfoEdit.refer}
									handleImageUploadSuccess={handleImageUploadSuccess}
									handleSaveProjectReport={handleUpdateCompanyInfo}
									handleChangeContent={content => handleChangeContent("Referanser", content.value)}
									handleOpenEditor={() => handleOpenEditor("refer")}
								/>
							</div>
						</section>
						{loader}
					</div>
				)}
			</div>
		)
	}
}

export default Report
