import { Don<PERSON><PERSON><PERSON>, <PERSON> } from "@tremor/react"
import React from "react"

export default function Pie<PERSON><PERSON>({
	data = {},
	total = 0,
	variant = "pie",
	type = "percent",
	language = "no-NO",
	showTooltip = true,
	className = "h-28 w-28",
	colors = ["rose", "sky", "amber"],
	t = () => {},
	showLengend = true,
}) {
	const valueFormat = number => {
		return `${new Intl.NumberFormat(language, {
			maximumFractionDigits: 2,
		})
			.format(number)
			.toString()} %`
	}

	const getPercentage = (value, total) => {
		return (value / total) * 100
	}

	const getChartDataFromList = () => {
		try {
			const keys = Object.keys(data)

			const total = Object.values(data).reduce((a, b) => a + b, 0)

			const chartData = keys.map(key => {
				if (type === "percent") {
					if (data[key] > 0 && total > 0) {
						return {
							name: key,
							value: getPercentage(data[key], total),
						}
					} else return [{ name: "", value: 0 }]
				} else {
					if (data[key] > 0) {
						return {
							name: key,
							value: data[key],
						}
					} else return [{ name: "", value: 0 }]
				}
			})

			const filterData = chartData.filter(data => data.value > 0)

			if (filterData.length > 0) {
				return chartData
			}
			return []
		} catch (error) {}
	}

	const getChartData = () => {
		try {
			if (typeof data === "object") {
				return getChartDataFromList()
			}

			if (data > 0 && total > 0) {
				const percentage = getPercentage(data, total)
				return [{ value: percentage }, { value: 100 - percentage }]
			}
			return []
		} catch (error) {}
	}

	const getLegendData = () => {
		const keys = Object.keys(data)

		const legendData = keys.map(key => {
			return `${t(key)}`
		})

		return legendData
	}

	const customTooltip = ({ payload, active }) => {
		if (!active || !payload) return null
		const categoryPayload = payload?.[0]
		if (!categoryPayload) return null
		return (
			<div className="w-24 rounded-tremor-default text-tremor-default bg-tremor-background p-2 shadow-tremor-dropdown border border-tremor-border">
				<div className="flex flex-1 space-x-2.5">
					<div className={`w-1.5 flex flex-col bg-${categoryPayload?.color}-500 rounded`} />
					<div className="w-full">
						<div className="flex items-center justify-between space-x-8">
							<p className="font-medium text-right whitespace-nowrap text-tremor-content-emphasis !m-0 !p-0">
								{valueFormat(categoryPayload.value, language)}
							</p>
						</div>
					</div>
				</div>
			</div>
		)
	}

	const getLabel = (data, total, type) => {
		if (type === "percent") {
			return valueFormat(getPercentage(data, total))
		} else {
			return Number(total).toFixed(2)
		}
	}

	return (
		<>
			<DonutChart
				className={variant === "pie" ? `"mt-2 w-full z-10 print:h-56 print:w-56"` : `${className}`}
				data={getChartData(data, total)}
				category="value"
				variant={variant}
				label={getLabel(data, total, type)}
				valueFormatter={valueFormat}
				showTooltip={showTooltip}
				customTooltip={customTooltip}
				colors={colors}
				showAnimation
			/>

			{showLengend && (
				<Legend
					className="mt-3 p-0 my-custom-legend w-full min-w-fit"
					categories={getLegendData()}
					colors={colors}
				/>
			)}
		</>
	)
}
