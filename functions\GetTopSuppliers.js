exports = async function (input) {
	try {
		const res = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("transaction")
			.aggregate([
				{
					$match: { RegistrationNumber: input.RegistrationNumber, PeriodYear: input.year },
				},
				{
					$group: {
						_id: "$SupplierID",
						Emission: { $sum: { $add: ["$Scope_1", "$Scope_2", "$Scope_3"] } },
					},
				},
				{
					$sort: {
						Emission: -1,
					},
				},
				{
					$limit: 6,
				},
			])
			.toArray()
		
		let topSuppliers = []
		let topSuppliersIds = []

		for (let i = 0; i < res.length && topSuppliers.length < 5; i++) {
			if (!res[i]?._id || res[i]._id == "") continue
			topSuppliers.push({
				SupplierID: res[i]._id,
				Emission: res[i].Emission,
			})
			topSuppliersIds.push(res[i]._id)
		}

		let supplierNames = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("company")
			.aggregate([
				{
					$match: { RegistrationNumber: input.RegistrationNumber },
				},
				{
					$unwind: "$Suppliers",
				},
				{
					$match: { "Suppliers.SupplierID": { $in: topSuppliersIds } },
				},
				{
					$project: {
						"Suppliers.SupplierID": 1,
						"Suppliers.Name": 1,
					},
				},
			])
			.toArray()
		let supplierNamesObj = {}
		for (let i = 0; i < supplierNames.length; i++) {
			supplierNamesObj[supplierNames[i].Suppliers.SupplierID] = supplierNames[i].Suppliers.Name
		}
		for (let i = 0; i < topSuppliers.length; i++) {
			topSuppliers[i].SupplierName = supplierNamesObj[topSuppliers[i].SupplierID]
		}

		return { topSuppliers: topSuppliers, success: true }
	} catch (err) {
		console.error(err)
	}
	return { success: false }
}
