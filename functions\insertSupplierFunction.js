const getNaicsCodes = async () => {
  const naicsCodes = await context.services
    .get("mongodb-atlas")
    .db(context.environment.values.database)
    .collection("nace-naics")
    .find({})
    .toArray()
  let naicsRecords = {}
  naicsCodes.forEach((rec) => {
    naicsRecords[rec.US_NAICS_2017] = rec.co2Intensity["2021"][0]
  })
  return naicsRecords
}

exports = async function (changeEvent) {
  const company = changeEvent.fullDocument
  if (!company) {
    console.error("company document not found")
    return
  }

  const naicsRecords = await getNaicsCodes()
 // const NACE_TO_NAICS = context.values.get("nace_to_naics")

  // Insert NaceCode and Industry in supplier data.
  // instead of getting naices values from db we putt all those
  // values into mongodb realm values and use them here from there
  // we are getting all the naics codes
  for (let i = 0; i < company.Suppliers.length; i++) {
    // functionality to fetch the IndustryC02Intencity
    try {
      // get suppliers naics code and on the base that get co2Intensity from records that we get back from
      // function getNaicsCodes
      let naicsCode = company.Suppliers[i].NaicsCode
      if (naicsCode in naicsRecords) {
        company.Suppliers[i]["IndustryCo2Intensity"] = naicsRecords[naicsCode]
        company.Suppliers[i]["SupplierCo2Intensity"] = naicsRecords[naicsCode]
      }
    } catch (err) {
      console.error(err)

      // Set the SupplierCo2Intensity and IndustryCo2Intensity = 0
      company.Suppliers[i].SupplierCo2Intensity = 0
      company.Suppliers[i].IndustryCo2Intensity = 0
    }

    if (!company.Suppliers[i].Address) {
      company.Suppliers[i].Address = []
    }

    for (let j = 0; j < company.Suppliers[i].Address.length; j++) {
      if (
        company.Suppliers[i].Address[j].StreetName == null ||
        !company.Suppliers[i].Address[j].StreetName
      ) {
        company.Suppliers[i].Address[j].StreetName = ""
      }
      if (company.Suppliers[i].Address[j].City == null || !company.Suppliers[i].Address[j].City) {
        company.Suppliers[i].Address[j].City = ""
      }
      if (
        company.Suppliers[i].Address[j].PostalCode == null ||
        !company.Suppliers[i].Address[j].PostalCode
      ) {
        company.Suppliers[i].Address[j].PostalCode = ""
      }
      if (
        company.Suppliers[i].Address[j].AddressType == null ||
        !company.Suppliers[i].Address[j].AddressType
      ) {
        company.Suppliers[i].Address[j].AddressType = ""
      }
    }
  }
  // Update flag in company collection to be true
  //
  // company.Updated = true
  try {
    const request = context.services
      .get("mongodb-atlas")
      .db(context.environment.values.DB)
      .collection("company")
    const result = await request.updateOne(
      {
        _id: company._id,
      },
      {
        $set: company,
      }
    )
  } catch (err) {
    console.error("Error while saving company to database: ", err)
  }

  return company
}
