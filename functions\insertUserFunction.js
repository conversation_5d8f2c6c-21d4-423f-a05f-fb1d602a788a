/*
  This function is run when a GraphQL Query is made requesting your
  custom field name. The return value of this function is used to
  populate the resolver generated from your Payload Type.

  This function expects the following input object:

  {
    "type": "object",
    "title": "MyCustomResolverInput",
    "properties": {
      "name": {
        "type": "string"
      }
    },
    "required": ["name"]
  }

  And the following payload object:

  {
    "type": "object",
    "title": "MyCustomResolverResult",
    "properties": {
      "hello": {
        "type": "string"
      }
    }
  }
*/

exports = async (input) => {
  try {
    const request = context.services
      .get("mongodb-atlas")
      .db(context.environment.values.database)
      .collection("user");
    const result = await request.insertOne({
      name: input.name || "",
      email: input.email || "",
      created_at: input.created_at,
      app_metadata: {
        provider: input.app_metadata.provider || "",
      },
      netlifyID: input.netlifyID || "",
      role: input.role || "",
      user_metadata: {
        avatarurl: input.user_metadata.avatarurl || "",
        full_name: input.user_metadata.full_name || "",
      },
      "saf-t_files": [],
    });
  } catch (err) {
    console.log(err);
    return { status: "error" };
  }
  return { status: "done" };
};
