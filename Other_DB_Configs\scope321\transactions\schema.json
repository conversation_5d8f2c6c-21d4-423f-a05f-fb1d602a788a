{"properties": {"AccountDescription": {"bsonType": "string"}, "AccountID": {"bsonType": "string"}, "AccountIDInRange": {"bsonType": "bool"}, "Amount": {"bsonType": "int"}, "Auto": {"bsonType": "int"}, "Description": {"bsonType": "array", "items": {"bsonType": "string"}}, "DescriptionDetails": {"bsonType": "string"}, "Flag": {"bsonType": "int"}, "Goods-type": {"bsonType": "string"}, "IsSplittedTransaction": {"bsonType": "bool"}, "Lines": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"AccountID": {"bsonType": "string"}, "CreditAmount": {"bsonType": "object", "properties": {"Amount": {"bsonType": "string"}, "CurrencyAmount": {"bsonType": "string"}, "CurrencyCode": {"bsonType": "string"}, "ExchangeRate": {"bsonType": "string"}}}, "DebitAmount": {"bsonType": "object", "properties": {"Amount": {"bsonType": "string"}, "CurrencyAmount": {"bsonType": "string"}, "CurrencyCode": {"bsonType": "string"}, "ExchangeRate": {"bsonType": "string"}}}, "Description": {"bsonType": "string"}, "NaceCode": {"bsonType": "string"}, "RecordID": {"bsonType": "string"}, "ReferenceNumber": {"bsonType": "string"}, "Status": {"bsonType": "string"}, "SupplierID": {"bsonType": "string"}}}}, "NaceCode": {"bsonType": "string"}, "Notes": {"bsonType": "string"}, "Period": {"bsonType": "string"}, "PeriodYear": {"bsonType": "string"}, "Production-related": {"bsonType": "bool"}, "ReferenceNumber": {"bsonType": "string"}, "RegistrationNumber": {"bsonType": "string"}, "Relation": {"bsonType": "string"}, "Scope": {"bsonType": "string"}, "Scope3_category": {"bsonType": "int"}, "Scope_1": {"bsonType": "double"}, "Scope_2": {"bsonType": "double"}, "Scope_3": {"bsonType": "double"}, "Scope_3_Category": {"bsonType": "string"}, "Status": {"bsonType": "int"}, "SupplierID": {"bsonType": "string"}, "SupplierName": {"bsonType": "string"}, "SystemID": {"bsonType": "string"}, "TransactionDate": {"bsonType": "string"}, "TransactionID": {"bsonType": "string"}, "Type": {"bsonType": "string"}, "_id": {"bsonType": "objectId"}, "liter": {"bsonType": "double"}}, "title": "transaction"}