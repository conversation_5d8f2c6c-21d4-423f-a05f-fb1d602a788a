exports = async (input) => {
	// Get transactions from database.
	let transactions = []

	try {
		const { RegistrationNumber, year } = input

		let queryInput = { RegistrationNumber: RegistrationNumber, PeriodYear: year }
		if (input && input.Scope) {
			queryInput.Scope = input.Scope
		}
		transactions = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("transaction")
			.find(queryInput)
			.toArray()

		// get supplier factor on base of year

		// const transactionsWithSupplierFactor = transactions.map((transaction) => {
		// 	let supplierFactor
		// 	if (typeof (transaction.supplierFactor)=="number") {
		// 		supplierFactor = transaction.supplierFactor
		// 	}
		// 	else {
		// 		supplierFactor = transaction.supplierFactor[year]
		// 	}
		// 	transaction['supplierFactor'] = supplierFactor
		// 	return transaction
		// })
		// return transactionsWithSupplierFactor

		return transactions
	} catch (err) {
		console.log(err)
		return []
	}

	//return transactions
}
