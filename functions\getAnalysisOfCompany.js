exports = async (args) => {
	const { RegistrationNumber, year } = args

	try {
		const company = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("company")
			.findOne({ RegistrationNumber: RegistrationNumber }, { Analysis: 1 })

		// analysis is array of objects with AnalysisIDDescription , AnalysisType and AnalysisID and more fields
		// we need AnalysisIDDescription and AnalysisID for only those analysis which are of type "P" or "A"
		// if we have type "P" put it into projects array and if we have type "A" put it into departments array

		if (company) {
			let projects = []
			let departments = []
			company.Analysis.forEach((analysis) => {
				if (analysis.AnalysisType === "P") {
					projects.push({ label: analysis.AnalysisIDDescription, value: analysis.AnalysisID })
				} else if (analysis.AnalysisType === "A") {
					departments.push({ label: analysis.AnalysisIDDescription, value: analysis.AnalysisID })
				}
			})
			return { success: true, projects, departments }
		} else {
			return { success: true, projects, departments }
		}
	} catch (error) {
		console.log(error)
		return { success: true, projects, departments }
	}
}
