{"properties": {"CompanyName": {"bsonType": "string"}, "RegistrationNumber": {"bsonType": "string"}, "_id": {"bsonType": "objectId"}, "app_metadata": {"bsonType": "object", "properties": {"provider": {"bsonType": "string"}}}, "created_at": {"bsonType": "string"}, "email": {"bsonType": "string"}, "name": {"bsonType": "string"}, "netlifyID": {"bsonType": "string"}, "role": {"bsonType": "string"}, "user_metadata": {"bsonType": "object", "properties": {"avatarurl": {"bsonType": "string"}, "full_name": {"bsonType": "string"}}}}, "title": "user"}