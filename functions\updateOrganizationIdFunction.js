exports = async (input) => {
    try {
        const company = await context.services
            .get("mongodb-atlas")
            .db(context.environment.values.database)
            .collection("company")
            .findOne({ AuthId: input.netlifyID });
        if (!company) {
            return { status: "error" };
        }
            
        const requestComapny = context.services
            .get("mongodb-atlas")
            .db(context.environment.values.database)
            .collection("company");
            
        const resultCompany = await requestComapny.updateOne({AuthId: input.netlifyID},
            {
              $set: { "OrganizationId" : input.organizationId } 
            })

        
        return { status: "done", organizationId: input.organizationId };
    } catch (error) {
        console.log(error);
        return { status: "error" };
    }
    return {};
};
