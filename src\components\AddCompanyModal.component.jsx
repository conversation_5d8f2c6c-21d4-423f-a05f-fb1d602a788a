/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useState } from "react"
import { useTranslation } from "react-i18next"
import { FiX } from "react-icons/fi"
import { useSelector, useDispatch } from "react-redux"
import { useNavigate } from "react-router-dom"
import AsyncSelect from "react-select/async"

import { useToast } from "../hooks"
import useFullPageLoader from "../pages/loader/useFullPageLoader"
import { useRealmApp } from "../realm/RealmAppProvider"
import { currentOrganizationAction, isCompanyRegistered } from "../store/actions/UserAction"

import Button from "./ui/Button"
import FormInput from "./ui/FormInput"
//import { useTranslation } from 'react-i18next'

export default function AddCompanyModal({ isOpen, setIsOpen }) {
	const { t } = useTranslation(["subscription", "common"])
	const [companyName, setCompanyName] = useState("")
	const [registrationNumber, setRegistrationNumber] = useState("")
	const [companyAlreadyExist, setCompanyAlreadyExist] = useState(false)
	const [naceCode, setNaceCode] = useState("")
	const [industry, setIndustry] = useState("")
	const [firstName, setFirstName] = useState("")
	const [lastName, setLastName] = useState("")
	const [email, setEmail] = useState("")
	const [phoneNumber, setPhoneNumber] = useState("")
	const [agree, setAgree] = useState(false)
	const user = useSelector(state => state.user)
	const app = useRealmApp()
	const dispatch = useDispatch()
	const toast = useToast()
	const [loader, showLoader, hideLoader] = useFullPageLoader()
	const navigate = useNavigate()

	const companyInputChange = inputValue => {
		return new Promise(resolve => {
			if (!inputValue.trim() || inputValue.trim().length <= 3) {
				return resolve([])
			}
			let totalElementPerRequest = 1000
			let url =
				"https://data.brreg.no/enhetsregisteret/api/enheter?navn=" +
				inputValue.trim() +
				"&size=" +
				totalElementPerRequest
			fetch(url)
				.then(response => response.json())
				.then(result => {
					if (result && result.page) {
						if (result.totalElements > totalElementPerRequest) {
							let url =
								"https://data.brreg.no/enhetsregisteret/api/enheter?navn=" +
								inputValue.trim() +
								"&size=" +
								result.totalElements
							fetch(url)
								.then(response => response.json())
								.then(result => {
									if (
										result._embedded &&
										result._embedded.enheter &&
										result._embedded.enheter.length
									) {
										let searchData = []
										for (let data of result._embedded.enheter) {
											let label = `${data.navn} (${data.organisasjonsnummer})`
											searchData.push({
												label: label,
												value: data.organisasjonsnummer,
												data: data,
											})
										}
										resolve(searchData)
									} else {
										resolve([])
									}
								})
						} else {
							if (result._embedded && result._embedded.enheter && result._embedded.enheter.length) {
								let searchData = []
								for (let data of result._embedded.enheter) {
									let label = `${data.navn} (${data.organisasjonsnummer})`
									searchData.push({
										label: label,
										value: data.organisasjonsnummer,
										data: data,
									})
								}
								resolve(searchData)
							} else {
								resolve([])
							}
						}
					} else {
						resolve([])
					}
				})
		})
	}

	const companyChange = event => {
		if (event && event.data && event.value) {
			let RegistrationNumber = event.value
			setRegistrationNumber(RegistrationNumber)
			setCompanyAlreadyExist(false)
			if (event.data.navn) {
				setCompanyName(event.data.navn)
			} else {
				setCompanyName("")
			}
			if (event.data.naeringskode1 && event.data.naeringskode1.kode) {
				setNaceCode(event.data.naeringskode1.kode)
			}
			if (event.data.naeringskode1 && event.data.naeringskode1.beskrivelse) {
				setIndustry(event.data.naeringskode1.beskrivelse)
			}
		}
	}

	const createCompany = async e => {
		try {
			e.preventDefault()
			let ContactPerson = {
				FirstName: firstName,
				LastName: lastName,
			}
			if (email !== "") {
				ContactPerson["Email"] = email
			}
			if (phoneNumber !== "") {
				ContactPerson["PhoneNumber"] = phoneNumber
			}
			const payload = {
				RegistrationNumber: registrationNumber,
				Name: companyName,
				AuthId: user?.user?.netlifyID,
				ContactPerson,
				Industry: industry,
				NaceCode: naceCode,
				Subscription: user?.user?.Subscription || 0,
				Suppliers: [],
			}
			const organization = {
				...user.currentOrganization,
				CompanyName: companyName,
				RegistrationNumber: registrationNumber,
				Industry: industry,
				NaceCode: naceCode,
				company: payload,
			}

			showLoader()
			const result = await app.createCompany(payload)
			if (result?.status === 201) {
				toast("error", t("company_already_exist"))
				setCompanyAlreadyExist(true)
			}
			if (result?.success) {
				toast("success", t("company_created"))
				dispatch(currentOrganizationAction(organization))
				setIsOpen(false)
				dispatch(isCompanyRegistered(true))
				return navigate("/dashboard")
			}
			hideLoader()

			//(result)
			//(payload, user)
		} catch (error) {
			// handle error
		}
	}

	const customStyles = {
		control: base => ({
			...base,
			boxShadow: "0 !important",
			"&:hover": {
				boxShadow: "0 !important",
			},
			"&:focus": {
				boxShadow: "0 !important",
			},
		}),
	}

	return (
		<div
			className={
				"fixed z-10 bg-black bg-opacity-50 inset-0 transform ease-in-out " +
				(isOpen
					? "transition-opacity opacity-100 duration-500 h-full translate-x-0 z-[999] overflow-y-auto"
					: "transition-all delay-500 opacity-0 translate-x-full")
			}
		>
			<div
				className={
					"p-8 max-w-sm right-0 absolute bg-white h-min min-h-full shadow-xl delay-400 duration-500 ease-in-out transition-all transform " +
					(isOpen ? "translate-x-0" : "translate-x-full")
				}
			>
				{/* <FaTimes color="red" size={30} /> */}
				<div className="flex justify-between items-center">
					<h3 className="text-lg font-semibold">{t("Create Company")}</h3>
					<FiX
						onClick={() => {
							setIsOpen(false)
						}}
						size={30}
						className="text-rose-800 cursor-pointer -mt-6"
					/>
				</div>

				<p className="mt-3">{t("Intro")}</p>

				<form
					onSubmit={e => {
						createCompany(e)
					}}
				>
					<label htmlFor="userName" className="font-bold block text-slate-800 mt-6">
						{t("CompanySearch")}
					</label>
					<AsyncSelect
						id="company-list"
						placeholder={t("placeholdersearch")}
						onChange={e => companyChange(e)}
						loadOptions={e => companyInputChange(e)}
						styles={customStyles}
					/>

					<div className=" mt-6">
						<FormInput
							title={t("common:CompanyName")}
							required
							value={companyName}
							handleChange={e => setCompanyName(e.target.value)}
						/>
					</div>
					<div className=" mt-3">
						<FormInput
							title={t("common:RegistrationNumber")}
							type="text"
							value={registrationNumber}
							error={companyAlreadyExist}
							placeholder={t("placeholderRegistration")}
							required
							handleChange={e => setRegistrationNumber(e.target.value)}
						/>
					</div>
					<div className=" mt-3">
						<FormInput
							title={t("common:NaceCode")}
							type="text"
							value={naceCode}
							required
							handleChange={e => setNaceCode(e.target.value)}
						/>
					</div>
					<div className=" mt-3">
						<FormInput
							title={t("common:Industry")}
							type="text"
							value={industry}
							placeholder=""
							required
							handleChange={e => setIndustry(e.target.value)}
						/>
					</div>
					<h4 className="my-3 mt-6 font-bold">{t("common:ContactDetail")}</h4>
					<div className=" mt-3">
						<FormInput
							title={t("common:FirstName")}
							type="text"
							required
							value={firstName}
							handleChange={e => setFirstName(e.target.value)}
						/>
					</div>
					<div className=" mt-3">
						<FormInput
							title={t("common:LastName")}
							type="text"
							required
							value={lastName}
							handleChange={e => setLastName(e.target.value)}
						/>
					</div>
					<div className=" mt-3">
						<FormInput
							title={t("common:Email")}
							type="email"
							required
							value={email}
							handleChange={e => setEmail(e.target.value)}
						/>
					</div>
					<div className=" mt-3">
						<FormInput
							title={t("common:Telephone")}
							type="text"
							value={phoneNumber}
							handleChange={e => setPhoneNumber(e.target.value)}
						/>
					</div>
					<div className="form-group mt-3 flex items-start">
						<input
							className="w-4 h-4 mt-1 text-sky-600 bg-gray-100 border-gray-300 rounded focus:ring-sky-500 dark:focus:ring-sky-600 dark:ring-offset-gray-800 focus:ring-1 checked:text-sky-600 dark:bg-gray-700 dark:border-gray-600"
							type="checkbox"
							checked={agree}
							onChange={() => setAgree(!agree)}
							required
						/>
						<label className="ml-4 mt-0 mb-2">
							{t("terms")} {/* TODO Link to the policy */}
							<a className="text-sky-600 cursor-pointer" href="#">
								{/* jhgjh */}
								{t("policy")}
							</a>
						</label>
					</div>
					<Button title={t("createCompany")} disabled={!agree} type="submit" width={"100%"} />
					{loader}
				</form>
			</div>
		</div>
	)
}
