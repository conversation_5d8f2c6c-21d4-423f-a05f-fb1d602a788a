exports = async function(query) {
  // Destructure naceCode from the query parameters
  const { naceCode } = query;

  if (!naceCode) {
    throw new Error("Missing 'naceCode' parameter in the query.");
  }

  // Get the MongoDB service client
  const mongodb = context.services.get("mongodb-atlas");
  
  // Get the 'co2-intensities' database
  const db = mongodb.db("co2-intensities-dev");
  
  // Get the 'nace' collection
  const naceCollection = db.collection("nace");

  console.log(`Searching for NACE code: ${naceCode}`);

  try {
    // Find the document matching the naceCode
    const naceData = await naceCollection.findOne({ code: naceCode });

    if (naceData) {
      console.log(`Found NACE data: ${JSON.stringify(naceData)}`);
      // Return the co2Intensity if found
      return naceData.co2Intensity;
    } else {
      console.log(`NACE code ${naceCode} not found.`);
      // Return default intensity object if not found
      return {
        "2020": 23.27,
        "2021": 22.59,
        "2022": 22.02,
        "2023": 20.61,
        "2024": 19.75,
        "2025": 19.16,
      };
    }
  } catch (error) {
    console.error(`Error fetching NACE data for code ${naceCode}: ${error}`);
    throw new Error(`Failed to retrieve CO2 intensity for NACE code ${naceCode}: ${error.message}`);
  }
};