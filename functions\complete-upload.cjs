const uploadHandler = require("../src/server/upload-handler.cjs")

exports.handler = async (event) => {
	if (event.httpMethod !== "POST") {
		return {
			statusCode: 405,
			body: JSON.stringify({ error: "Method Not Allowed" }),
			headers: { "Content-Type": "application/json" },
		}
	}

	// Ensure event.body is not null or undefined
	if (!event.body) {
		return {
			statusCode: 400,
			body: JSON.stringify({ error: "Request body is missing" }),
			headers: { "Content-Type": "application/json" },
		}
	}

	// Parse the request body
	let requestBody
	try {
		requestBody = JSON.parse(event.body)
	} catch (error) {
		return {
			statusCode: 400,
			body: JSON.stringify({ error: "Invalid JSON body" }),
			headers: { "Content-Type": "application/json" },
		}
	}

	return new Promise((resolve) => {
		// Mock Express req and res for upload-handler
		const req = { body: requestBody }
		const res = {
			status: function (statusCode) {
				return {
					json: function (data) {
						resolve({
							statusCode,
							body: JSON.stringify(data),
							headers: { "Content-Type": "application/json" },
						})
					},
				}
			},
		}

		try {
			uploadHandler.completeUpload(req, res)
		} catch (error) {
			resolve({
				statusCode: 500,
				body: JSON.stringify({ error: "Internal Server Error" }),
				headers: { "Content-Type": "application/json" },
			})
		}
	})
}
