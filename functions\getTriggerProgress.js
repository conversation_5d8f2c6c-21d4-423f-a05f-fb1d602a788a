exports = async function (input) {
	try {
		const { userId, RegistrationNumber } = input

		const req = context.services.get("mongodb-atlas").db(context.environment.values.database).collection("errordata")

		const errorData = await req.findOne({ userId, read: false })

		if (errorData) {
			await req.updateOne({ userId, read: false }, { $set: { read: true } })
			return {
				success: false,
				statusCode: 302,
				message: errorData.error_message,
			}
		}

		let company = null

		const request = context.services.get("mongodb-atlas").db(context.environment.values.database).collection("company")

		if (RegistrationNumber) {
			company = await request.findOne(
				{
					RegistrationNumber,
					Updating: true,
				},
				{
					_id: 0,
					ImportID: 1,
					RegistrationNumber: 1,
				}
			)
		} else {
			company = await request.findOne(
				{
					AuthId: userId,
					Updating: true,
				},
				{
					_id: 0,
					ImportID: 1,
					RegistrationNumber: 1,
				}
			)
		}

		if (company) {
			const { ImportID, RegistrationNumber } = company

			let lengthOfimportIDs = ImportID.length

			let lastImportID = ImportID[lengthOfimportIDs - 1]

			let key = Object.keys(lastImportID)

			let totalTransactions = Number(lastImportID[key])

			const request2 = context.services
				.get("mongodb-atlas")
				.db(context.environment.values.database)
				.collection("transaction")
			const res1 = await request2
				.aggregate([
					{
						$match: {
							RegistrationNumber: RegistrationNumber,
							ImportID: key.toString(),
							Updated: true,
						},
					},
					{
						$count: "updatedTransactions",
					},
				])
				.toArray()

			const { updatedTransactions } = res1[0]

			let percentage = (updatedTransactions / totalTransactions) * 100

			if (percentage === 100) {
				await request.updateOne({ RegistrationNumber }, { $set: { Updating: false } })
				return {
					percentage,
					isCompleted: true,
				}
			} else {
				return {
					percentage,
					isCompleted: false,
				}
			}
		} else {
			return {
				isCompleted: false,
			}
		}
	} catch (error) {
		console.log(error)
		return {
			error: true,
		}
	}
}
