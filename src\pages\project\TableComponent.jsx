import Spinner from "../../components/ui/Spinner"

const TableComponent = ({
	data,
	loading,
	tableHeadings,
	handleClick,
	isLink = true,
	queryEnabled,
}) => {
	return (
		<div className="border-t-4 w-full border-sky-600 shadow-md sm:rounded-lg mb-3">
			<div className={` ${queryEnabled ? "max-h-[400px]" : "h-full"} overflow-y-auto`}>
				<table className="divide-y w-full divide-slate-300">
					<thead className="sticky top-0 bg-slate-100">
						<tr>
							{tableHeadings.map((heading, index) => (
								<th
									key={index}
									className={`px-4 py-3 text-base whitespace-nowrap w-full font-semibold text-slate-900 ${
										index === 0 ? "text-left" : "text-right"
									}`}
								>
									{heading}
								</th>
							))}
						</tr>
					</thead>
					<tbody
						aria-hidden="true"
						className="divide-y divide-slate-200 bg-white"
						onClick={event => handleClick(event)}
					>
						{loading ? (
							data.map((analysis, index) => (
								<tr className="table-light" key={index}>
									<td
										className={`truncate py-2.5 px-4 text-base ${
											isLink ? "text-sky-500 cursor-pointer" : "text-slate-700"
										}`}
										data-id={analysis._id}
										data-name={analysis.name}
										style={{ maxWidth: "100px" }}
									>
										{analysis.name}
									</td>
									<td className="hidden 2xl:table-cell lg:table-cell md:table-cell px-4 py-2 text-right text-base text-slate-700">
										{analysis.totalEmission.toFixed(2) || 0}
									</td>
								</tr>
							))
						) : (
							<tr>
								<td className="custom-td" colSpan={3}>
									<Spinner type="sm" />
								</td>
							</tr>
						)}
					</tbody>
				</table>
			</div>
		</div>
	)
}

export default TableComponent
