const updateRecord = async (input, transactionId) => {
	//let { id }=input
	try {
		const request = context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("transaction")

		const result2 = await request.updateOne(
			{
				_id: transactionId,
				RegistrationNumber: input.RegistrationNumber,
			},
			{
				$set: {
					...input,
				},
			}
		)
	} catch (err) {
		console.log(err)
	}
}

exports = async function (input) {
	try {
		var { Notes, Scope_3_Category, data, RegistrationNumber } = input

		var newData = []

		for (let i = 0; i < data.length; i++) {
			let { Scope_1, Scope_2, Scope_3, id, NaceCode, Status } = data[i]

			let transactionId = new BSON.ObjectId(id)

			let input = {
				Scope_1,
				Scope_2,
				Scope_3,
				Notes,
				NaceCode,
				Status,
				RegistrationNumber,
			}

			if (Scope_3_Category != -1) {
				input["Scope_3_Category"] = Scope_3_Category
			}

			newData.push({ ...input, _id: transactionId })

			await updateRecord(input, transactionId)
		}

		return { status: "done", modifieddata: newData }
	} catch (err) {
		console.log(err)
		return { status: "error" }
	}
}
