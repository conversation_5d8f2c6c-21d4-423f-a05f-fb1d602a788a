/*TransactionEditDetailsDialog Css */

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	margin: 0;
}

/* Tooltip container */
.tooltip {
	position: relative;
	display: inline-block;
	opacity: 1;
	top: -2px;
}

.tooltip .tooltiptext {
	visibility: hidden;

	font-size: 11px;
	background: #e0e0e0;
	color: #4f4f4f;
	padding: 5px 5px;
	border-radius: 6px;
	bottom: 100%;
	left: 50%;
	margin-left: -140px;

	position: absolute;
	z-index: 100;
}

/* Show the tooltip text when you mouse over the tooltip container */
.tooltip:hover .tooltiptext {
	visibility: visible;
}

.tooltip .tooltiptext2 {
	visibility: hidden;
	width: 280px;
	font-size: 11px;
	background: #e0e0e0;
	color: #4f4f4f;
	padding: 5px 5px;
	border-radius: 6px;
	top: 100%;
	left: 700%;
	margin-left: -140px;
	position: absolute;
	z-index: 200;
}

/* Show the tooltip text when you mouse over the tooltip container */
.tooltip:hover .tooltiptext2 {
	visibility: visible;
}

/* Show the tooltip text when you mouse over the tooltip container */
.tooltip:hover .tooltiptext2 {
	visibility: visible;
}

.tooltip-icon {
	font-size: 20px;
}

.tooltip-controlled {
	height: 60px !important;
}

.edit-dialog-target {
	height: 100%;
}

.period-select {
	width: 150px;
}

.saved-emission {
	font-size: 14px;
	overflow-wrap: anywhere;
}

.edit-details-dialogs-container {
	position: fixed;
	max-width: 0px;
	z-index: 5000;
	top: 0;
	right: 0;
	height: 100%;
	visibility: hidden;
	background-color: #f2f2f2;
	box-shadow: 1px 5px 20px #888888;
}

.edit-details-dialogs-container-hide {
	max-width: 0px;
	visibility: hidden;
}

.edit-details-dialogs-container-show {
	transition: all 0.8s ease;
	visibility: visible;
	max-width: 980px;
	width: 68%;
}

.edit-details-details-container {
	height: 90%;
}

.edit-details-sub-container {
	position: relative;
	height: 100%;
}

.closeBtn {
	cursor: pointer;
	width: 40px;
	height: 40px;
}

.scope-emissions-container {
	padding: 8px;
	background: #fffdfd;
	box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
	border-radius: 4px;
}

.tab-pannels > *:not(:first-child) {
	@apply border-t border-gray-200;
}

.transaction-emission-tb th {
	padding: 6px;
}

.transaction-emission-tb td {
	font-size: 12px;
	padding-left: 8px;
}

.scope-details-container {
	overflow: hidden;
	flex-grow: 1;
	position: relative;
	padding: 0px;
}

.tab-content {
	position: absolute;
	top: 50px;
	left: 0;
	right: 0;
	bottom: 30px;
	overflow: auto;
}

.save-cancel-btn {
	position: fixed;
	bottom: 0px;
	height: 48px;
	right: 2px;
}

.metric-label {
	margin-bottom: 0px;
	color: #01aae4;
	font-weight: 600;
	white-space: nowrap;
}

.radiative-value {
	margin-bottom: 0px;
	color: #01aae4;
	font-weight: 600;
	white-space: nowrap;
	font-size: 15px;
}

.custom-form-input-control-electricity {
	padding-right: 30px;
}

.custom-span-unit-value {
	position: absolute;
	right: 4px;
	font-size: 14px;
	top: 12px;
	color: rgba(0, 0, 0, 0.5);
	line-height: 1;
}

.custom-span-unit-value-gram-co2 {
	position: absolute;
	right: 4px;
	font-size: 14px;
	top: 5px;
	color: rgba(0, 0, 0, 0.5);
	line-height: 1;
}

.custom-span-unit-value-gram-co2-electricity {
	position: absolute;
	right: 4px;
	font-size: 9px;
	top: 8px;
	color: rgba(0, 0, 0, 0.5);
	line-height: 1;
}

.custom-span-unit-value-save {
	white-space: nowrap;
	color: rgba(0, 0, 0, 0.5);
	margin-left: 4px;
	font-size: 11px;
	margin-top: 3px;
}

.scope-unit-kg-co2e {
	padding-right: 68px !important;
	width: 100% !important;
}

.scope-unit-kg-per-1000-nok {
	padding-right: 128px !important;
	width: 100% !important;
}

.scope-kg-co2e {
	position: absolute;
	right: 6px;
	top: 6px;
	white-space: nowrap;
	color: rgba(0, 0, 0, 0.5);
}

.scope-kg-per-1000-nok {
	position: absolute;
	right: 10px;
	top: 5px;
	white-space: nowrap;
	color: rgba(0, 0, 0, 0.5);
}

.custom-form-input-control-unit {
	padding-right: 60px;
}

.unit-kg-co2e {
	padding-right: 60px;
}

.unit-combustion-metric {
	padding-right: 62px;
}

.unit-kgs {
	padding-right: 28px;
}

.unit-kg {
	padding-right: 25px;
}

.unit-km {
	padding-right: 25px;
}

.unit-kms {
	padding-right: 32px;
}

.unit-l-100km {
	padding-right: 70px;
}

.unit-co2e {
	padding-right: 42px;
}

.unit-gco2e {
	padding-right: 48px;
}

.unit-percentage {
	padding-right: 24px;
}

.unit-gram-co2e {
	padding-right: 30px;
}

.unit-kwh {
	padding-right: 30px;
}

.unit-pcs {
	padding-right: 30px;
}

.unit-people {
	padding-right: 48px;
}

.unit-nights {
	padding-right: 48px;
}

.unit-sqm {
	padding-right: 32px;
}

.radiative-factor {
	height: 34px;
	width: 20px;
}

.radiative-factor-text {
	line-height: 1.2;
}

.radio-yes-no {
	font-size: 12px;
}

.yes-no-radio-label {
	position: relative;
	top: -2px;
}

.custom-form-input-control {
	min-width: 100px;
}

.btn-info-custom {
	background-color: #01aae4;
	border-color: #01aae4;
	color: #ffffff;
	font-weight: 600;
}

.metric-select-container {
	flex: 1 1;
}

.delete-icon {
	display: flex;
	align-items: flex-end;
	font-size: 24px;
	color: #ff0000;
	cursor: pointer;
}

.custom-factor-unit-field {
	padding-right: 38px;
}

/* override nav tab css */
.tab-content-container .tab-content {
	position: unset !important;
	overflow: unset;
}

.nav-tabs {
	border-bottom: 2px solid #2f80ed;
}

.nav-tabs .nav-link {
	background-color: #ffffff;
	color: #000000;
	margin-bottom: 0px;
	border-bottom: 0px;
}

.nav-tabs .nav-link.active {
	background-color: #2f80ed;
	color: #ffffff;
	border-color: unset;
}

.nav-tabs::after {
	background-color: red;
	height: 10px;
}

.tab-content .active {
	background-color: unset;
}

/* override nav tab css */

/* override accordion css */
.accordion-item:first-of-type {
	border-top-left-radius: 0.25rem;
	border-top-right-radius: 0.25rem;
}

.accordion-item {
	background-color: #fff;
	border: 1px solid rgba(0, 0, 0, 0.125);
}

.accordion-header {
	margin-bottom: 0;
}

.accordion-button:not(.collapsed) {
	color: #0c63e4;
	background-color: #e7f1ff;
	box-shadow: inset 0 -1px 0 rgb(0 0 0 / 13%);
}

.accordion-item:first-of-type .accordion-button {
	border-top-left-radius: calc(0.25rem - 1px);
	border-top-right-radius: calc(0.25rem - 1px);
}

.accordion-button {
	position: relative;
	display: flex;
	align-items: center;
	width: 100%;
	padding: 1rem 1.25rem;
	font-size: 1rem;
	color: #212529;
	text-align: left;
	background-color: #fff;
	border: 0;
	border-radius: 0;
	overflow-anchor: none;
	transition:
		color 0.15s ease-in-out,
		background-color 0.15s ease-in-out,
		border-color 0.15s ease-in-out,
		box-shadow 0.15s ease-in-out,
		border-radius 0.15s ease;
}

.accordion-button:not(.collapsed):after {
	background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%230c63e4'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
	transform: rotate(-180deg);
}

.accordion-button:after {
	flex-shrink: 0;
	width: 1.25rem;
	height: 1.25rem;
	margin-left: auto;
	content: "";
	background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-size: 1.25rem;
	transition: transform 0.2s ease-in-out;
}

.accordion-body {
	padding: 1rem 1.25rem;
}

/* override accordion css */

/*TransactionDialogFrom Css */
#edit-details-btn {
	float: left;
}

/*TransactionDialogFrom Css */

/* Transaction */
.dull-background {
	position: fixed;
	width: 100vw;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0.125);
	top: 0;
}

.radio-label {
	position: relative;
	top: -2px;
}

.aleart-Clear {
	background-color: #faedb7;
	background-position-x: right;
	font-size: 15px;
	border-radius: 8px;
	float: right;
	width: 100px;
	color: #1e90ff;
	font-weight: bold;
	border-color: #1ec6ff;
	margin-top: -48px;
	padding: 2.1px;
}

.e-warning {
	background-color: #faedb7;
}

.e-dialog .e-footer-content {
	position: relative;
}

.e-footer-content .e-btn.e-primary.e-flat {
	float: right;
}

.e-grid .e-pager {
	padding: 0.25rem;
}

.css-13cymwt-control {
	border-color: #94a3b8;
}
