/**
 * Asynchronous function to process the given arguments and return relevant data.
 *
 * @param {object} args - The input arguments for the function
 * @return {object} The data to be returned from the function
 */
exports = async function (args) {
	try {
		let transaction = args.transaction
		// get the account data from value AccountID and not the AccountData passed from insertTransaction
		let accountData = context.values.get("accountData")
		// Variable to store an AccountID
		let finalAccountID

		// Check if the AccountID on the transaction is in the AccountData object
		// If found - all is good.
		if (transaction.AccountID in accountData) {
			finalAccountID = transaction.AccountID

			// If the AccountID is not there -> run getStandardAccountID to get the StandardAccoundID that might be stored in Accounts
		} else {
			finalAccountID = transaction.StandardAccountID

			// Then resort what the finalAccountID should be
			// finalAccountID = getAccountId(transaction.AccountID, StandardAccountID)
		}
		if (!finalAccountID) {
			// This might be the best place to handle no AccountID error.
			return {}
		}

		// Get the data from the accountData object
		// If the final accountID is not in accountData
		// then the substring of the 2 first chars should be used and should be found
		if (!(finalAccountID in accountData)) {
			finalAccountID = finalAccountID.substring(0, 2)
		}
		const data = accountData[finalAccountID]
		const dataToReturn = {
			// Return the finalAccountID as AccountID - Not used on the other side.
			AccountID: finalAccountID,

			Status: data["status"],
			NaceCode: data["nace-code"],
			DescriptionDetails: data["description-details"],
			Scope: data["scope"],
			Scope_3_Category: data["scope-3-category"],
			Notes: data["notes"],
			Relation: data["relation"],
			Type: data["type"],
		}
		if ("function" in data) {
			dataToReturn.function = data.function
		}
		return dataToReturn
	} catch (error) {
		console.log("ERROR IS====", error)
		return { Status: -1, Scope: 0 }
	}
}
