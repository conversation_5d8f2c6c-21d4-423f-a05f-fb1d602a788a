{"publish": "Publish", "republish": "Republish", "continue": "CONTINUE", "cancel": "CANCEL", "deletion_warning": "Deletion Warning", "file_deleted_message": "File deleted successfully.", "confirm_delete_message": "Are you sure you want to delete this?", "data_published_message": "Your data is published successfully.", "organization_created_message": "Organization created successfully.", "revoked_successfully_message": "Revoked successfully.", "invitation_sent_message": "Invitation has been sent successfully.", "max_members_reached_message": "Maximum number of members reached. You cannot invite more users.", "NoRevenueAlertTitle": "No Revenue Provided", "NoRevenueAlertText": "You can not publish data without revenue. Please add revenue first.", "Published climate data:": "Published climate data:", "Publish": "Publish data", "Unpublish": "Unpublish", "organization": "Organization", "org_not_enable": "Organization features has not been enabled", "enable_org_feature": "Enable organization feature", "Administrator": "Administrator", "Member": "Member", "org_feat_with_multi_users": "Organization features with multiple users is not available for free users", "team_is_available_for_org": "Team is only available for organizations", "InviteMembers": "Invite Members", "SendInvite": "Send Invite", "users_will_be_invited": "User will be invited to company", "PendingInvites": "Pending Invites", "BasicMember": "Basic Member", "RevokeInvitation": "Revoke Invitation", "DeleteMember": "Delete Member", "SwitchRole": "Switch Role", "RegisterCompany": "Register Company", "no_company_reg_with_account": "There is no company registered with your account.", "back_to_account": "Back to Account", "CompanyLogo": "Company Logo", "DropHere": "Drop here...", "Upload": "Upload", "Data": "Data", "upload_fin_data": "Upload financial data:", "last_upload_trans": "The date of the last uploaded transaction is:", "file_upload_warning": "Make sure you are not uploading the same files or files with overlapping transactions as this will create duplicates in your database. Make sure the filename is not in the history list below.", "no_file_attached": "No file is attached with this account.", "no_trans": "No transactions are added", "PreparingYourData": "Preparing your data...", "company_not_found": "company with this registration number not found.", "GrantAccess": "Grant Access"}