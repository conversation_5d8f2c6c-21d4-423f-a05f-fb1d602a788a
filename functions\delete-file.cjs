const fileService = require("../src/server/upload-handler.cjs")

exports.handler = async (event) => {
	if (event.httpMethod !== "DELETE") {
		return {
			statusCode: 405,
			body: JSON.stringify({ error: "Method Not Allowed" }),
			headers: { "Content-Type": "application/json" },
		}
	}

	// Parse the request body
	const { fileId } = JSON.parse(event.body || "{}")

	if (!fileId) {
		return {
			statusCode: 400,
			body: JSON.stringify({ error: "File id are required" }),
			headers: { "Content-Type": "application/json" },
		}
	}

	return new Promise((resolve) => {
		// Mock Express req and res for file deletion
		const req = { params: { fileId } }
		const res = {
			status: function (statusCode) {
				return {
					json: function (data) {
						resolve({
							statusCode,
							body: JSON.stringify(data),
							headers: { "Content-Type": "application/json" },
						})
					},
				}
			},
		}

		try {
			fileService.deleteFile(req, res)
		} catch (error) {
			resolve({
				statusCode: 500,
				body: JSON.stringify({ error: "Internal Server Error" }),
				headers: { "Content-Type": "application/json" },
			})
		}
	})
}
