import React from "react"

export default function Button({
	handleClick,
	title,
	variation = "primary",
	disabled = false,
	type = "",
	width,
	name = "",
	color = "sky-600",
}) {
	const buttonStyle = width ? { width: width } : {}
	return (
		<>
			{variation === "primary" && (
				<button
					onClick={handleClick}
					type={type}
					disabled={disabled}
					name={name}
					style={buttonStyle}
					className={`bg-${color} px-4 justify-end text-white rounded-lg py-2 cursor-pointer font-bold text-md hover:border-${color}  disabled:cursor-not-allowed`}
				>
					{title}
				</button>
			)}
			{variation === "secondary" && (
				<button
					onClick={handleClick}
					type={type}
					name={name}
					disabled={disabled}
					style={buttonStyle}
					className="border-sky-600 px-4 border-2 justify-end text-sky-600 rounded-lg py-2 font-bold text-md hover:bg-sky-600 hover:text-white disabled:cursor-not-allowed"
				>
					{title}
				</button>
			)}
			{variation === "danger" && (
				<button
					onClick={handleClick}
					type={type}
					disabled={disabled}
					name={name}
					style={buttonStyle}
					className="border-red-400 px-4 border-2 justify-end text-red-400 rounded-lg py-2 font-bold text-md hover:bg-red-400 hover:text-white"
				>
					{title}
				</button>
			)}
		</>
	)
}
