exports = async (args) => {
	const { RegistrationNumber, yearImportFrom, yearImportFor } = args
	try {
		const company = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("company")
			.findOne({ RegistrationNumber: RegistrationNumber })
		let reports = company.reports
		let newReports = []
		if (reports === undefined) {
			return {success:false, statusCode:404}
		} else {
			let report = reports.filter((report) => {
				if (report.year == yearImportFrom) {
					return information
				}
			})

            if(report.length === 0){
                return { success: false, statusCode: 404 }
            }

            // let index = reports.findIndex((report)=>{
            //     report.year === yearImportFor
            // })

           return { success:true, data: report}


			
		}

		// const request = await context.services
		// 	.get("mongodb-atlas")
		// 	.db(context.environment.values.database)
		// 	.collection("company")
		// 	.updateOne({ RegistrationNumber: RegistrationNumber }, { $set: { reports: newReports } })
		// if (request) {
		// 	return { success: true }
		// } else {
		// 	return { success: false }
		// }
	} catch (error) {
		console.log(error)
		return { success: false }
	}
}
