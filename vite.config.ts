import path from "path";

import react from "@vitejs/plugin-react";
import { defineConfig } from "vite";
// @ts-ignore - vite-plugin-eslint doesn't have proper TypeScript declarations
import eslint from "vite-plugin-eslint";

export default defineConfig(() => {
  return {
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
      },
      extensions: [".mjs", ".js", ".jsx", ".ts", ".tsx", ".json"],
    },
    server: {
      port: 3000,
      proxy: {
        "/.netlify/functions": {
          target: "http://localhost:8888",
          changeOrigin: true,
        },
      },
    },
    build: {
      outDir: "dist",
      target: "es2020",
      sourcemap: true,
    },
    plugins: [
      react(),
      eslint({
        include: ["src/**/*.{js,jsx,ts,tsx}"],
        exclude: ["node_modules", "dist", "build"],
        failOnError: false,
        failOnWarning: false,
        emitWarning: true,
        emitError: true,
        lintOnStart: false,
      }),
    ],
  };
});
