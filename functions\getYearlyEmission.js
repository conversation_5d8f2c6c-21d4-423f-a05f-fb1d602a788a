exports = async function (input) {
	try {
		const request = context.services.get("mongodb-atlas").db(context.environment.values.database).collection("company")

		const result = await request.find({ RegistrationNumber: input.RegistrationNumber }).toArray()

		const { Emissions } = result[0]
		// if emissions are found means user has publish its data
		if (Emissions) {
			// get all years from emissions

			const years = Object.keys(Emissions)

			// if there is no emission return false

			if (years.length == 0) return { showChart: false }

			// if emission is there then get that emission by year and return that emission

			const data = years.map((year) => {
				return {
					year,
					Scope1:Emissions[year].Scope1,
                    Scope2:Emissions[year].Scope2,
                    Scope3:Emissions[year].Scope3,
				}
			})
			return {
				showChart: true,
				data,
			}
		}

		return { showChart: false }
	} catch (error) {
		console.log(error)
		return { showChart: false, error: error.message }
	}
}
