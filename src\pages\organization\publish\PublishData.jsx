/* eslint-disable import/no-unresolved */
import { Table, TableBody, TableCell, TableHead, TableHeaderCell, TableRow } from "@tremor/react"
import React, { useState, useEffect } from "react"
import { useSelector, useDispatch } from "react-redux"

import Alert from "../../../components/Alert.component"
import Button from "../../../components/ui/Button"
import {
	useGetCompanyEmission,
	useGetPublishedDataStatus,
	useGetScopeChartData,
	useToast,
} from "../../../hooks"
import { useRealmApp } from "../../../realm/RealmAppProvider"
import { handleSetScopeData } from "../../../store/actions/UserAction"
import useFullPageLoader from "../../loader/useFullPageLoader"
import Loader from "../loader/Loader.component"

import { Badge } from "@/components/ui/badge"

export default function PublishData({ t }) {
	const [isDataPublished, setIsDataPublished] = useState()

	const dispatch = useDispatch()
	const Toast = useToast()
	const [showNoRevenueAlert, setShowNoRevenueAlert] = useState(false)

	const [emissionData, setEmissionData] = useState({})

	const [revenue, setRevenue] = useState(0)

	let scopeData = useSelector(state => state.ScopeData)

	const [PublishedData, setPublishedData] = useState([])
	const [loader, showLoader, hideLoader] = useFullPageLoader()
	const app = useRealmApp()
	const reportingYear = useSelector(state => state.reporting)

	const currentOrganization = useSelector(state => state.user.currentOrganization)

	const RegistrationNumber = currentOrganization ? currentOrganization.RegistrationNumber : ""

	const scope_Data = useGetScopeChartData(app, { RegistrationNumber, year: reportingYear })

	const CompanyEmission = useGetCompanyEmission(app, { RegistrationNumber, year: reportingYear })

	const publishedDataInfo = useGetPublishedDataStatus(app, {
		RegistrationNumber,
	})

	const getNextYearsCo2Intensities = () => {
		let nextYearsCo2Intensities = {}

		let lastYearCo2Intensity = 0

		PublishedData.forEach(data => {
			if (data.published) {
				lastYearCo2Intensity = Number(data.co2Intensity)
			}
			if (Number(data.year) > Number(reportingYear) && !data.published) {
				nextYearsCo2Intensities[data.year] = {
					co2Intensity: Number(lastYearCo2Intensity),
				}
			}
		})

		return nextYearsCo2Intensities
	}

	const updatePublishedData = () => {
		const publishedData = [...PublishedData]

		const date = new Date()

		return publishedData.map(item =>
			Number(item.year) === Number(reportingYear)
				? {
						year: reportingYear,
						updatedAt: `${date.getFullYear()}/${date.getMonth() + 1}/ ${date.getDate()}`,
						published: true,
					}
				: item
		)
	}

	const handlePublishData = async () => {
		try {
			const nextYearsCo2Intensities = getNextYearsCo2Intensities()

			const payload = {
				Emission: {
					[reportingYear]: {
						Revenue: Number(scopeData.revenue) === 0 ? revenue : Number(scopeData.revenue),
						TotalCo2: parseFloat(Number(emissionData.total).toFixed(2)),
						co2Intensity: Number(scopeData?.carbonIntensity),
						Scope1: parseFloat(Number(emissionData.Scope_1).toFixed(2)),
						Scope2: parseFloat(Number(emissionData.Scope_2).toFixed(2)),
						Scope3: parseFloat(Number(emissionData.Scope_3).toFixed(2)),
						locked: true,
						updatedAt: new Date(),
					},
					...nextYearsCo2Intensities,
				},
				setRevenue: revenue !== 0,
				year: reportingYear,
				CreatedAt: new Date(),
				Nace: currentOrganization?.company?.NaceCode,
				CompanyName: currentOrganization.CompanyName,
				RegistrationNumber,
				IndustryName: "Test",
				Sector: "Roadworks",
				Category: "",
				Currency: "NOK",
				Naics: "",
				Sic: "",
			}

			showLoader()

			const result = await app.publishData(payload)

			hideLoader()

			if (result && result.success) {
				const updatedPublishedData = updatePublishedData()

				setPublishedData([...updatedPublishedData])

				setIsDataPublished(true)
			}

			Toast("success", t("data_published_message"))
		} catch (error) {}
	}

	const calculateCo2Intensity = () => {
		try {
			setShowNoRevenueAlert(false)
			const totalScopeData = emissionData.total * 1000
			const revenueInThousands = Number(revenue) / 1000
			const carbonIntensity = (totalScopeData / revenueInThousands).toFixed(2)

			scopeData = { ...scopeData, carbonIntensity: carbonIntensity, Revenue: Number(revenue) }

			dispatch(
				handleSetScopeData({
					...scopeData,
					carbonIntensity: carbonIntensity,
					Revenue: Number(revenue),
				})
			)

			handlePublishData()
		} catch (error) {}
	}

	const checkForRevenue = () => {
		if (Number(scopeData.revenue) === 0 && revenue === 0) {
			setShowNoRevenueAlert(true)
			return
		} else {
			handlePublishData()
		}
	}

	useEffect(() => {
		if (publishedDataInfo?.data?.success) {
			setPublishedData(publishedDataInfo.data.data)
		}
	}, [publishedDataInfo.data])

	useEffect(() => {
		let totalScope = emissionData.total * 1000
		let totalRevenue = Number(revenue) / 1000
		const intensity = (totalScope / totalRevenue).toFixed(2)

		dispatch(
			handleSetScopeData({ ...emissionData, revenue: Number(revenue), carbonIntensity: intensity })
		)
	}, [emissionData, revenue, dispatch])

	useEffect(() => {
		if (scope_Data.data && scope_Data.data.scope) {
			const scopeEmissionChartData = scope_Data.data
			const data = {
				Scope_1: parseFloat(scopeEmissionChartData.scope.Scope_1),
				Scope_2: parseFloat(scopeEmissionChartData.scope.Scope_2),
				Scope_3: parseFloat(scopeEmissionChartData.scope.Scope_3),
				total: 0,
				isDefault: false,
			}

			data.total = data.Scope_1 + data.Scope_2 + data.Scope_3
			setEmissionData(data)
		}
	}, [scope_Data.data])

	useEffect(() => {
		if (CompanyEmission.data?.success) {
			setRevenue(CompanyEmission.data?.data?.Revenue)
		} else {
			setRevenue(0)
		}
	}, [CompanyEmission.data])

	useEffect(() => {
		const reportingYearData = PublishedData.find(item => {
			return Number(item.year) === Number(reportingYear)
		})

		setIsDataPublished(reportingYearData ? reportingYearData.published : false)
	}, [PublishedData, reportingYear])

	return (
		<div className="cl-component cl-user-profile">
			<div className="cl-main mt-2">
				{showNoRevenueAlert && (
					<Alert
						handleClose={() => setShowNoRevenueAlert(false)}
						show={showNoRevenueAlert}
						title={t("NoRevenueAlertTitle")}
						text={t("NoRevenueAlertText")}
						inputPlaceholder="Enter revenue"
						hasInput={true}
						t={t}
						handleContinue={() => calculateCo2Intensity()}
						handleInputChange={e => {
							const regex = /^-?\d*\.?\d*$/
							const value = e.target.value
							if (regex.test(value)) setRevenue(Number(e.target.value))
						}}
					/>
				)}
				<div className="cl-user-profile-card cl-themed-card">
					<h4 className="font-extrabold text-2xl">{t("Publish")}</h4>
					<p>{t("Published climate data:")}</p>
					<div>
						{publishedDataInfo?.isLoading ? (
							<div className="mt-5 ml-5">
								<Loader />
							</div>
						) : (
							<div className="sm:w-full lg:w-1/2">
								<Table>
									<TableHead>
										<TableRow>
											<TableHeaderCell className="font-bold text-black">
												Period Year
											</TableHeaderCell>
											<TableHeaderCell className="font-bold text-black">Updated At</TableHeaderCell>
											<TableHeaderCell className="font-bold text-black">Status</TableHeaderCell>
										</TableRow>
									</TableHead>
									<TableBody>
										{PublishedData.map(data => (
											<TableRow key={data.year}>
												<TableCell>{data.year}</TableCell>
												<TableCell>{data.updatedAt}</TableCell>
												<TableCell>
													{data.published ? (
														<Badge variant="success">Published</Badge>
													) : (
														<Badge variant="destructive">Not Published</Badge>
													)}
												</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							</div>
						)}

						{!isDataPublished ? (
							<div className="flex justify-end mt-3">
								<Button
									title={t("publish")}
									type="primary"
									disabled={publishedDataInfo?.isLoading || false}
									handleClick={() => checkForRevenue()}
								/>
							</div>
						) : (
							<div className="flex justify-end mt-3">
								<Button
									title={t("republish")}
									type="primary"
									handleClick={() => {
										checkForRevenue()
									}}
								/>
							</div>
						)}
					</div>
				</div>
			</div>
			{loader}
		</div>
	)
}
