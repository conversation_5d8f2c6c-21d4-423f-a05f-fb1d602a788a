exports = async function (arg) {
	const { RegistrationNumber, year, projectId } = arg

	try {
		let reportData = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("projectReports")
			.findOne({ RegistrationNumber, year, projectId },{report:1})

		if(reportData)
        {
           const {report} = reportData

          return [{...report}]
        }
        return []
	} catch (error) {
		console.log(error)
		return {}
	}
}
