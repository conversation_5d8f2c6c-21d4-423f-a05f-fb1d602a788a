/**
 * Cache Warming Service
 * Implements intelligent preloading strategies for critical data
 */

import { cache<PERSON>anager, CACHE_TAGS, CacheTag } from "./cache-manager"
import { CACHE_WARMING_STRATEGIES } from "./query-cache-integration"

export interface WarmingStrategy {
	key: string
	fetcher: () => Promise<any>
	priority: "critical" | "important" | "background"
	tags: CacheTag[]
	ttl?: number
	condition?: () => boolean
	dependencies?: string[]
}

export interface WarmingResult {
	key: string
	success: boolean
	duration: number
	error?: Error
}

export interface WarmingStats {
	totalStrategies: number
	successCount: number
	failureCount: number
	totalDuration: number
	averageDuration: number
}

class CacheWarmingService {
	private warmingInProgress = new Set<string>()
	private warmingHistory = new Map<string, WarmingResult>()
	private maxConcurrentWarming = 3

	/**
	 * Warm cache with multiple strategies based on priority
	 */
	async warmCache(
		strategies: WarmingStrategy[],
		options: {
			maxConcurrent?: number
			timeoutMs?: number
			skipIfExists?: boolean
		} = {}
	): Promise<WarmingStats> {
		const {
			maxConcurrent = this.maxConcurrentWarming,
			timeoutMs = 30000,
			skipIfExists = true,
		} = options

		// Filter strategies based on conditions
		const validStrategies = strategies.filter(strategy => {
			// Check if condition is met
			if (strategy.condition && !strategy.condition()) {
				return false
			}

			// Skip if already warming
			if (this.warmingInProgress.has(strategy.key)) {
				return false
			}

			// Skip if exists and skipIfExists is true
			if (skipIfExists && this.isCacheEntryFresh(strategy.key)) {
				return false
			}

			return true
		})

		// Sort by priority
		const sortedStrategies = this.sortStrategiesByPriority(validStrategies)

		// Execute warming in batches
		const results: WarmingResult[] = []
		const batches = this.createBatches(sortedStrategies, maxConcurrent)

		for (const batch of batches) {
			const batchResults = await Promise.allSettled(
				batch.map(strategy => this.executeWarmingStrategy(strategy, timeoutMs))
			)

			batchResults.forEach((result, index) => {
				const strategy = batch[index]
				if (result.status === "fulfilled") {
					results.push(result.value)
				} else {
					results.push({
						key: strategy.key,
						success: false,
						duration: 0,
						error: result.reason,
					})
				}
			})
		}

		// Calculate stats
		const stats = this.calculateWarmingStats(results)

		// Store results in history
		results.forEach(result => {
			this.warmingHistory.set(result.key, result)
		})

		return stats
	}

	/**
	 * Warm critical data for application startup
	 */
	async warmCriticalData(app: any, currentOrganization: any): Promise<WarmingStats> {
		if (!currentOrganization?.RegistrationNumber) {
			return {
				totalStrategies: 0,
				successCount: 0,
				failureCount: 0,
				totalDuration: 0,
				averageDuration: 0,
			}
		}

		const currentYear = new Date().getFullYear()
		const registrationNumber = currentOrganization.RegistrationNumber

		const criticalStrategies: WarmingStrategy[] = [
			{
				key: `user-profile:${currentOrganization.id}`,
				fetcher: () => Promise.resolve(currentOrganization),
				priority: "critical",
				tags: [CACHE_TAGS.USER, CACHE_TAGS.ORGANIZATIONS],
				ttl: 30 * 60 * 1000, // 30 minutes
			},
			{
				key: `dashboard:yearly:${registrationNumber}:${currentYear}`,
				fetcher: () =>
					app.getYearlyChartData({
						RegistrationNumber: registrationNumber,
						year: currentYear,
					}),
				priority: "critical",
				tags: [CACHE_TAGS.DASHBOARD],
				ttl: 10 * 60 * 1000,
				condition: () => !!app.getYearlyChartData,
			},
			{
				key: `suppliers:current:${registrationNumber}:${currentYear}`,
				fetcher: () =>
					app.getSuppliers({
						RegistrationNumber: registrationNumber,
						year: currentYear,
					}),
				priority: "critical",
				tags: [CACHE_TAGS.SUPPLIERS],
				ttl: 15 * 60 * 1000,
				condition: () => !!app.getSuppliers,
			},
		]

		return this.warmCache(criticalStrategies, {
			maxConcurrent: 2,
			timeoutMs: 15000,
			skipIfExists: true,
		})
	}

	/**
	 * Warm important data after critical data is loaded
	 */
	async warmImportantData(app: any, currentOrganization: any): Promise<WarmingStats> {
		if (!currentOrganization?.RegistrationNumber) {
			return this.getEmptyStats()
		}

		const currentYear = new Date().getFullYear()
		const registrationNumber = currentOrganization.RegistrationNumber

		const importantStrategies: WarmingStrategy[] = [
			{
				key: `transactions:recent:${registrationNumber}:${currentYear}`,
				fetcher: () =>
					app.getTransactions({
						RegistrationNumber: registrationNumber,
						Scope: -1,
						year: currentYear,
						limit: 50,
					}),
				priority: "important",
				tags: [CACHE_TAGS.TRANSACTIONS],
				ttl: 5 * 60 * 1000,
				condition: () => !!app.getTransactions,
				dependencies: [`suppliers:current:${registrationNumber}:${currentYear}`],
			},
			{
				key: `nace:${registrationNumber}`,
				fetcher: () => app.getNace({ RegistrationNumber: registrationNumber }),
				priority: "important",
				tags: [CACHE_TAGS.NACE],
				ttl: 60 * 60 * 1000, // 1 hour
				condition: () => !!app.getNace,
			},
			{
				key: `analysis:company:${registrationNumber}`,
				fetcher: () => app.getCompanyAnalysis(registrationNumber),
				priority: "important",
				tags: [CACHE_TAGS.ANALYSIS],
				ttl: 20 * 60 * 1000,
				condition: () => !!app.getCompanyAnalysis,
				dependencies: [`transactions:recent:${registrationNumber}:${currentYear}`],
			},
		]

		return this.warmCache(importantStrategies, {
			maxConcurrent: 2,
			timeoutMs: 20000,
			skipIfExists: true,
		})
	}

	/**
	 * Warm background data for better user experience
	 */
	async warmBackgroundData(app: any, currentOrganization: any): Promise<WarmingStats> {
		if (!currentOrganization?.RegistrationNumber) {
			return this.getEmptyStats()
		}

		const currentYear = new Date().getFullYear()
		const registrationNumber = currentOrganization.RegistrationNumber

		const backgroundStrategies: WarmingStrategy[] = [
			{
				key: `settings:lock:${registrationNumber}`,
				fetcher: () => app.getLockStatus({ RegistrationNumber: registrationNumber }),
				priority: "background",
				tags: [CACHE_TAGS.SETTINGS],
				ttl: 30 * 60 * 1000,
				condition: () => !!app.getLockStatus,
			},
			{
				key: `settings:publish:${registrationNumber}`,
				fetcher: () => app.getPublishedDataStatus({ RegistrationNumber: registrationNumber }),
				priority: "background",
				tags: [CACHE_TAGS.SETTINGS],
				ttl: 30 * 60 * 1000,
				condition: () => !!app.getPublishedDataStatus,
			},
			{
				key: `suppliers:list:${registrationNumber}:${currentYear}`,
				fetcher: () =>
					app.getSuppliersList({
						RegistrationNumber: registrationNumber,
						year: currentYear,
					}),
				priority: "background",
				tags: [CACHE_TAGS.SUPPLIERS],
				ttl: 20 * 60 * 1000,
				condition: () => !!app.getSuppliersList,
			},
		]

		return this.warmCache(backgroundStrategies, {
			maxConcurrent: 1,
			timeoutMs: 30000,
			skipIfExists: true,
		})
	}

	/**
	 * Get warming history for debugging
	 */
	getWarmingHistory(): Map<string, WarmingResult> {
		return new Map(this.warmingHistory)
	}

	/**
	 * Clear warming history
	 */
	clearWarmingHistory(): void {
		this.warmingHistory.clear()
	}

	/**
	 * Get currently warming keys
	 */
	getCurrentlyWarming(): string[] {
		return Array.from(this.warmingInProgress)
	}

	private async executeWarmingStrategy(
		strategy: WarmingStrategy,
		timeoutMs: number
	): Promise<WarmingResult> {
		const startTime = Date.now()
		this.warmingInProgress.add(strategy.key)

		try {
			// Create timeout promise
			const timeoutPromise = new Promise<never>((_, reject) => {
				setTimeout(() => reject(new Error("Warming timeout")), timeoutMs)
			})

			// Race between fetcher and timeout
			const data = await Promise.race([strategy.fetcher(), timeoutPromise])

			// Store in cache
			cacheManager.set(strategy.key, data, {
				key: strategy.key,
				ttl: strategy.ttl || 5 * 60 * 1000,
				tags: strategy.tags,
				staleWhileRevalidate: true,
				maxAge: (strategy.ttl || 5 * 60 * 1000) * 2,
				priority:
					strategy.priority === "critical"
						? "high"
						: strategy.priority === "important"
							? "medium"
							: "low",
			})

			const duration = Date.now() - startTime

			return {
				key: strategy.key,
				success: true,
				duration,
			}
		} catch (error) {
			const duration = Date.now() - startTime

			return {
				key: strategy.key,
				success: false,
				duration,
				error: error as Error,
			}
		} finally {
			this.warmingInProgress.delete(strategy.key)
		}
	}

	private sortStrategiesByPriority(strategies: WarmingStrategy[]): WarmingStrategy[] {
		const priorityOrder = { critical: 0, important: 1, background: 2 }

		return strategies.sort((a, b) => {
			return priorityOrder[a.priority] - priorityOrder[b.priority]
		})
	}

	private createBatches<T>(items: T[], batchSize: number): T[][] {
		const batches: T[][] = []

		for (let i = 0; i < items.length; i += batchSize) {
			batches.push(items.slice(i, i + batchSize))
		}

		return batches
	}

	private calculateWarmingStats(results: WarmingResult[]): WarmingStats {
		const successCount = results.filter(r => r.success).length
		const failureCount = results.length - successCount
		const totalDuration = results.reduce((sum, r) => sum + r.duration, 0)
		const averageDuration = results.length > 0 ? totalDuration / results.length : 0

		return {
			totalStrategies: results.length,
			successCount,
			failureCount,
			totalDuration,
			averageDuration,
		}
	}

	private isCacheEntryFresh(key: string): boolean {
		// Check if entry exists and is fresh in cache manager
		const entries = cacheManager.getEntries()
		const entry = entries.find(e => e.key === key)

		if (!entry) return false

		const now = Date.now()
		return now - entry.entry.timestamp < entry.entry.ttl
	}

	private getEmptyStats(): WarmingStats {
		return {
			totalStrategies: 0,
			successCount: 0,
			failureCount: 0,
			totalDuration: 0,
			averageDuration: 0,
		}
	}
}

// Global cache warming service instance
export const cacheWarmingService = new CacheWarmingService()

/**
 * React hook for cache warming
 */
export function useCacheWarming() {
	const warmCriticalData = async (app: any, currentOrganization: any) => {
		return cacheWarmingService.warmCriticalData(app, currentOrganization)
	}

	const warmImportantData = async (app: any, currentOrganization: any) => {
		return cacheWarmingService.warmImportantData(app, currentOrganization)
	}

	const warmBackgroundData = async (app: any, currentOrganization: any) => {
		return cacheWarmingService.warmBackgroundData(app, currentOrganization)
	}

	const getWarmingHistory = () => {
		return cacheWarmingService.getWarmingHistory()
	}

	const getCurrentlyWarming = () => {
		return cacheWarmingService.getCurrentlyWarming()
	}

	return {
		warmCriticalData,
		warmImportantData,
		warmBackgroundData,
		getWarmingHistory,
		getCurrentlyWarming,
	}
}
