exports = async (input) => {
	try {
		const request = context.services.get("mongodb-atlas").db(context.environment.values.database).collection("company")

		const result = await request.findOne({ RegistrationNumber: input.RegistrationNumber })

		if (result) {
			let supplier = [input.supplier]
			if (result.Suppliers) {
				supplier = [...supplier, ...result.Suppliers]
			}
			const company = await request.updateOne(
				{
					RegistrationNumber: input?.RegistrationNumber,
				},
				{
					$set: { Suppliers: supplier },
				}
			)

			return { status: "done" }
		} else {
			return { status: "Not Found" }
		}
	} catch (err) {
		console.log(err)
		return { status: "error" }
	}
}
