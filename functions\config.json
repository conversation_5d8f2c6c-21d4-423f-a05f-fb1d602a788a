[{"name": "getTopSuppliersFunction", "private": false}, {"name": "getTransactionsFunction", "private": false, "can_evaluate": {}}, {"name": "GetScopeCategoryData", "private": false}, {"name": "createCompany", "private": false, "disable_arg_logs": true}, {"name": "getNaceFunction", "private": false, "can_evaluate": {}}, {"name": "getSuppliersList", "private": false}, {"name": "UpdateTransactionsAccountId", "private": false}, {"name": "getCompanyReport", "private": false, "disable_arg_logs": true}, {"name": "recalculateScope", "private": false, "disable_arg_logs": true}, {"name": "publishData", "private": false, "disable_arg_logs": true}, {"name": "updateCompanyInfo", "private": false, "disable_arg_logs": true}, {"name": "updateSupplierAmountAndEmission", "private": false, "disable_arg_logs": true}, {"name": "insertSupplierFunction", "private": true}, {"name": "updateOrganizationIdFunction", "private": false, "can_evaluate": {}}, {"name": "UpdateTransactionScopeFunction", "private": false, "can_evaluate": {}}, {"name": "updateUserFunction", "private": false, "can_evaluate": {}}, {"name": "getPublishedDataStatus", "private": false, "disable_arg_logs": true}, {"name": "insertUserFunction", "private": false, "can_evaluate": {}}, {"name": "getYearlyEmission", "private": false, "disable_arg_logs": true}, {"name": "insertTransactionFunction", "private": true, "can_evaluate": {}}, {"name": "setRevenueForYear", "private": false, "disable_arg_logs": true}, {"name": "getMonthlyEmissions", "private": false, "disable_arg_logs": true}, {"name": "GetTopSuppliers", "private": false}, {"name": "loginTriggerFunction", "private": true}, {"name": "updateLineFunction", "private": false, "can_evaluate": {}}, {"name": "rerunEmissionTransactionFunction", "private": false}, {"name": "updateSupplierFunction", "private": false, "can_evaluate": {}}, {"name": "updateMultipleTransactions", "private": false}, {"name": "insertNewSupplierFunction", "private": false, "can_evaluate": {}}, {"name": "getUserByNetlifyIdFunction", "private": false, "can_evaluate": {}}, {"name": "getlastUploadedTransactionDate", "private": false, "disable_arg_logs": true}, {"name": "getRevenueForYear", "private": false, "can_evaluate": {}}, {"name": "updateTransactionFunction", "private": false, "can_evaluate": {}}, {"name": "updateCompanyReport", "private": false, "disable_arg_logs": true}, {"name": "updateCompanyEmission", "private": false, "disable_arg_logs": true}, {"name": "getTriggerProgress", "private": false, "disable_arg_logs": true}, {"name": "getUserByOrganizationIdFunction", "private": false, "can_evaluate": {}}, {"name": "getScopeEmissionChartDataFunction", "private": false}, {"name": "getSuppliersFunction", "private": false, "can_evaluate": {}}, {"name": "getCompanyEmission", "private": false, "disable_arg_logs": true}, {"name": "ClassifyTransaction", "private": false}, {"name": "getCO2AddedDataFunction", "private": false}, {"name": "insertNewTransactionFunction", "private": false}, {"name": "RunAccountDataFunction", "private": false}, {"name": "getAnalysisOfCompany", "private": false, "disable_arg_logs": true}, {"name": "getProjectEmissions", "private": false, "disable_arg_logs": true}, {"name": "getProjectReport", "private": false, "disable_arg_logs": true}, {"name": "saveProjectReport", "private": false, "disable_arg_logs": true}, {"name": "deleteTransactions", "private": false, "disable_arg_logs": true}, {"name": "deleteSAFTFile", "private": false, "disable_arg_logs": true}, {"name": "getYearlyEmissionData", "private": false, "disable_arg_logs": true}, {"name": "createGroupingOfCompanies", "private": false, "disable_arg_logs": true}, {"name": "getIndustryCo2Intensity", "private": false, "disable_arg_logs": true}, {"name": "getEmissionsAndEnergyData", "private": false, "disable_arg_logs": true}, {"name": "pusherTest", "private": false, "disable_arg_logs": true}, {"name": "testFunction", "private": false}, {"name": "importReportData", "private": false, "disable_arg_logs": true}, {"name": "testTriggerForBatchUpdate", "private": true}]