// Global type declarations

declare global {
	interface Window {
		// Add any global window properties here
		gtag?: (...args: unknown[]) => void
		dataLayer?: unknown[]
	}

	// Environment variables
	namespace NodeJS {
		interface ProcessEnv {
			readonly NODE_ENV: "development" | "production" | "test"
			readonly VITE_CLERK_PUBLISHABLE_KEY: string
			readonly VITE_REALM_APP_ID: string
			readonly VITE_FIREBASE_API_KEY: string
			readonly VITE_FIREBASE_AUTH_DOMAIN: string
			readonly VITE_FIREBASE_PROJECT_ID: string
			readonly VITE_FIREBASE_STORAGE_BUCKET: string
			readonly VITE_FIREBASE_MESSAGING_SENDER_ID: string
			readonly VITE_FIREBASE_APP_ID: string
			readonly VITE_FIREBASE_MEASUREMENT_ID: string
		}
	}

	// Vitest globals
	const vi: typeof import("vitest").vi
	const describe: typeof import("vitest").describe
	const it: typeof import("vitest").it
	const expect: typeof import("vitest").expect
	const beforeEach: typeof import("vitest").beforeEach
	const afterEach: typeof import("vitest").afterEach
	const beforeAll: typeof import("vitest").beforeAll
	const afterAll: typeof import("vitest").afterAll
	const test: typeof import("vitest").test
}

// Module declarations for packages without types
declare module "*.svg" {
	const content: string
	export default content
}

declare module "*.png" {
	const content: string
	export default content
}

declare module "*.jpg" {
	const content: string
	export default content
}

declare module "*.jpeg" {
	const content: string
	export default content
}

declare module "*.gif" {
	const content: string
	export default content
}

declare module "*.webp" {
	const content: string
	export default content
}

declare module "*.ico" {
	const content: string
	export default content
}

declare module "*.css" {
	const classes: { readonly [key: string]: string }
	export default classes
}

// Utility types
export type Prettify<T> = {
	[K in keyof T]: T[K]
	// eslint-disable-next-line @typescript-eslint/no-empty-object-type
} & {}

export type NonEmptyArray<T> = [T, ...T[]]

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type RequiredKeys<T> = {
	// eslint-disable-next-line @typescript-eslint/no-empty-object-type
	[K in keyof T]-?: {} extends Pick<T, K> ? never : K
}[keyof T]

export type OptionalKeys<T> = {
	// eslint-disable-next-line @typescript-eslint/no-empty-object-type
	[K in keyof T]-?: {} extends Pick<T, K> ? K : never
}[keyof T]

export {}
