/**
 * Advanced Cache Manager for Request Deduplication and Intelligent Caching
 * Implements requirements 10.1 and 10.2 for efficient data management
 */

export interface CacheConfig {
	key: string
	ttl: number // Time to live in milliseconds
	staleWhileRevalidate: boolean
	maxAge: number
	tags: string[]
	priority?: "low" | "medium" | "high"
}

export interface CacheEntry<T = any> {
	data: T
	timestamp: number
	ttl: number
	tags: string[]
	accessCount: number
	lastAccessed: number
	priority: "low" | "medium" | "high"
	staleWhileRevalidate: boolean
}

export interface CacheStats {
	totalEntries: number
	totalSize: number
	hitRate: number
	missRate: number
	evictionCount: number
}

class CacheManager {
	private cache = new Map<string, CacheEntry>()
	private pendingRequests = new Map<string, Promise<any>>()
	private maxSize: number
	private stats: CacheStats = {
		totalEntries: 0,
		totalSize: 0,
		hitRate: 0,
		missRate: 0,
		evictionCount: 0,
	}
	private hitCount = 0
	private missCount = 0
	private evictionCount = 0

	constructor(maxSize = 100) {
		this.maxSize = maxSize
		this.startCleanupInterval()
	}

	/**
	 * Get data from cache with deduplication support
	 */
	async get<T>(
		key: string,
		fetcher?: () => Promise<T>,
		config?: Partial<CacheConfig>
	): Promise<T | null> {
		// Check if there's a pending request for this key (deduplication)
		if (this.pendingRequests.has(key)) {
			return this.pendingRequests.get(key)!
		}

		const entry = this.cache.get(key)
		const now = Date.now()

		// Cache hit - return fresh data
		if (entry && this.isEntryFresh(entry, now)) {
			entry.accessCount++
			entry.lastAccessed = now
			this.hitCount++
			this.updateStats()
			return entry.data
		}

		// If entry exists but is stale and no fetcher, return null for expired data
		if (entry && !fetcher && !this.isEntryFresh(entry, now)) {
			return null
		}

		// Cache miss or stale data
		this.missCount++
		this.updateStats()

		// If no fetcher provided, return stale data or null
		if (!fetcher) {
			return entry?.data || null
		}

		// Handle stale-while-revalidate
		if (entry?.staleWhileRevalidate && entry.data) {
			// Return stale data immediately and revalidate in background
			this.revalidateInBackground(key, fetcher, config)
			entry.accessCount++
			entry.lastAccessed = now
			return entry.data
		}

		// Fetch fresh data with deduplication
		const promise = this.fetchAndCache(key, fetcher, config)
		this.pendingRequests.set(key, promise)

		try {
			const result = await promise
			return result
		} finally {
			this.pendingRequests.delete(key)
		}
	}

	/**
	 * Set data in cache with configuration
	 */
	set<T>(key: string, data: T, config?: Partial<CacheConfig>): void {
		const now = Date.now()
		const defaultConfig: CacheConfig = {
			key,
			ttl: 5 * 60 * 1000, // 5 minutes default
			staleWhileRevalidate: true,
			maxAge: 30 * 60 * 1000, // 30 minutes max age
			tags: [],
			priority: "medium",
		}

		const finalConfig = { ...defaultConfig, ...config }

		const entry: CacheEntry<T> = {
			data,
			timestamp: now,
			ttl: finalConfig.ttl,
			tags: finalConfig.tags,
			accessCount: 1,
			lastAccessed: now,
			priority: finalConfig.priority!,
			staleWhileRevalidate: finalConfig.staleWhileRevalidate,
		}

		// Ensure cache size limit
		this.ensureCacheSize()

		this.cache.set(key, entry)
		this.updateStats()
	}

	/**
	 * Invalidate cache entries by tags (intelligent invalidation)
	 */
	invalidateByTags(tags: string[]): void {
		const keysToDelete: string[] = []

		for (const [key, entry] of this.cache.entries()) {
			if (entry.tags.some(tag => tags.includes(tag))) {
				keysToDelete.push(key)
			}
		}

		keysToDelete.forEach(key => {
			this.cache.delete(key)
			this.pendingRequests.delete(key)
		})

		this.updateStats()
	}

	/**
	 * Invalidate specific cache entry
	 */
	invalidate(key: string): void {
		this.cache.delete(key)
		this.pendingRequests.delete(key)
		this.updateStats()
	}

	/**
	 * Clear all cache entries
	 */
	clear(): void {
		this.cache.clear()
		this.pendingRequests.clear()
		this.hitCount = 0
		this.missCount = 0
		this.evictionCount = 0
		this.updateStats()
	}

	/**
	 * Warm cache with critical data
	 */
	async warmCache(
		warmupStrategies: Array<{
			key: string
			fetcher: () => Promise<any>
			config?: Partial<CacheConfig>
		}>
	): Promise<void> {
		const promises = warmupStrategies.map(async ({ key, fetcher, config }) => {
			try {
				const data = await fetcher()
				this.set(key, data, { ...config, priority: "high" })
			} catch (error) {
				console.warn(`Cache warming failed for key: ${key}`, error)
			}
		})

		await Promise.allSettled(promises)
	}

	/**
	 * Get cache statistics
	 */
	getStats(): CacheStats {
		return { ...this.stats }
	}

	/**
	 * Get cache entries for debugging
	 */
	getEntries(): Array<{ key: string; entry: CacheEntry }> {
		return Array.from(this.cache.entries()).map(([key, entry]) => ({ key, entry }))
	}

	private async fetchAndCache<T>(
		key: string,
		fetcher: () => Promise<T>,
		config?: Partial<CacheConfig>
	): Promise<T> {
		try {
			const data = await fetcher()
			this.set(key, data, config)
			return data
		} catch (error) {
			// Remove failed request from pending
			this.pendingRequests.delete(key)
			throw error
		}
	}

	private async revalidateInBackground<T>(
		key: string,
		fetcher: () => Promise<T>,
		config?: Partial<CacheConfig>
	): Promise<void> {
		try {
			const data = await fetcher()
			this.set(key, data, config)
		} catch (error) {
			console.warn(`Background revalidation failed for key: ${key}`, error)
		}
	}

	private isEntryFresh(entry: CacheEntry, now: number): boolean {
		return now - entry.timestamp < entry.ttl
	}

	private isEntryExpired(entry: CacheEntry, now: number): boolean {
		return now - entry.timestamp > entry.ttl * 2 // Consider expired after 2x TTL
	}

	private ensureCacheSize(): void {
		if (this.cache.size >= this.maxSize) {
			this.evictLeastUsed()
		}
	}

	private evictLeastUsed(): void {
		const entries = Array.from(this.cache.entries())

		// Sort by priority (low first), then by access count and last accessed time
		entries.sort(([, a], [, b]) => {
			const priorityOrder = { low: 0, medium: 1, high: 2 }

			if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
				return priorityOrder[a.priority] - priorityOrder[b.priority]
			}

			if (a.accessCount !== b.accessCount) {
				return a.accessCount - b.accessCount
			}

			return a.lastAccessed - b.lastAccessed
		})

		// Remove the least used entries (25% of cache size)
		const toRemove = Math.max(1, Math.floor(this.maxSize * 0.25))

		for (let i = 0; i < toRemove && i < entries.length; i++) {
			const [key] = entries[i]
			this.cache.delete(key)
			this.evictionCount++
		}

		this.updateStats()
	}

	private startCleanupInterval(): void {
		// Clean up expired entries every 5 minutes
		setInterval(
			() => {
				this.cleanupExpiredEntries()
			},
			5 * 60 * 1000
		)
	}

	private cleanupExpiredEntries(): void {
		const now = Date.now()
		const keysToDelete: string[] = []

		for (const [key, entry] of this.cache.entries()) {
			if (this.isEntryExpired(entry, now)) {
				keysToDelete.push(key)
			}
		}

		keysToDelete.forEach(key => {
			this.cache.delete(key)
			this.evictionCount++
		})

		if (keysToDelete.length > 0) {
			this.updateStats()
		}
	}

	private updateStats(): void {
		const totalRequests = this.hitCount + this.missCount

		this.stats = {
			totalEntries: this.cache.size,
			totalSize: this.cache.size, // Simplified size calculation
			hitRate: totalRequests > 0 ? (this.hitCount / totalRequests) * 100 : 0,
			missRate: totalRequests > 0 ? (this.missCount / totalRequests) * 100 : 0,
			evictionCount: this.evictionCount,
		}
	}
}

// Global cache manager instance
export const cacheManager = new CacheManager(200) // Increased size for production use

// Cache tag constants for intelligent invalidation
export const CACHE_TAGS = {
	TRANSACTIONS: "transactions",
	SUPPLIERS: "suppliers",
	ORGANIZATIONS: "organizations",
	DASHBOARD: "dashboard",
	ANALYSIS: "analysis",
	NACE: "nace",
	SETTINGS: "settings",
	USER: "user",
} as const

export type CacheTag = (typeof CACHE_TAGS)[keyof typeof CACHE_TAGS]
