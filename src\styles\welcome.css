.headerTitle {
	font-size: 1.9rem;
}

.subHeaderTitle {
	font-size: 1.6rem;
}

.descriptionText {
	font-size: 1.3rem;
}

.descContainer {
	background-color: #ffffff;
}

.dragDropContainer {
	border-style: dashed;
}

.upload-btn-wrapper {
	position: relative;
	overflow: hidden;
	display: inline-block;
}

.fileUploadContainer {
	display: inline-block;
	position: relative;
	width: 100%;
	height: 30vh;
	min-height: 290px;
	overflow: hidden;
}

.fileUploadContain {
	border: dashed grey 4px;
	background-color: rgba(255, 255, 255, 0.8);
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 10;
}

.fileUpload {
	position: absolute;
	top: 10%;
	right: 0;
	left: 0;
	text-align: center;
	color: #505050;
}

.dropText {
	font-size: 1.4rem;
	font-weight: 500;
}

input[type="file"]::file-selector-button {
	padding: 0.2rem 0.4rem;
	border-radius: 0.2rem;
	font-size: 1.1rem;
	background-color: #7b8ca8;
	border: none;
	outline: none;
	cursor: pointer;
	color: #ffffff;
	white-space: pre-wrap;
	font: unset;
}

.uploadBtn {
	padding: 0.1rem 0.5rem;
	border-radius: 0.2rem;
	font-size: 1rem;
	background-color: #277ea7;
	border: none;
	outline: none;
	cursor: pointer;
	color: #ffffff;
}

.disabled {
	cursor: not-allowed;
	background-color: #7b8ca8;
}

.fileInput + input {
	font-size: unset;
}

.fileInputTag {
	display: none !important;
}

.uploadImg {
	width: 150px;
	cursor: pointer;
}
