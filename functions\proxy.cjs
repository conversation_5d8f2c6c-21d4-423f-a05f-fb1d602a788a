const axios = require("axios")

exports.handler = async function (event) {
	try {
		const { RegistrationNumber } = JSON.parse(event.body)

		const response = await axios.get(`https://data.brreg.no/regnskapsregisteret/regnskap/${RegistrationNumber}`)
		if (response.status === 200) {
			return {
				statusCode: 200,
				body: JSON.stringify(response.data),
				headers: {
					"Content-Type": "application/json",
				},
			}
		}
	} catch (error) {
		return {
			statusCode: 500,
			body: JSON.stringify({ error: "Unable to fetch data" }),
			headers: {
				"Content-Type": "application/json",
			},
		}
	}
}
