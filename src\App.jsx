/* eslint-disable import/namespace */
import "./App.css"
import { <PERSON><PERSON><PERSON><PERSON>, SignedIn, SignedOut, RedirectToSignIn } from "@clerk/clerk-react"
import { QueryClientProvider } from "@tanstack/react-query"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import { EnhancedQueryClient } from "./services/query-cache-integration"
import { Chart, ArcElement } from "chart.js"
import React from "react"
import { BrowserRouter, Routes, Route } from "react-router-dom"
//import "bootstrap/dist/css/bootstrap.min.css"

import RealmApolloProvider from "./graphql/realmApolloClient"
import { RealmAppProvider } from "./realm/RealmAppProvider"

import "react-toastify/dist/ReactToastify.css"

const DefaultLayout = React.lazy(() => import("./pages/shared/Layout"))

Chart.register(ArcElement)

function App() {
	const appId = import.meta.env.VITE_REALM_APP_ID
	const publishableKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY
	// Create enhanced client with intelligent caching
	const queryClient = new EnhancedQueryClient({
		defaultOptions: {
			logger: {
				warn: console.warn,
				error: console.error,
			},
			queries: {
				retry: 2, // Enable retries for better reliability
				retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
				refetchOnWindowFocus: false,
				refetchOnMount: false,
				staleTime: 5 * 60 * 1000, // 5 minutes default stale time
				gcTime: 10 * 60 * 1000, // 10 minutes garbage collection time
			},
			mutations: {
				retry: 1,
				retryDelay: 1000,
			},
		},
	})
	return (
		<RealmAppProvider appId={appId}>
			<RealmApolloProvider>
				<QueryClientProvider client={queryClient}>
					<ClerkProvider domain="clerk.accounts.dev" publishableKey={publishableKey}>
						<BrowserRouter>
							<SignedIn>
								<Routes>
									<Route path="*" element={<DefaultLayout />} />
								</Routes>
							</SignedIn>
							<SignedOut>
								<RedirectToSignIn />
							</SignedOut>
						</BrowserRouter>
						<ReactQueryDevtools initialIsOpen={false} />
					</ClerkProvider>
				</QueryClientProvider>
			</RealmApolloProvider>
		</RealmAppProvider>
	)
}

export default App
