/**
 * Cache Initialization Service
 * Handles cache setup and warming on application startup
 */

import { useEffect, useRef } from "react"
import { useSelector } from "react-redux"
import { cacheWarmingService } from "./cache-warming"
import { cacheManager, CACHE_TAGS } from "./cache-manager"
import { EnhancedQueryClient } from "./query-cache-integration"

export interface CacheInitializationOptions {
	enableWarming?: boolean
	enableDeduplication?: boolean
	enableIntelligentInvalidation?: boolean
	warmingDelay?: number
	debugMode?: boolean
}

class CacheInitializationService {
	private initialized = false
	private warmingInProgress = false

	/**
	 * Initialize cache system with configuration
	 */
	async initialize(
		queryClient: EnhancedQueryClient,
		options: CacheInitializationOptions = {}
	): Promise<void> {
		if (this.initialized) return

		const {
			enableWarming = true,
			enableDeduplication = true,
			enableIntelligentInvalidation = true,
			warmingDelay = 1000,
			debugMode = process.env.NODE_ENV === "development",
		} = options

		// Set up cache configuration
		this.setupCacheConfiguration()

		// Set up intelligent invalidation listeners
		if (enableIntelligentInvalidation) {
			this.setupInvalidationListeners(queryClient)
		}

		// Log initialization in debug mode
		if (debugMode) {
			console.log("Cache system initialized with options:", {
				enableWarming,
				enableDeduplication,
				enableIntelligentInvalidation,
				warmingDelay,
			})
		}

		this.initialized = true
	}

	/**
	 * Warm cache for specific organization
	 */
	async warmCacheForOrganization(
		app: any,
		currentOrganization: any,
		options: { immediate?: boolean; debugMode?: boolean } = {}
	): Promise<void> {
		if (this.warmingInProgress || !currentOrganization) return

		const { immediate = false, debugMode = false } = options

		this.warmingInProgress = true

		try {
			if (debugMode) {
				console.log(
					"Starting cache warming for organization:",
					currentOrganization.RegistrationNumber
				)
			}

			// Warm critical data first
			const criticalStats = await cacheWarmingService.warmCriticalData(app, currentOrganization)

			if (debugMode) {
				console.log("Critical data warming completed:", criticalStats)
			}

			// Wait a bit before warming important data (unless immediate)
			if (!immediate) {
				await new Promise(resolve => setTimeout(resolve, 2000))
			}

			// Warm important data
			const importantStats = await cacheWarmingService.warmImportantData(app, currentOrganization)

			if (debugMode) {
				console.log("Important data warming completed:", importantStats)
			}

			// Wait before warming background data (unless immediate)
			if (!immediate) {
				await new Promise(resolve => setTimeout(resolve, 5000))
			}

			// Warm background data
			const backgroundStats = await cacheWarmingService.warmBackgroundData(app, currentOrganization)

			if (debugMode) {
				console.log("Background data warming completed:", backgroundStats)
				console.log("Total cache warming completed")
			}
		} catch (error) {
			console.error("Cache warming failed:", error)
		} finally {
			this.warmingInProgress = false
		}
	}

	/**
	 * Handle organization change
	 */
	async handleOrganizationChange(
		app: any,
		newOrganization: any,
		oldOrganization: any
	): Promise<void> {
		// Clear organization-specific cache when switching
		if (oldOrganization && newOrganization?.id !== oldOrganization.id) {
			this.clearOrganizationCache(oldOrganization)
		}

		// Warm cache for new organization
		if (newOrganization) {
			await this.warmCacheForOrganization(app, newOrganization, { immediate: true })
		}
	}

	/**
	 * Clear cache for specific organization
	 */
	clearOrganizationCache(organization: any): void {
		if (!organization?.RegistrationNumber) return

		const registrationNumber = organization.RegistrationNumber

		// Get all cache entries
		const entries = cacheManager.getEntries()

		// Find entries related to this organization
		const keysToInvalidate = entries
			.filter(({ key }) => key.includes(registrationNumber))
			.map(({ key }) => key)

		// Invalidate organization-specific entries
		keysToInvalidate.forEach(key => {
			cacheManager.invalidate(key)
		})

		// Also invalidate by tags
		cacheManager.invalidateByTags([
			CACHE_TAGS.TRANSACTIONS,
			CACHE_TAGS.SUPPLIERS,
			CACHE_TAGS.DASHBOARD,
			CACHE_TAGS.ANALYSIS,
		])
	}

	/**
	 * Get initialization status
	 */
	isInitialized(): boolean {
		return this.initialized
	}

	/**
	 * Get warming status
	 */
	isWarmingInProgress(): boolean {
		return this.warmingInProgress
	}

	private setupCacheConfiguration(): void {
		// Configure cache size limits based on environment
		const isProduction = process.env.NODE_ENV === "production"
		const maxCacheSize = isProduction ? 300 : 100

		// Cache configuration is handled in cache-manager constructor
		// This could be extended for more dynamic configuration
	}

	private setupInvalidationListeners(queryClient: EnhancedQueryClient): void {
		// Set up listeners for data mutations that should trigger cache invalidation
		// This would typically be integrated with your mutation hooks

		// Example: Listen for transaction updates
		const originalInvalidateQueries = queryClient.invalidateQueries.bind(queryClient)

		queryClient.invalidateQueries = function (filters?: any) {
			// Custom logic for intelligent invalidation could go here
			return originalInvalidateQueries(filters)
		}
	}
}

// Global cache initialization service
export const cacheInitializationService = new CacheInitializationService()

/**
 * React hook for cache initialization
 */
export function useCacheInitialization(
	app: any,
	queryClient: EnhancedQueryClient,
	options: CacheInitializationOptions = {}
) {
	const currentOrganization = useSelector((state: any) => state.user.currentOrganization)
	const initializationRef = useRef(false)
	const organizationRef = useRef(currentOrganization)

	// Initialize cache system once
	useEffect(() => {
		if (!initializationRef.current) {
			initializationRef.current = true
			cacheInitializationService.initialize(queryClient, options)
		}
	}, [queryClient, options])

	// Handle organization changes and warming
	useEffect(() => {
		const previousOrganization = organizationRef.current
		organizationRef.current = currentOrganization

		if (app && currentOrganization) {
			// Handle organization change
			if (previousOrganization?.id !== currentOrganization.id) {
				cacheInitializationService.handleOrganizationChange(
					app,
					currentOrganization,
					previousOrganization
				)
			}
			// Initial warming for first organization load
			else if (!previousOrganization && currentOrganization) {
				// Delay initial warming to allow app to settle
				setTimeout(() => {
					cacheInitializationService.warmCacheForOrganization(app, currentOrganization, {
						debugMode: options.debugMode,
					})
				}, options.warmingDelay || 1000)
			}
		}
	}, [app, currentOrganization, options.debugMode, options.warmingDelay])

	return {
		isInitialized: cacheInitializationService.isInitialized(),
		isWarmingInProgress: cacheInitializationService.isWarmingInProgress(),
		warmCache: (immediate = false) => {
			if (app && currentOrganization) {
				return cacheInitializationService.warmCacheForOrganization(app, currentOrganization, {
					immediate,
					debugMode: options.debugMode,
				})
			}
			return Promise.resolve()
		},
		clearOrganizationCache: () => {
			if (currentOrganization) {
				cacheInitializationService.clearOrganizationCache(currentOrganization)
			}
		},
	}
}
