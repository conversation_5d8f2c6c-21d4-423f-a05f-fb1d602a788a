exports = async function({ companyOwnRegistrationNumber, accessRegistrationNumber }) {
  // Validate input
  if (!companyOwnRegistrationNumber || !accessRegistrationNumber) {
    console.error("Missing required registration numbers.");
    return { error: true, message: "Both companyOwnRegistrationNumber and accessRegistrationNumber are required.", statusCode: 400 }; // Bad Request
  }
   if (companyOwnRegistrationNumber === accessRegistrationNumber) {
    console.error("Own registration number and access registration number cannot be the same.");
    return { error: true, message: "Company cannot grant access to itself in this manner.", statusCode: 400 }; // Bad Request
  }

  const companyCollection = context.services
    .get("mongodb-atlas")
    .db(context.environment.values.database)
    .collection("company");

  try {
    // 1. Check if the company to give access to exists
    const accessCompany = await companyCollection.findOne({ RegistrationNumber: accessRegistrationNumber });
    if (!accessCompany) {
      console.error(`Company to grant access to not found: ${accessRegistrationNumber}`);
      return { error: true, message: `Company with registration number ${accessRegistrationNumber} not found.`, statusCode: 404 };
    }

    // 2. Grouping Logic - Ensure both numbers are in the access company's grouping
    // Note: We are not verifying the existence of companyOwnRegistrationNumber as per requirement.
    let updateOperation;
    const numbersToAdd = [companyOwnRegistrationNumber, accessRegistrationNumber];

    // Check the accessCompany's grouping field
    if (accessCompany.grouping && Array.isArray(accessCompany.grouping)) {
      // Grouping array exists in accessCompany, add ONLY the companyOwnRegistrationNumber if it's not already there
      console.log(`Grouping found for access company ${accessRegistrationNumber}. Ensuring ${companyOwnRegistrationNumber} is present.`);
      updateOperation = {
        // Only add the 'own' number if the group already exists
        $addToSet: { grouping: companyOwnRegistrationNumber }
      };
    } else {
      // Grouping array does not exist or is not an array in accessCompany, create it with both numbers
      console.log(`No grouping found for access company ${accessRegistrationNumber}. Creating new grouping with ${companyOwnRegistrationNumber} and ${accessRegistrationNumber}.`);
      // Use $set to ensure the array contains exactly these two initially
      updateOperation = {
        $set: { grouping: numbersToAdd } // numbersToAdd still contains both [own, access]
      };
    }

    // 3. Update the access company's document
    const updateResult = await companyCollection.updateOne(
      { _id: accessCompany._id }, // Target the accessCompany's _id
      updateOperation
    );

    // Check if the update was successful
    if (updateResult.acknowledged) {
       console.log(`Successfully processed grouping update for access company ${accessRegistrationNumber}.`);
       return { success: true, message: "Grouping updated successfully in access company." };
    } else {
       // This case indicates a failure in the update command itself
       console.error(`Failed to update grouping for access company ${accessRegistrationNumber}. Update result:`, updateResult);
       return { error: true, message: "Failed to update access company grouping.", statusCode: 500 }; // Internal Server Error
    }

  } catch (error) {
    console.error("Error in createGroupingOfCompanies function:", error);
    return { error: true, message: `An internal error occurred: ${error.message}`, statusCode: 500 };
  }
};