# Development Setup Documentation

## TypeScript Configuration

The project is now configured with strict TypeScript settings:

- **Strict mode enabled** with additional strict checks
- **Enhanced type checking** including `noUncheckedIndexedAccess` and `noPropertyAccessFromIndexSignature`
- **Path aliases** configured for `@/*` imports
- **Proper type definitions** for environment variables and global types

## ESLint Configuration

Enhanced ESLint setup with:

- **TypeScript-specific rules** with `@typescript-eslint/recommended`
- **React and accessibility rules** with proper configuration
- **Import ordering** and resolution rules
- **Stricter code quality rules** including `prefer-const`, `no-console`, etc.
- **Separate rules for test files** with relaxed restrictions

## Prettier Configuration

Consistent code formatting with:

- **Tabs for indentation** (2 spaces)
- **No semicolons** and **double quotes**
- **100 character line width**
- **Proper line endings** (LF)

## Vitest Testing Framework

Comprehensive testing setup:

- **Vitest** as the primary testing framework
- **React Testing Library** for component testing
- **Enhanced test utilities** with custom render functions
- **Coverage reporting** with v8 provider
- **Test setup file** with proper mocks for browser APIs
- **Coverage thresholds** set to 80% for all metrics

## Pre-commit Hooks

Quality assurance with Husky:

- **Automatic linting** and formatting on commit
- **Type checking** to prevent TypeScript errors
- **Staged file processing** with lint-staged
- **Consistent code quality** enforcement

## Available Scripts

```bash
# Development
npm start                 # Start development server
npm run build            # Build for production
npm run serve            # Preview production build

# Code Quality
npm run lint             # Run ESLint
npm run lint:fix         # Fix ESLint issues
npm run format           # Format code with Prettier
npm run format:check     # Check formatting
npm run type-check       # Run TypeScript type checking

# Testing
npm test                 # Run tests in watch mode
npm run test:run         # Run tests once
npm run test:coverage    # Run tests with coverage
npm run test:ui          # Run tests with UI

# Pre-commit
npm run pre-commit       # Run pre-commit checks manually
```

## File Structure

```
src/
├── types/
│   ├── global.d.ts      # Global type declarations
│   └── index.ts         # Application-specific types
├── test/
│   ├── setup.ts         # Test setup and mocks
│   ├── utils.tsx        # Test utilities and helpers
│   └── example.test.ts  # Example test file
└── ...
```

## Type Safety

The project now includes:

- **Comprehensive type definitions** for all major interfaces
- **Utility types** for common patterns
- **Environment variable types** with proper validation
- **Test utilities** with proper TypeScript support

## Next Steps

1. **Migrate existing components** to use proper TypeScript types
2. **Add unit tests** for critical components and utilities
3. **Fix existing linting issues** in the codebase
4. **Implement error boundaries** and proper error handling
5. **Add integration tests** for key user workflows

## Troubleshooting

### Common Issues

1. **Import resolution errors**: Ensure paths are correctly configured in `tsconfig.json`
2. **Type errors**: Use the `@types/*` packages for third-party libraries
3. **Test failures**: Check that test setup is properly imported
4. **Pre-commit failures**: Run `npm run lint:fix` and `npm run format` manually

### Performance

- **Bundle analysis**: Use `npm run build` and analyze the output
- **Test performance**: Use `npm run test:coverage` to identify slow tests
- **Type checking**: Use `npm run type-check` for faster type validation
