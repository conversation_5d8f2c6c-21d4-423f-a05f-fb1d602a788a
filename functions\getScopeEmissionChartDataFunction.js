exports = async function (input) {
  try {
    //const { RegistrationNumber, year } = input;

    let query = {
      RegistrationNumber: input.RegistrationNumber,
      PeriodYear: input.year,
    };
    if (input.AnalysisIDs) {
      query["Analysis"] = {
        $elemMatch: {
          AnalysisID: {
            $in: [...input.AnalysisIDs],
          },
        },
      };
    }

    const commonMatch = {
      $match: query,
    };

    const scopeDataQuery = context.services
      .get("mongodb-atlas")
      .db(context.environment.values.database)
      .collection("transaction")
      .aggregate([
        {
          ...commonMatch,
        },
        {
          $facet: {
            scopeData: [
              {
                $group: {
                  _id: undefined,
                  Scope_1: { $sum: "$Scope_1" },
                  Scope_2: { $sum: "$Scope_2" },
                  Scope_3: { $sum: "$Scope_3" },
                  marketBased: { $sum: "$marketBased" },
                  locationBased: { $sum: "$locationBased" },
                  consumptionBased: { $sum: "$consumptionBased" },
                },
              },
            ],
            categoriesData: [
              {
                $group: {
                  _id: "$Scope_3_Category",
                  Emission: { $sum: "$Scope_3" },
                },
              },
            ],
          },
        },
      ])
      .toArray();

   

    const [Scope_Data] = await Promise.all([
      scopeDataQuery,
      //companyEmissionQuery,
    ]);

    const data = Scope_Data[0];

    let { scopeData, categoriesData } = data;

    let downStreamEmission = 0;


    categoriesData?.forEach((category) => {
      if (category?._id > 8) {
        downStreamEmission += category.Emission;
      }
    });


    if (downStreamEmission > 0) {
      scopeData[0]["scope3IncludingDownStream"] = scopeData[0].Scope_3;
      scopeData[0].Scope_3 -= downStreamEmission;
    }


    if (input.AnalysisIDs && scopeData.length > 0) {
      return {
        success: true,
        scope: scopeData[0],
      };
    }


    if (scopeData.length === 0) {
      const emptyResult =
        {
          Scope_1: 0.0,
          Scope_2: 0.0,
          Scope_3: 0.0,
        }
      
      return {
        success: true,
        scope: emptyResult,
        companyName: "",
       // yearlyEmissionData,
      };
    }

    return {
      success: true,
      scope: scopeData[0],
      //yearlyEmissionData,
    };
  } catch (err) {
    console.error(err);
  }
  //return { success: false }
};
