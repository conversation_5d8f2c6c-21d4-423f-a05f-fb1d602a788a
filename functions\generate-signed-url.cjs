const uploadHandler = require("../src/server/upload-handler.cjs")

exports.handler = async (event) => {
	if (event.httpMethod !== "POST") {
		return {
			statusCode: 405,
			body: JSON.stringify({ error: "Method Not Allowed" }),
			headers: { "Content-Type": "application/json" },
		}
	}

	let requestBody
	try {
		requestBody = JSON.parse(event.body)
	} catch (error) {
		return {
			statusCode: 400,
			body: JSON.stringify({ error: "Invalid JSON body" }),
			headers: { "Content-Type": "application/json" },
		}
	}

	try {
		// Call `generateSignedUrl` and return response
		const response = await uploadHandler.generateSignedUrl(requestBody)

		return {
			statusCode: 200,
			body: JSON.stringify(response),
			headers: { "Content-Type": "application/json" },
		}
	} catch (error) {
		console.error("Error in Netlify function:", error)
		return {
			statusCode: 500,
			body: JSON.stringify({ error: "Internal Server Error", details: error.message }),
			headers: { "Content-Type": "application/json" },
		}
	}
}
