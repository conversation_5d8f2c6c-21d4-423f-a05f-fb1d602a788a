import { ref, uploadBytesResumable, getDownloadURL } from "firebase/storage"
import React, { useRef } from "react"
import { useDispatch } from "react-redux"

import Button from "../../../components/ui/Button"
import TextInputEdit from "../../../components/ui/TextInputEdit"
import { useRealmApp } from "../../../realm/RealmAppProvider"
import { currentOrganizationAction } from "../../../store/actions/UserAction"
import { dynamicStorage } from "../../firebase/firebase"
import useFullPageLoader from "../../loader/useFullPageLoader"

export default function Company({ loginUser, t, openAddCompanyModal }) {
	const selectFile = useRef()
	//const [logo,setLogo] = useState('')
	//const [progress, setProgress] = useState(0)
	const [loader, showLoader, hideLoader] = useFullPageLoader()
	const app = useRealmApp()
	const dispatch = useDispatch()

	const updateCompany = async (key, value) => {
		try {
			// Call the API to update company info
			await app.updateCompanyInfo(loginUser.RegistrationNumber, { [key]: value })

			// Clone the loginUser object to avoid directly modifying it
			let newData = { ...loginUser }

			// Check the provided key to determine which property to update
			if (key.startsWith("ContactPerson.")) {
				const contactKey = key.replace("ContactPerson.", "") // Remove "ContactPerson." prefix

				// Update ContactPerson properties
				newData.company.ContactPerson[contactKey] = value
			} else {
				// Update other properties
				newData.company[key] = value
			}

			// Dispatch the updated data
			dispatch(currentOrganizationAction(newData))
		} catch (error) {
			// handle error
		}
	}

	const handleFileUploading = async e => {
		try {
			showLoader()
			const storage = dynamicStorage("gs://report-images-dev")
			const storageRef = ref(storage, `/images/${e.name}`)
			const uploadTask = uploadBytesResumable(storageRef, e)

			uploadTask.on(
				"state_changed",

				() => {
					getDownloadURL(uploadTask.snapshot.ref).then(async fireBaseUrl => {
						app.updateCompanyInfo(loginUser.RegistrationNumber, { Logo: fireBaseUrl })
						let newData = { ...loginUser }

						newData.company["Logo"] = fireBaseUrl

						dispatch(currentOrganizationAction(newData))

						hideLoader()
					})
				}
			)
		} catch (error) {
			hideLoader()
		}
	}

	return (
		<div className="cl-component cl-user-profile">
			<input
				type="file"
				className="hidden"
				ref={selectFile}
				onChange={e => {
					handleFileUploading(e.target.files[0])
				}}
			/>
			<div className="cl-main mt-4">
				<div className="cl-user-profile-card cl-themed-card">
					<div className="cl-titled-card-list">
						<h4 className="font-extrabold text-2xl">{t("Company")}</h4>
						{loginUser?.RegistrationNumber ? (
							<>
								<div className="grid grid-cols-1 gap-4 sm:grid-cols-2 mb-3">
									<TextInputEdit
										label={t("common:CompanyName")}
										value={loginUser?.company?.Name}
										handleChange={value => updateCompany("Name", value)}
									/>
									<TextInputEdit
										label={t("common:RegistrationNumber")}
										value={loginUser?.RegistrationNumber || ""}
										disable={true}
									/>
									<TextInputEdit
										label={t("common:NaceCode")}
										value={loginUser?.company?.NaceCode || ""}
										handleChange={value => updateCompany("NaceCode", value)}
									/>

									<TextInputEdit
										label={t("common:Industry")}
										value={loginUser?.company?.Industry || ""}
										handleChange={value => updateCompany("Industry", value)}
									/>
								</div>

								<h4 className="font-extrabold text-lg mt-4">{t("CompanyLogo")}</h4>
								<div className="grid gap-4 items-center grid-cols-[200px_minmax(100px,_1fr)]">
									{loginUser?.company?.Logo ? (
										<>
											<div>
												<img className="max-h-40" src={loginUser?.company?.Logo} alt="Not Found" />
											</div>
											<div>
												<Button
													handleClick={() => selectFile.current.click()}
													title={t("Change")}
												/>
											</div>
										</>
									) : (
										<div
											onClick={() => {
												selectFile.current.click()
											}}
											aria-hidden
											className="w-48 h-24 flex flex-col justify-center items-center border-2 border-dashed border-black ml-3 cursor-pointer"
										>
											<h5 className="text-sm cursor-pointer">{"Select Logo"}</h5>
										</div>
									)}
								</div>
								<h4 className="font-extrabold text-lg mt-4">{t("common:ContactPerson")}</h4>
								<div className="grid grid-cols-1 gap-4 sm:grid-cols-2 mb-3">
									<TextInputEdit
										label={t("common:FirstName")}
										value={loginUser?.company?.ContactPerson.FirstName}
										handleChange={value => updateCompany("ContactPerson.FirstName", value)}
									/>
									<TextInputEdit
										label={t("common:LastName")}
										value={loginUser?.company?.ContactPerson.LastName}
										handleChange={value => updateCompany("ContactPerson.LastName", value)}
									/>

									<TextInputEdit
										label={t("common:Email")}
										value={loginUser?.company?.ContactPerson?.Email || ""}
										handleChange={value => updateCompany("ContactPerson.Email", value)}
									/>

									<TextInputEdit
										label={t("common:Telephone")}
										value={loginUser?.company?.ContactPerson?.PhoneNumber || ""}
										handleChange={value => updateCompany("ContactPerson.PhoneNumber", value)}
									/>
								</div>
							</>
						) : (
							<>
								<p className="font-normal mb-0">{t("no_company_reg_with_account")}</p>
								<div className="w-full">
									<div className="flex justify-end">
										<Button
											handleClick={() => openAddCompanyModal()}
											title={t("RegisterCompany")}
										/>
									</div>
								</div>
							</>
						)}
					</div>
				</div>
			</div>
			{loader}
		</div>
	)
}
