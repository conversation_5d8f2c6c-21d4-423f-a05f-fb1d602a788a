exports = async function (input) {
	try {
		const { RegistrationNumber } = input

		// get comapny emission from co2-intensities-dev table

		const companyEmission = await context.services
			.get("mongodb-atlas")
			.db("co2-intensities-dev")
			.collection("companies")
			.findOne({ RegistrationNumber: RegistrationNumber }, { Emissions: 1 })

		const transactionRequest = context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("transaction")

		// get the distinct years from transaction table so that we can show user
		// published and unpublished status of data for only those years which data
		// he has
		const transactionsPeriodYears = await transactionRequest.distinct("PeriodYear", {
			RegistrationNumber: RegistrationNumber,
		})

		// get min and max years from transactionsPeriodYears
		const minYear = Math.min(...transactionsPeriodYears.map(Number))
		const maxYear = Math.max(...transactionsPeriodYears.map(Number))

		const { Emissions } = companyEmission

		const data = []

		// loop from min year to max years to status of all those years
		// whose data user has

		for (let year = minYear; year <= maxYear; year++) {
			const { locked, updatedAt, co2Intensity } = Emissions[year] || {} // Ensure it doesn't break if year data is missing

			let formattedDate = "--/--/--" // Default value if `updatedAt` is missing

			if (locked) {
				const dateObj = new Date(updatedAt)
				formattedDate = `${dateObj.getFullYear()}/${dateObj.getMonth() + 1}/${dateObj.getDate()}`
			}

			data.push({
				year,
				published: !!locked, // Ensure `locked` is always a boolean
				updatedAt: formattedDate,
				co2Intensity,
			})
		}

		return { success: true, data }
	} catch (error) {
		console.log(error)
		return { success: false, error: error.message }
	}
}
