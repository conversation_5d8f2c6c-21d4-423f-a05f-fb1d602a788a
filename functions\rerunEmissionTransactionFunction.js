//;
// new accountData 02/03/2022
let accountData = {}

/* ------------------------ Populate data into trasaction using account Id ------------------------ */
const fillData = async (transaction) => {
	const extraData = await context.functions.execute("ClassifyTransaction", {
		transaction,
	})
	return extraData
}

const transformTransaction1 = async (transaction, accountID, AccountDescription) => {
	transaction.AccountID = accountID.toString()
	transaction.AccountDescription = AccountDescription

	const extraData = await fillData(transaction)

	transaction.NaceCode = extraData.NaceCode || transaction.NaceCode || ""
	transaction.Status = extraData?.Status || transaction?.Status || 0
	transaction.DescriptionDetails = extraData.DescriptionDetails || ""
	transaction.Scope = extraData.Scope || 3
	transaction.Scope_3_Category = extraData.Scope_3_Category || 1
	transaction.Notes = extraData.Notes || ""
	transaction.Relation = extraData.Relation || ""
	transaction.Type = extraData.Type || ""

	let Amount = transaction.Amount

	let Scope_1 = 0.0
	let Scope_2 = 0.0
	let Scope_3 = 0.0
	let liter = 0.0
	let kwh = 0.0
	let non_renewables = 0.0
	let renewables = 0.0
	let mobile = 0.0
	let stationaryCombustion = 0.0
	let marketBased = null
	let locationBased = null
	let consumptionBased = null
	let nuclear = null
	let Status = transaction.Status

	if (extraData.function) {
		// console.log("HELLO IN IF TESTING")
		const emissionData = context.functions.execute("RunAccountDataFunction", {
			functionName: extraData.function,
			Amount: Amount,
			period: transaction.Period,
			year: transaction.PeriodYear,
			AccountID: transaction.AccountID,
		})

		Scope_1 = emissionData.scope_1
		Scope_2 = emissionData.scope_2
		Scope_3 = emissionData.scope_3
		liter = emissionData.liter ? emissionData.liter : liter
		kwh = parseFloat(emissionData.kwh ? emissionData.kwh : kwh)
		marketBased = emissionData?.market_based ? parseFloat(emissionData?.market_based) : 0
		locationBased = emissionData?.location_based ? parseFloat(emissionData?.location_based) : 0
		consumptionBased = emissionData?.consumption_based ? parseFloat(emissionData?.consumption_based) : 0
		non_renewables = parseFloat(emissionData.non_renewables ? emissionData.non_renewables : non_renewables)
		renewables = parseFloat(emissionData.renewables ? emissionData.renewables : renewables)
		mobile = emissionData.mobile ? emissionData.mobile : 0.0
		stationaryCombustion = emissionData.stationaryCombustion ? emissionData.stationaryCombustion : 0.0
		nuclear = emissionData.nuclear ? emissionData.nuclear : 0.0
	} else {

		const year = transaction.PeriodYear

		let supplierFactor = transaction.supplierFactor

		let factor

		if (typeof supplierFactor == "number") {
			factor = supplierFactor || 19
		} else {
			factor = supplierFactor[year] || 19
		}
		

		let tco2e = (Amount * factor) / 1000 / 1000
		Status = 2

		switch (transaction.Scope) {
			case 1:
				Scope_1 = tco2e
				break
			case 2:
				Scope_2 = tco2e
				break
			case 3:
				Scope_3 = tco2e
				break
		}
	}

	// Update the transaction object in database.
	try {
		const updateData = {
			AccountID: transaction.AccountID,
			Scope_1: Scope_1,
			Scope_2: Scope_2,
			Scope_3: Scope_3,
			liter: liter,
			kwh: kwh,
			AccountDescription,
			renewable_energy: renewables,
			non_renewable_energy: non_renewables,
			stationaryCombustion,
			mobileCombustion: mobile,
			Status: Status,
			Relation: transaction.Relation,
			Type: transaction.Type,
		}

		// These 2 ifs are to hedge for nulls. Is Scope missing from the transaction?
		if (transaction.Scope != "") {
			updateData["Scope"] = extraData.Scope === 0 ? 0 : transaction.Scope
		}
		if (transaction.Scope_3_Category != "") {
			updateData.Scope_3_Category = transaction.Scope_3_Category
		}
		
			if (consumptionBased && marketBased && locationBased && nuclear && updateData["Scope"] === 2) {
				updateData.consumptionBased = consumptionBased
				updateData.locationBased = locationBased
				updateData.marketBased = marketBased
				updateData.nuclear = nuclear ? nuclear : 0.0
			}
			else{
					updateData.consumptionBased = 0.0
					updateData.locationBased = 0.0
					updateData.marketBased = 0.0
					updateData.nuclear = 0.0
					updateData.renewable_energy = 0.0
					updateData.non_renewable_energy = 0.0
					updateData.stationaryCombustion = 0.0
					updateData.mobileCombustion = 0.0
					updateData.liter= liter
					updateData.kwh = kwh
			}
		const request = context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("transaction")
		const result = await request.updateOne(
			{
				_id: transaction._id,
			},
			{
				$set: updateData,
			}
		)
		return { ...updateData, _id: transaction._id }
	} catch (err) {
		console.log(err)
		//console.error("Error in updating transaction:", err)
	}
}

exports = async function (input) {
	accountData = context.values.get("AccountData")
	let accountID = input.AccountID
	const AccountDescription = input.AccountDescription
	const request = context.services
		.get("mongodb-atlas")
		.db(context.environment.values.database)
		.collection("transaction")
	let ids = []
	//let ids2 = JSON.parse(input.ids);
	for (let i = 0; i < input.ids.length; i++) {
		ids.push(new BSON.ObjectId(input.ids[i]))
	}
	let transactions = await request.find({ _id: { $in: ids } }).toArray()
	// Go over all the transaction and transform the transaction.
	let responseTransation = []
	for (let i = 0; i < transactions.length; i++) {
		let transaction = await transformTransaction1(transactions[i], accountID, AccountDescription)
		if (transaction) {
			transaction._id = transaction._id.toString()
			responseTransation.push(transaction)
		}
	}
	return responseTransation
}
