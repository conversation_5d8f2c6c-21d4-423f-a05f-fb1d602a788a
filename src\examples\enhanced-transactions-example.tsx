/**
 * Example: Enhanced Transactions Component with Advanced Caching
 * Demonstrates how to integrate the new caching system into existing components
 */

import React, { useEffect, useState } from "react"
import { useSelector } from "react-redux"
import { useRealmApp } from "../realm/RealmAppProvider"
import { useEnhancedTransactions, useEnhancedSuppliers } from "../hooks/enhanced/useEnhancedQuery"
import { useCacheInitialization } from "../services/cache-initialization"
import { useCacheWarming } from "../services/cache-warming"
import { useCacheMonitor } from "../components/debug/CacheMonitor"
import { cacheManager, CACHE_TAGS } from "../services/cache-manager"
import { EnhancedQueryClient } from "../services/query-cache-integration"
import { useQueryClient } from "@tanstack/react-query"

interface EnhancedTransactionsProps {
	// Component props
}

export const EnhancedTransactionsExample: React.FC<EnhancedTransactionsProps> = () => {
	const app = useRealmApp()
	const queryClient = useQueryClient() as EnhancedQueryClient
	const currentOrganization = useSelector((state: any) => state.user.currentOrganization)
	const [currentYear] = useState(new Date().getFullYear())

	// Initialize cache system
	const { isInitialized, isWarmingInProgress, warmCache, clearOrganizationCache } =
		useCacheInitialization(app, queryClient, {
			enableWarming: true,
			enableDeduplication: true,
			enableIntelligentInvalidation: true,
			debugMode: process.env.NODE_ENV === "development",
		})

	// Cache warming utilities
	const {
		warmCriticalData,
		warmImportantData,
		warmBackgroundData,
		getWarmingHistory,
		getCurrentlyWarming,
	} = useCacheWarming()

	// Cache monitor for debugging
	const { isOpen, toggleMonitor, CacheMonitor } = useCacheMonitor()

	// Enhanced data fetching with caching
	const transactionsQuery = useEnhancedTransactions(app, {
		RegistrationNumber: currentOrganization?.RegistrationNumber,
		Scope: -1,
		year: currentYear,
	})

	const suppliersQuery = useEnhancedSuppliers(app, {
		RegistrationNumber: currentOrganization?.RegistrationNumber,
		year: currentYear,
	})

	// Handle manual cache operations
	const handleWarmCache = async () => {
		if (currentOrganization) {
			await warmCache(true) // immediate warming
		}
	}

	const handleClearCache = () => {
		clearOrganizationCache()
		// Also invalidate React Query cache
		queryClient.invalidateQueriesWithRelationships([CACHE_TAGS.TRANSACTIONS, CACHE_TAGS.SUPPLIERS])
	}

	const handleInvalidateTransactions = () => {
		// Intelligent invalidation - will also invalidate related data
		queryClient.invalidateQueriesWithRelationships([CACHE_TAGS.TRANSACTIONS])
	}

	// Get cache statistics
	const cacheStats = cacheManager.getStats()
	const warmingHistory = getWarmingHistory()
	const currentlyWarming = getCurrentlyWarming()

	return (
		<div className="p-6">
			<div className="mb-6">
				<h1 className="text-2xl font-bold mb-4">Enhanced Transactions with Advanced Caching</h1>

				{/* Cache Status Panel */}
				<div className="bg-gray-50 p-4 rounded-lg mb-6">
					<h2 className="text-lg font-semibold mb-3">Cache Status</h2>
					<div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
						<div className="bg-white p-3 rounded shadow">
							<div className="text-sm text-gray-600">Initialized</div>
							<div className="text-lg font-semibold">{isInitialized ? "✅ Yes" : "❌ No"}</div>
						</div>
						<div className="bg-white p-3 rounded shadow">
							<div className="text-sm text-gray-600">Warming</div>
							<div className="text-lg font-semibold">
								{isWarmingInProgress ? "🔄 In Progress" : "✅ Complete"}
							</div>
						</div>
						<div className="bg-white p-3 rounded shadow">
							<div className="text-sm text-gray-600">Hit Rate</div>
							<div className="text-lg font-semibold">{cacheStats.hitRate.toFixed(1)}%</div>
						</div>
						<div className="bg-white p-3 rounded shadow">
							<div className="text-sm text-gray-600">Cache Entries</div>
							<div className="text-lg font-semibold">{cacheStats.totalEntries}</div>
						</div>
					</div>

					{/* Cache Controls */}
					<div className="flex flex-wrap gap-2">
						<button
							onClick={handleWarmCache}
							disabled={isWarmingInProgress}
							className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
						>
							{isWarmingInProgress ? "Warming..." : "Warm Cache"}
						</button>
						<button
							onClick={handleClearCache}
							className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
						>
							Clear Cache
						</button>
						<button
							onClick={handleInvalidateTransactions}
							className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
						>
							Invalidate Transactions
						</button>
						<button
							onClick={toggleMonitor}
							className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
						>
							{isOpen ? "Close" : "Open"} Cache Monitor
						</button>
					</div>
				</div>

				{/* Currently Warming Indicator */}
				{currentlyWarming.length > 0 && (
					<div className="bg-blue-50 border border-blue-200 p-4 rounded-lg mb-6">
						<h3 className="font-semibold text-blue-800 mb-2">Currently Warming Cache:</h3>
						<ul className="text-sm text-blue-700">
							{currentlyWarming.map(key => (
								<li key={key} className="flex items-center">
									<div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-2"></div>
									{key}
								</li>
							))}
						</ul>
					</div>
				)}
			</div>

			{/* Data Display */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Transactions */}
				<div className="bg-white p-6 rounded-lg shadow">
					<h2 className="text-xl font-semibold mb-4">Transactions</h2>
					{transactionsQuery.isLoading ? (
						<div className="flex items-center">
							<div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
							Loading transactions...
						</div>
					) : transactionsQuery.error ? (
						<div className="text-red-600">
							Error loading transactions: {transactionsQuery.error.message}
						</div>
					) : (
						<div>
							<p className="text-gray-600 mb-2">
								Loaded {transactionsQuery.data?.length || 0} transactions
							</p>
							<div className="text-sm text-gray-500">
								Cache Status: {transactionsQuery.isStale ? "Stale" : "Fresh"} | Background Updating:{" "}
								{transactionsQuery.isFetching ? "Yes" : "No"}
							</div>
							{/* Add your transaction display logic here */}
						</div>
					)}
				</div>

				{/* Suppliers */}
				<div className="bg-white p-6 rounded-lg shadow">
					<h2 className="text-xl font-semibold mb-4">Suppliers</h2>
					{suppliersQuery.isLoading ? (
						<div className="flex items-center">
							<div className="animate-spin rounded-full h-5 w-5 border-b-2 border-green-600 mr-2"></div>
							Loading suppliers...
						</div>
					) : suppliersQuery.error ? (
						<div className="text-red-600">
							Error loading suppliers: {suppliersQuery.error.message}
						</div>
					) : (
						<div>
							<p className="text-gray-600 mb-2">
								Loaded {suppliersQuery.data?.length || 0} suppliers
							</p>
							<div className="text-sm text-gray-500">
								Cache Status: {suppliersQuery.isStale ? "Stale" : "Fresh"} | Background Updating:{" "}
								{suppliersQuery.isFetching ? "Yes" : "No"}
							</div>
							{/* Add your supplier display logic here */}
						</div>
					)}
				</div>
			</div>

			{/* Cache Monitor */}
			<CacheMonitor />

			{/* Development Info */}
			{process.env.NODE_ENV === "development" && (
				<div className="mt-8 bg-gray-100 p-4 rounded-lg">
					<h3 className="font-semibold mb-2">Development Info</h3>
					<div className="text-sm space-y-1">
						<div>Cache Entries: {cacheStats.totalEntries}</div>
						<div>Hit Rate: {cacheStats.hitRate.toFixed(2)}%</div>
						<div>Miss Rate: {cacheStats.missRate.toFixed(2)}%</div>
						<div>Evictions: {cacheStats.evictionCount}</div>
						<div>Warming History: {warmingHistory.size} entries</div>
					</div>
				</div>
			)}
		</div>
	)
}

/**
 * Example of how to replace existing useQuery hooks
 */

// BEFORE (using regular React Query):
/*
const transactionsQuery = useQuery({
	queryKey: ['transactions', input],
	queryFn: () => app.getTransactions(input),
	enabled: input !== null,
	refetchOnMount: true,
	retry: 5,
})
*/

// AFTER (using enhanced caching):
/*
const transactionsQuery = useEnhancedTransactions(app, input)

// Additional benefits:
// - Automatic request deduplication
// - Intelligent cache invalidation
// - Background cache warming
// - Stale-while-revalidate support
// - Cache size management
// - Performance monitoring
*/

/**
 * Example of manual cache operations
 */
export const CacheOperationsExample = () => {
	const queryClient = useQueryClient() as EnhancedQueryClient

	const handleDataUpdate = async () => {
		// After updating transaction data, intelligently invalidate related caches
		await queryClient.invalidateQueriesWithRelationships([CACHE_TAGS.TRANSACTIONS])
		// This will also invalidate dashboard and analysis data automatically
	}

	const handleSupplierUpdate = async () => {
		// After updating supplier data
		await queryClient.invalidateQueriesWithRelationships([CACHE_TAGS.SUPPLIERS])
		// This will also invalidate related transaction and dashboard data
	}

	const handleOrganizationSwitch = () => {
		// When switching organizations, clear all organization-specific data
		cacheManager.invalidateByTags([
			CACHE_TAGS.TRANSACTIONS,
			CACHE_TAGS.SUPPLIERS,
			CACHE_TAGS.DASHBOARD,
			CACHE_TAGS.ANALYSIS,
		])
	}

	return null // This is just an example
}
