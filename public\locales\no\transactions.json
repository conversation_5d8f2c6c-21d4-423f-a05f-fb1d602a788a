{"continue": "FORTSETT", "cancel": "AVBRYT", "deletion_warning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "apply_filter": "Bruk filter", "clear_filter": "<PERSON><PERSON><PERSON> filter", "projects": "Prosjekter", "departments": "<PERSON><PERSON><PERSON><PERSON>", "confirm_delete_message": "Er du sikker på at du vil slette dette?", "account_id_updated_message": "Konto-ID oppdatert vellykket.", "error_updating_account_id_message": "<PERSON><PERSON><PERSON>, vi har en feil under oppdatering av konto-ID.", "transactions_updated_message": "Transaksjoner er oppdatert vellykket.", "error_updating_multiple_transactions_message": "Feil under oppdatering av flere transaksjoner.", "supplier_empty_message": "Lev<PERSON><PERSON><PERSON><PERSON> bør ikke være tom, vennligst legg til transaksjonen igjen.", "amount_empty_message": "Beløpet bør ikke være tomt, vennligst legg til transaksjonen igjen eller skriv inn 0.", "transaction_updated_message": "Transaksjonen er oppdatert vellykket.", "error_updating_transaction_message": "<PERSON><PERSON><PERSON>, vi har et problem med å oppdatere transaksjonen.", "no_transaction_selected_message": "Ingen transaksjon er valgt.", "rows_copied_message": "Valgte rader er kopiert til utklippstavlen.", "transaction_amount_missing_message": "Det er en valgt transaksjon der beløpet ikke er tilgjengelig.", "transactions_deleted_message": "Transaksjoner slettet vellykket.", "error_deleting_transactions_message": "<PERSON><PERSON><PERSON>, vi har en feil under sletting av transaksjoner.", "error_adding_transaction_message": "<PERSON><PERSON><PERSON>, vi har en feil under tillegg av ny transaksjon.", "transaction_added_message": "Transaksjon lagt til vellykket.", "error_updating_message": "<PERSON><PERSON><PERSON>, vi har en feil under opp<PERSON><PERSON>.", "emission_updated_message": "<PERSON><PERSON><PERSON><PERSON> oppdatert vellykket.", "Category8Info": "Dette er for å beregne energi og utslipp dersom du ikke betaler energiregninger og rapportere om dette i scope 1 og scope 2", "Distanse": "Avstand", "Size": "<PERSON><PERSON><PERSON><PERSON>", "People": "<PERSON><PERSON><PERSON>", "TravelMode": "<PERSON>ise<PERSON><PERSON>", "Drivetrain": "Drivverk", "addTransactionHeading": "Legg til ny post", "update_multiple_transactions": "<PERSON><PERSON><PERSON><PERSON> flere <PERSON>aks<PERSON>", "update_account_id": "Oppdater konto-ID", "delete_records": "<PERSON><PERSON> poster", "multiple_transactions_heading": "Rediger flere transaksjoner", "Yearly": "Årlig", "Monthly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Quarterly": "<PERSON><PERSON><PERSON><PERSON>", "Barnehage": "<PERSON><PERSON><PERSON>", "Kontor": "<PERSON><PERSON><PERSON>", "Skole": "Skole", "Universitet": "Universitet", "Sykehus": "Sykehus", "Sykehjem": "Sykeh<PERSON><PERSON>", "Hotell": "Hotell", "Idrettsbygg": "Idrettsbygg", "Forretning": "Forretning", "Kulturbygning": "Kulturbygning", "Lett industri": "Lett industri", "ControlledTooltip": "En kontrollert bygning/eiendel er en du har operasjonell kontroll over og derfor er ansvarlig for energiforbruket og rapporterer på dette i scope 1 eller 2.", "Scope 3 Category": "Scope 3 Kategori", "Supplier_list_empty": "Leverandørlisten er tom - vennligst legg til en leverandør først.", "add_supplier": "<PERSON>gg til leverandør", "Amount_zero": "Beløp skal ikke være 0.", "Scope_3_Category": "Scope 3 kategori", "EmissionsFromElectricitytooltip": "For selskap som kjøper strøm med opprinnelsesgaranti(OG) om fornybar strøm så er faktoren for det satt til '0'. For selskap som ikke har slike så anbefales det å vise til NVEs årlige beregning av varedeklarasjon for strømleverandører. Du kan også bruke 'Europeisk mix' i beregningen. Du kan også endre faktoren som brukes, men da må det opplyses om det i din rapport.", "InputEmmissionManually": "Legg inn utslipp manuelt", "SelectEmission": "Utslippsfaktor som skal brukes", "Or": "Eller", "MultipleEdit": "Du redigerer flere transaksjoner. <PERSON><PERSON><PERSON> vil gjelde for alle transaksjoner som er valgt. Scope 1, 2, and 3 vil bli fordelt over de valgte transaksjonene.", "Add": "Legg til", "Edit": "<PERSON><PERSON>", "Delete": "<PERSON><PERSON>", "LineNumber": "<PERSON><PERSON> #", "RecordID": "FøringsId", "AccountID": "<PERSON><PERSON><PERSON><PERSON>", "SupplierID": "Leverandør-Id", "TransactionID": "Transaksjons-Id", "Description": "Beskrivelse", "CreditAmount": "<PERSON><PERSON><PERSON>bel<PERSON><PERSON>", "DebitAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ReferenceNumber": "Referanse", "SupplierName": "Leverandørnavn", "Emissions-kgCo2e": "<PERSON><PERSON>lipp (kgCo2e)", "Status": "Status", "AccountDescription": "Kontobeskrivelse", "Period": "<PERSON><PERSON><PERSON>", "PeriodYear": "<PERSON><PERSON>", "TransactionDate": "Transaksjonsdato", "NaceCode": "<PERSON><PERSON> kode", "Notes": "Notater", "RedFlagMsg": "<PERSON>ne trans<PERSON>en er merket som rødt. Du bør trolig redigere detaljene her.", "EditDetails": "REDIGER DETALJER", "EditTransactionId": "Rediger transaksjons-Id", "EmissionsForThisTransaction": "<PERSON><PERSON><PERSON><PERSON> for denne transaksjonen", "Scope": "<PERSON><PERSON>", "Scope1": "Scope 1", "Scope2": "Scope 2", "Scope3": "Scope 3", "Emissions-Co2e": "<PERSON><PERSON><PERSON><PERSON>(Co2e)", "kg": "kg", "Total": "Totalt", "EmissionsFromCombustion": "<PERSON><PERSON><PERSON><PERSON> fra for<PERSON>nning", "EmissionsFromVehicles": "<PERSON><PERSON><PERSON><PERSON> fra kjøretøy", "ProcessAndFugitiveEmissions": "Prosess og flyktige utslipp", "OtherScope1Emissions": "Andre scope 1 utslipp", "EmissionsFromElectricity": "Utslipp fra elektrisitet", "EmissionsFromHeat": "<PERSON><PERSON><PERSON><PERSON> fra varme", "OtherScope2Emissions": "Andre scope 2 utslipp", "Category1-PurchasedGoodsAndServices": "Kategori 1 - Innkjøpte varer og tjenester", "Category2-CapitalGoods": "Kategori 2 - <PERSON><PERSON><PERSON><PERSON><PERSON>", "Category3-FuelAndEnergyRelatedActivities": "Kategori 3 - Drivstoff og energirelaterte aktiviteter", "Category4-UpstreamTransportationAndDistribution": "Kategori 4 - Oppstrøms transport og distribusjon", "Category5-WasteGeneratedInOperations": "Kategori 5 - Av<PERSON> fra produksjon", "Category6-BusinessTravel": "<PERSON><PERSON><PERSON> 6 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Category7-EmployeeCommuting": "Kategori 7 - <PERSON><PERSON> for an<PERSON><PERSON>", "Category8-UpstreamLeasedAssets": "Kategori 8 - Oppstrøms leasing", "Category9-DownstreamTransportationandDistribution": "Kategori 9 - Nedstrøms Transport og Distribusjon", "Category10-ProcessingofSoldProducts": "Kategori 10 - Prosessering av Solgte Produkter", "Category11-UseofSoldProducts": "Kategori 11 - Bruk av Solgte Produkter", "Category12-EndofLifeTreatmentofSoldProducts": "Kategori 12 - Sluttbehandling av Solgte Produkter", "Category13-DownstreamLeasedAssets": "Kategori 13 - Nedstrøms Leasing/utleie av lokaler og utstyr", "Category14-Franchises": "Kategori 14 - <PERSON><PERSON><PERSON>ser", "Category15-Investments": "Kategori 15 - <PERSON><PERSON><PERSON><PERSON>", "Save": "Lagre", "Cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Metric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Factor": "<PERSON><PERSON><PERSON>", "Unit": "<PERSON><PERSON><PERSON>", "Kg-Co2e": "Kg Co2e", "Units": "Enheter", "OtherEmissionSources": "<PERSON>", "Gram": "Gram", "Co2e": "Co2e", "kwh": "kwh", "TotalCo2e": "Totalt Co2e", "VehicleType": "Kjøretøystype", "FuelType": "<PERSON><PERSON><PERSON><PERSON>", "Distance": "<PERSON><PERSON><PERSON>", "Economy": "Økonomi", "BasicInput": "Basis Input", "Pcs": "stk", "TransportType": "Transportmåte", "Weight": "Vekt", "MaterialType": "Materialtype", "RecycledShare": "<PERSON><PERSON><PERSON><PERSON><PERSON> andel", "Flights": "<PERSON><PERSON><PERSON>", "LandTransport": "Landtransport", "Accommodation": "<PERSON><PERSON><PERSON>", "AccommodationType": "Oppholdstype", "FlightType": "Flytype", "From": "<PERSON>a", "Destination": "Destinasjon", "FlightClass": "Klass<PERSON>", "Traveller": "Antal<PERSON> reisende", "SearchAirport": "<PERSON><PERSON><PERSON> flyplass", "AddRadiativeFactor": "Legg til strålingsfaktor", "BuildingType": "Bygningstype", "Yes": "<PERSON>a", "No": "<PERSON><PERSON>", "Country": "Land", "Number": "<PERSON><PERSON><PERSON>", "Nights": "<PERSON><PERSON>", "kgs": "kg", "tkm": "tkm", "Scope3Category": "Scope 3 Kategori", "EmissionSourceRegistration": "Aktivitetsbasert registrering", "PurchaseWithKnownSupplierAmount": "Kostnadsbasert registrering", "TransactionWithKnownEmission": "Registrere et kjent utslipp", "ProductionRelatedPurchase": "Produksjonsrelatert innkjøp", "NonProductionRelatedPurchase": "Ikke produksjonsrelatert innkjøp", "Recalculation": "Rekalkulering", "RecalculationMsg": "Scope 1, 2 og 3 ovenfor må være 0 for å rekalkulere", "SupplierFactor": "Leverandø<PERSON><PERSON> faktor", "CustomEmissionFactor": "Custom utslippsfaktor", "NaceCode-Industry": "Nacekode - Industri", "KGPer1000NOK": "Kg per 1000 NOK", "SearchByIndustryOrNaceCode": "<PERSON><PERSON><PERSON> etter industri eller Nace kode", "TabExplainer Scope 1": "Her kan du redigere scope 1 - direkte utslipp. Forbrenning i produksjon og fra egne transportmidler samt prosess- og flyktige utslipp. Prosessutslipp er avgasssing fra produksjonsprosesser som må måles spesielt. Flyktige utslipp er som regel lekkasjer fra kjølemedier", "TabExplainer Scope 2": "Her kan du redigere scope 2 - indirekte utslipp fra innkjøpt energi. Strøm og fjernvarme faller inn under scope 2. Energi til kjøling må også tas med her.", "TabExplainer Scope 3": "Her kan du redigere scope 3 - andre indirekte utslipp. Det deles her opp i de 8 oppstrømskategoriene fra GHG-protokollen. Når du skal legge til ansattes pendling må du først legge til en ny transaksjon og så legge det inn her under kategori 7.", "Strøm med opprinnelsesgaranti": "Strøm med opprinnelsesgaranti", "Norsk residual mix(uten OG)": "Norsk residual mix(uten OG)", "European Mix": "Europeisk miks", "Other": "<PERSON><PERSON> faktor", "discription_error": "Beskrivelse kan ikke være tom.", "factor_unit_greater_0_error": "Faktor og enhet må være større enn null.", "factor_unit_weight_greater_0_error": "<PERSON><PERSON><PERSON>, enheter eller vekt per enhet kan ikke være null, tom eller negativ.", "transport_type_error": "Vennligst velg en transporttype.", "factor_weight_distance_greater_0_error": "<PERSON><PERSON><PERSON>, vekt og avstand kan ikke være tom, null eller et negativt tall.", "vehicle_type_error": "Vennligst velg en kjøretøytype.", "fuel_type_error": "Vennligst velg en drivstofftype.", "distance_error": "Avstand kan ikke være negativ eller tom.", "economy_heavy_truck_error": "Økonomi kan ikke være tom for tung lastebil.", "economy_negative_error": "Økonomi kan ikke være negativ.", "material_type_error": "Vennligst velg en materialtype.", "weight_error": "Vekt kan ikke være negativ eller tom.", "recycled_share_error": "<PERSON><PERSON><PERSON><PERSON><PERSON> andel må være mellom 0 og 100.", "from_airport_error": "Vennligst velg avreiseflyplass.", "destination_airport_error": "Vennligst velg destinasjonsflyplass.", "traveller_count_error": "Antall reisende må være større enn 0.", "travel_mode_error": "Vennligst velg en reisemåte.", "select_type_error": "Vennligst velg en type.", "drivetrain_error": "Vennligst velg et drivverk.", "people_count_error": "<PERSON><PERSON><PERSON>er må være større enn 0.", "accommodation_type_error": "Vennligst velg en overnattingstype.", "country_error": "Vennligst velg et land.", "number_nights_error": "<PERSON><PERSON>l netter må være større enn 0.", "building_type_error": "Vennligst velg en bygningstype.", "period_type_error": "Vennligst velg en type (Årlig/Månedlig/<PERSON>).", "size_error": "St<PERSON>rrelse kan ikke være negativ eller tom.", "material_level3_disposal_error": "Vennligst velg materialtype, nivå 3 og avhendingsmetode.", "factor_weight_non_negative_error": "Faktor og vekt må være ikke-negative tall.", "flight_details_error": "Kategori 6 fly<PERSON><PERSON><PERSON> fra, destinasjon eller reisende kan ikke være tomme.", "select_all_fields": "Vennligst fyll ut alle feltene", "select_drive_train": "Vennligst velg drivverk", "category_8_error": "<PERSON><PERSON><PERSON> utslippsfaktor eller størrelse kan ikke være tom eller null.", "scope_error": "Vennligst legg til minst én scope verdi", "vehicle_distance_error": "Kjøretøyavstand kan ikke være tom.", "electricity_error": "Elektrisitetsfaktor, enhet og periode kan ikke være tomme.", "factor_distance_error": "Faktor og avstand kan ikke være tom eller null.", "heat_error": "Varmefak<PERSON>, enhet og periode kan ikke være tom eller null.", "editDetails": "Rediger detalj av", "ElectricTransportation": "Elektrisk transport", "ExcelExport": "Excel Eksport", "Copy": "<PERSON><PERSON><PERSON>", "Filter": "<PERSON><PERSON><PERSON>"}