.emissions {
	background-color: #ffffff;
	min-height: 60px;
}

#scope-container {
	/*overflow-y: scroll;*/
}

#three {
	/*overflow-y: scroll;*/
}

.scope-container {
	flex: 1;
}

.scope {
	background-color: #ffffff;
	min-height: 70px;
	width: 100%;
}

.activity {
	background-color: rgba(86, 204, 242, 0.1);
	min-height: 80px;
	width: 100%;
}

.scope-title {
	font-weight: 700;
}

.activity-title {
	font-weight: 600;
}

.activity-details-title {
	font-weight: 700;
	font-size: 16px;
	word-wrap: break-word;
	line-height: 1.2;
}

.activity-details-value {
	font-size: 12px;
}

.activity-details-span-value {
	font-size: 12px;
	color: #3ba3db;
}

.activity-details-link {
	cursor: pointer;
}

.emissions-title {
	color: #505050;
	font-weight: 600;
	font-size: 16px;
}

.emissions-value {
	font-weight: 600;
	font-size: 22px;
}

.emissions-details-title {
	color: #505050;
	font-weight: 700;
	font-size: 14px;
}

.emissions-details-value {
	font-weight: 700;
	font-size: 18px;
}

.scope-details-title {
	color: #505050;
	font-weight: 700;
	font-size: 14px;
	word-wrap: break-word;
	line-height: 1.2;
}

.scope-details-value {
	font-weight: 700;
	font-size: 14px;
}

.scope-percentage {
	font-size: 14px;
	font-weight: 700;
}

.table-title {
	font-weight: 700;
}

table.table-bordered {
	border: 1px solid #b9b9b9;
	margin-top: 20px;
	padding: 0px 2px;
}

table.table-bordered > thead > tr > th {
	border: 1px solid #b9b9b9;
	padding: 0px 2px;
	color: #505050;
	font-weight: 600;
}

table.table-bordered > tbody > tr > td {
	border: 1px solid #b9b9b9;
	padding: 0px 2px;
}

table.table-bordered > tfoot > tr > th {
	border: 1px solid #b9b9b9;
	padding: 0px 2px;
	color: #505050;
	font-weight: 600;
}

.custom-td {
	border: 0 !important;
	display: table-cell;
	padding: 2rem 0 !important;
}

.custom-td .customSpinner {
	display: block;
	width: 1.8rem !important;
	height: 1.8rem !important;
	margin-left: auto;
	margin-right: auto;
}

.add-employee-transaction-spinner {
	width: 18px !important;
	height: 18px !important;
}

.emissions-charts {
	width: 80px;
	height: 80px;
}

.pie-chart-container {
	max-width: 350px !important;
	max-height: 220px !important;
}

.bar-chart-container {
	margin-left: 0 !important;
	margin-right: 0 !important;
}

.doughnut-chart-container {
	position: relative;
}

.doughnut-chart-text {
	position: absolute;
	font-size: 14px;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -33%);
}

.my-custom-legend > div {
	/* This will apply the flex-col style to the div */
	flex-direction: column;
}
