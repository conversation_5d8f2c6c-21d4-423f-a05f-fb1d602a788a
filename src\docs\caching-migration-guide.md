# Caching System Migration Guide

This guide explains how to migrate existing components to use the new advanced caching system with request deduplication and intelligent cache invalidation.

## Overview

The new caching system provides:

- **Request Deduplication**: Prevents duplicate concurrent requests
- **Intelligent Cache Invalidation**: Automatically invalidates related data
- **Cache Warming**: Preloads critical data for better performance
- **Cache Size Management**: Automatically manages memory usage
- **Stale-While-Revalidate**: Returns cached data while fetching fresh data in background

## Quick Migration Steps

### 1. Update App.jsx

Replace the standard QueryClient with EnhancedQueryClient:

```jsx
// BEFORE
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			retry: false,
			refetchOnWindowFocus: false,
		},
	},
})

// AFTER
import { QueryClientProvider } from "@tanstack/react-query"
import { EnhancedQueryClient } from "./services/query-cache-integration"

const queryClient = new EnhancedQueryClient({
	defaultOptions: {
		queries: {
			retry: 2,
			retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
			refetchOnWindowFocus: false,
			staleTime: 5 * 60 * 1000, // 5 minutes
			gcTime: 10 * 60 * 1000, // 10 minutes
		},
	},
})
```

### 2. Replace useQuery Hooks

#### For Transactions:

```jsx
// BEFORE
import useGetTransactions from "../hooks/transactions/useGetTransactions"

const transactions = useGetTransactions(app, {
	RegistrationNumber: currentOrganization?.RegistrationNumber,
	Scope: -1,
	year: currentYear,
})

// AFTER
import { useEnhancedTransactions } from "../hooks/enhanced/useEnhancedQuery"

const transactions = useEnhancedTransactions(app, {
	RegistrationNumber: currentOrganization?.RegistrationNumber,
	Scope: -1,
	year: currentYear,
})
```

#### For Suppliers:

```jsx
// BEFORE
import useGetSuppliers from "../hooks/suppliers/useGetSuppliers"

const suppliers = useGetSuppliers(app, {
	RegistrationNumber: currentOrganization?.RegistrationNumber,
	year: currentYear,
})

// AFTER
import { useEnhancedSuppliers } from "../hooks/enhanced/useEnhancedQuery"

const suppliers = useEnhancedSuppliers(app, {
	RegistrationNumber: currentOrganization?.RegistrationNumber,
	year: currentYear,
})
```

### 3. Add Cache Initialization

Add cache initialization to your main layout component:

```jsx
import { useCacheInitialization } from "../services/cache-initialization"
import { useQueryClient } from "@tanstack/react-query"

const Layout = () => {
	const app = useRealmApp()
	const queryClient = useQueryClient()

	// Initialize caching system
	const { isInitialized, warmCache } = useCacheInitialization(app, queryClient, {
		enableWarming: true,
		enableDeduplication: true,
		enableIntelligentInvalidation: true,
		debugMode: process.env.NODE_ENV === "development",
	})

	// Rest of your component...
}
```

### 4. Update Data Mutations

When updating data, use intelligent invalidation:

```jsx
import { CACHE_TAGS } from "../services/cache-manager"
import { useQueryClient } from "@tanstack/react-query"

const MyComponent = () => {
	const queryClient = useQueryClient()

	const handleTransactionUpdate = async () => {
		// Update transaction data...

		// Intelligently invalidate related caches
		await queryClient.invalidateQueriesWithRelationships([CACHE_TAGS.TRANSACTIONS])
		// This automatically invalidates dashboard, analysis, and supplier data too
	}

	const handleSupplierUpdate = async () => {
		// Update supplier data...

		await queryClient.invalidateQueriesWithRelationships([CACHE_TAGS.SUPPLIERS])
	}
}
```

## Component-Specific Migration

### Transactions.jsx

Key changes needed:

1. **Replace useQuery hooks**:

```jsx
// Replace these imports
import useGetTransactions from "../../hooks/transactions/useGetTransactions"
import useGetSuppliers from "../../hooks/suppliers/useGetSuppliers"

// With these
import {
	useEnhancedTransactions,
	useEnhancedSuppliers,
} from "../../hooks/enhanced/useEnhancedQuery"
```

2. **Update data fetching**:

```jsx
// BEFORE
const transactions = useGetTransactions(app, {
	RegistrationNumber,
	Scope: -1,
	year: reportingYear,
})

// AFTER
const transactions = useEnhancedTransactions(app, {
	RegistrationNumber,
	Scope: -1,
	year: reportingYear,
})
```

3. **Replace manual cache operations**:

```jsx
// BEFORE
suppliersData.refetch()

// AFTER
queryClient.invalidateQueriesWithRelationships([CACHE_TAGS.SUPPLIERS])
```

### Suppliers.jsx

1. **Update supplier queries**:

```jsx
// BEFORE
const suppliersQuery = useGetSuppliers(app, input)

// AFTER
const suppliersQuery = useEnhancedSuppliers(app, input)
```

2. **Replace cache updates**:

```jsx
// BEFORE
queryClient.setQueryData(
	["getSuppliers", { RegistrationNumber, year: reportingYear }],
	newSuppliers
)

// AFTER
// The enhanced system handles cache updates automatically
// Just invalidate when needed:
queryClient.invalidateQueriesWithRelationships([CACHE_TAGS.SUPPLIERS])
```

### Dashboard Components

1. **Use enhanced dashboard hook**:

```jsx
import { useEnhancedDashboard } from "../hooks/enhanced/useEnhancedQuery"

const dashboard = useEnhancedDashboard(app, {
	RegistrationNumber: currentOrganization?.RegistrationNumber,
	year: currentYear,
})
```

## Advanced Features

### Cache Warming

Warm critical data on app startup:

```jsx
import { useCacheWarming } from "../services/cache-warming"

const App = () => {
	const { warmCriticalData } = useCacheWarming()

	useEffect(() => {
		if (currentOrganization) {
			warmCriticalData(app, currentOrganization)
		}
	}, [currentOrganization])
}
```

### Cache Monitoring (Development)

Add cache monitoring for debugging:

```jsx
import { useCacheMonitor } from "../components/debug/CacheMonitor"

const DevTools = () => {
	const { toggleMonitor, CacheMonitor } = useCacheMonitor()

	return (
		<>
			<button onClick={toggleMonitor}>Open Cache Monitor</button>
			<CacheMonitor />
		</>
	)
}
```

### Manual Cache Operations

```jsx
import { cacheManager, CACHE_TAGS } from "../services/cache-manager"

// Clear specific cache entries
cacheManager.invalidate("specific-key")

// Clear by tags
cacheManager.invalidateByTags([CACHE_TAGS.TRANSACTIONS])

// Get cache statistics
const stats = cacheManager.getStats()
console.log(`Hit rate: ${stats.hitRate}%`)
```

## Performance Benefits

After migration, you should see:

1. **Reduced API Calls**: Request deduplication prevents duplicate requests
2. **Faster Navigation**: Cache warming preloads critical data
3. **Better UX**: Stale-while-revalidate shows cached data immediately
4. **Intelligent Updates**: Related data updates automatically
5. **Memory Management**: Automatic cache size management

## Testing

The caching system includes comprehensive tests. Run them with:

```bash
npm test -- --run src/test/cache-manager.test.ts
```

## Debugging

1. **Enable Debug Mode**: Set `debugMode: true` in cache initialization
2. **Use Cache Monitor**: Open the cache monitor component in development
3. **Check Console**: Cache operations are logged in debug mode
4. **Monitor Performance**: Use the cache statistics to track performance

## Common Issues

### Cache Not Updating

If data isn't updating after mutations:

```jsx
// Make sure to invalidate after updates
await queryClient.invalidateQueriesWithRelationships([CACHE_TAGS.TRANSACTIONS])
```

### Memory Usage

If memory usage is high:

```jsx
// Reduce cache size
const cacheManager = new CacheManager(50) // Reduce from default 200

// Or clear cache periodically
cacheManager.clear()
```

### Stale Data

If you're seeing stale data:

```jsx
// Reduce TTL for more frequent updates
const config = {
	ttl: 2 * 60 * 1000, // 2 minutes instead of 5
	staleWhileRevalidate: false, // Disable if you need fresh data always
}
```

## Migration Checklist

- [ ] Update App.jsx with EnhancedQueryClient
- [ ] Replace useQuery hooks with enhanced versions
- [ ] Add cache initialization to main layout
- [ ] Update data mutation handlers
- [ ] Replace manual refetch calls with intelligent invalidation
- [ ] Add cache warming for critical data
- [ ] Test cache behavior in development
- [ ] Monitor cache performance
- [ ] Update error handling for enhanced queries
- [ ] Document component-specific cache strategies

## Next Steps

After migration:

1. Monitor cache hit rates and adjust TTL values
2. Identify additional data that could benefit from warming
3. Optimize cache invalidation strategies based on usage patterns
4. Consider adding more granular cache tags for better invalidation
5. Implement cache persistence for offline support (future enhancement)
