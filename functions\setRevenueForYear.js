exports = async (input) => {
  // Get transactions from database.
  try {
    let {
      Revenue,
      TotalCo2,
      co2Intensity,
      Scope1,
      Scope2,
      Scope3,
      year,
      RegistrationNumber,
      CompanyName,
    } = input

    let queryInput = { RegistrationNumber }
    let companyRequest = context.services
      .get("mongodb-atlas")
      .db("co2-intensities-dev")
      .collection("companies")

    let company = await companyRequest.find(queryInput).toArray()

    let newCompany = {}

    if (company.length > 0) {
      newCompany = { ...company[0] }
    } else {
      newCompany["IndustryName"] = ""
      newCompany["CompanyName"] = CompanyName
      newCompany["CompanyRef"] = ""
      newCompany["Nace"] = ""
      newCompany["Sector"] = ""
      newCompany["RegistrationNumber"] = RegistrationNumber
      newCompany["CreatedAt"] = new Date()
      newCompany["Category"] = ""
      newCompany["Currency"] = ""
      newCompany["Naics"] = ""
      newCompany["Sic"] = ""
      newCompany["Emissions"] = {}
    }

    let { Emissions } = newCompany

    Emissions[year] = { Revenue, TotalCo2, co2Intensity, Scope1, Scope2, Scope3 }
    newCompany["Emissions"] = Emissions

    const KEYS = Object.keys(Emissions)

    let response = ""

    if (company.length > 0) {
      response = await companyRequest.updateOne({ RegistrationNumber }, { $set: newCompany })
    } else {
      response = await companyRequest.insertOne({ ...newCompany })
    }

    return response
  } catch (err) {
    console.log(err)
    return []
  }
}
