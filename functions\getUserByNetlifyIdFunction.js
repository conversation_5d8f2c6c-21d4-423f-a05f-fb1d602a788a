exports = async (input) => {
	// Netlify id for the user will be provided in "input".

	try {
		const user = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("user")
			.findOne({ netlifyID: input.netlifyID })
		if (!user) return {}

		if (!user?.Subscription) {
			user["Subscription"] = 0
		}

		// Get the full name from company collection.
		if (user.RegistrationNumber) {
			const company = await context.services
				.get("mongodb-atlas")
				.db(context.environment.values.database)
				.collection("company")
				.findOne(
					{ RegistrationNumber: user.RegistrationNumber },
					{
						ContactPerson: 1,
						Name: 1,
						Industry: 1,
						NaceCode: 1,
						Logo: 1,
						Subscription: 1,
						SAFT_files: 1,
						IndustryCo2Intensity: 1,
					}
				)

			if (company && company.ContactPerson) {
				user.companyInfo = company
				user.user_metadata.full_name =
					(company.ContactPerson.FirstName || "") + " " + (company.ContactPerson.LastName || "")
			}
			if ((company && company.Subscription === 0) || company.Subscription === 1) {
				user["Subscription"] = company.Subscription
			}
		}
		return user
	} catch (err) {
		console.error(err)
		return {}
	}
}
