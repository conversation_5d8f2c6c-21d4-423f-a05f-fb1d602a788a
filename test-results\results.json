{"numTotalTestSuites": 2, "numPassedTestSuites": 2, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 3, "numPassedTests": 3, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1755753883717, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["Cache Invalidation Fix"], "fullName": "Cache Invalidation Fix should invalidate cache for correct query keys", "status": "passed", "title": "should invalidate cache for correct query keys", "duration": 6.733099999999922, "failureMessages": [], "location": {"line": 52, "column": 5}, "meta": {}}, {"ancestorTitles": ["Cache Invalidation Fix"], "fullName": "Cache Invalidation Fix should use correct query key format matching useGetTransactions hook", "status": "passed", "title": "should use correct query key format matching useGetTransactions hook", "duration": 0.6100000000001273, "failureMessages": [], "location": {"line": 143, "column": 5}, "meta": {}}, {"ancestorTitles": ["Cache Invalidation Fix"], "fullName": "Cache Invalidation Fix should handle cross-scope transaction addition correctly", "status": "passed", "title": "should handle cross-scope transaction addition correctly", "duration": 1.3193999999998596, "failureMessages": [], "location": {"line": 158, "column": 5}, "meta": {}}], "startTime": 1755753885745, "endTime": 1755753885754.3193, "status": "passed", "message": "", "name": "C:/Users/<USER>/Desktop/Work/app 2.0/app/src/test/cache-invalidation-fix.test.js"}]}