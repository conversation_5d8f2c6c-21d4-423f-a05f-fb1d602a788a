{"numTotalTestSuites": 8, "numPassedTestSuites": 8, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 13, "numPassedTests": 13, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1755750974806, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Basic Cache Operations"], "fullName": "CacheManager Basic Cache Operations should store and retrieve data", "status": "passed", "title": "should store and retrieve data", "duration": 3.8829000000000633, "failureMessages": [], "location": {"line": 23, "column": 3}, "meta": {}}, {"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Basic Cache Operations"], "fullName": "CacheManager Basic Cache Operations should return null for non-existent keys", "status": "passed", "title": "should return null for non-existent keys", "duration": 0.8335999999999331, "failureMessages": [], "location": {"line": 31, "column": 3}, "meta": {}}, {"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Basic Cache Operations"], "fullName": "CacheManager Basic Cache Operations should handle TTL expiration", "status": "passed", "title": "should handle TTL expiration", "duration": 0.9313000000001921, "failureMessages": [], "location": {"line": 36, "column": 3}, "meta": {}}, {"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Basic Cache Operations"], "fullName": "CacheManager Basic Cache Operations should handle stale-while-revalidate", "status": "passed", "title": "should handle stale-while-revalidate", "duration": 0.9920999999999367, "failureMessages": [], "location": {"line": 58, "column": 3}, "meta": {}}, {"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Request Deduplication"], "fullName": "CacheManager Request Deduplication should deduplicate concurrent identical requests", "status": "passed", "title": "should deduplicate concurrent identical requests", "duration": 1.2786000000000968, "failureMessages": [], "location": {"line": 95, "column": 3}, "meta": {}}, {"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Request Deduplication"], "fullName": "CacheManager Request Deduplication should handle request deduplication with requestDeduplicator", "status": "passed", "title": "should handle request deduplication with requestDeduplicator", "duration": 0.7618999999999687, "failureMessages": [], "location": {"line": 123, "column": 3}, "meta": {}}, {"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Intelligent Cache Invalidation"], "fullName": "CacheManager Intelligent Cache Invalidation should invalidate cache entries by tags", "status": "passed", "title": "should invalidate cache entries by tags", "duration": 1.603399999999965, "failureMessages": [], "location": {"line": 149, "column": 3}, "meta": {}}, {"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Intelligent Cache Invalidation"], "fullName": "CacheManager Intelligent Cache Invalidation should invalidate specific cache entries", "status": "passed", "title": "should invalidate specific cache entries", "duration": 0.7085000000001855, "failureMessages": [], "location": {"line": 201, "column": 3}, "meta": {}}, {"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "fullName": "CacheManager Cache Size Management should evict least used entries when cache is full", "status": "passed", "title": "should evict least used entries when cache is full", "duration": 1.1808000000000902, "failureMessages": [], "location": {"line": 219, "column": 3}, "meta": {}}, {"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "fullName": "CacheManager Cache Size Management should track cache statistics correctly", "status": "passed", "title": "should track cache statistics correctly", "duration": 1.0217000000002372, "failureMessages": [], "location": {"line": 240, "column": 3}, "meta": {}}, {"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "fullName": "CacheManager Cache Warming should warm cache with multiple strategies", "status": "passed", "title": "should warm cache with multiple strategies", "duration": 1.770600000000286, "failureMessages": [], "location": {"line": 257, "column": 3}, "meta": {}}, {"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "fullName": "CacheManager Cache Warming should handle warming failures gracefully", "status": "passed", "title": "should handle warming failures gracefully", "duration": 0.8317000000001826, "failureMessages": [], "location": {"line": 277, "column": 3}, "meta": {}}, {"ancestorTitles": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "fullName": "CacheManager Cache Cleanup should clean up expired entries", "status": "passed", "title": "should clean up expired entries", "duration": 0.34930000000031214, "failureMessages": [], "location": {"line": 300, "column": 3}, "meta": {}}], "startTime": 1755750976738, "endTime": 1755750976754.3494, "status": "passed", "message": "", "name": "C:/Users/<USER>/Desktop/Work/app 2.0/app/src/test/cache-manager.test.ts"}]}