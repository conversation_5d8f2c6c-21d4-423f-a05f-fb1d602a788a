{"Market_based": "Market-based", "Location_based": "Location-based", "total_emission_for_reporting_year": "Total emissions of reporting year", "for": "for", "Including_dwonstream": "Including downstream", "Title": "Carbon and energy report for", "Category9_15": "Category 9 - 15 - Downstream Emissions", "Picture": "Picture here...", "Logo": "Logo here...", "Ceo": "A message from our CEO", "Commitment": "Our climate commitment", "Energy": "Energy use", "Total": "Total CO2 emissions", "Market": "Market-based total scope:", "Location": "Location-based total scope:", "Market_based_scope2": "Market-based scope2: ", "Location_based_scope2": "Location-based scope2: ", "marketBasedChart": "Market-based emissions", "locationBasedChart": "Location-based emissions", "Scope1": "Scope 1 emissions", "Scope2": "Scope 2 emissions", "Scope3": "Scope 3 emissions", "Suppliers": "Suppliers", "Cat1": "Category 1 - Purchased goods and services", "Cat2": "Category 2 - Capital goods", "Cat3": "Category 3 - Fuel- and energy-related activities", "Cat4": "Category 4 - Upstream transportation and distribution", "Cat5": "Category 5 - Waste generated in operations", "Cat6": "Category 6 - Business travel", "Cat7": "Category 7 - Employee commute", "Cat8": "Category 8 - Upstream leased assets", "Methodology": "Methodology", "References": "References", "Energy overview": "Energy overview", "Stationary combustion": "Stationary combustion", "Mobile combustion": "Mobile combustion", "Renewable electricity": "Renewable electricity", "Non-Renewable electricity": "Non-Renewable electricity", "Nuclear energy": "Nuclear energy", "Total energy to output": "Total energy consumption", "Percentage": "Percentage", "Kilowatt hours": "<PERSON><PERSON><PERSON> hours", "Energy source": "Energy source", "importdata": "Import Data", "importText": "You can import your report from previos years into this years report.", "year": "Year", "printPage": "Print Page"}