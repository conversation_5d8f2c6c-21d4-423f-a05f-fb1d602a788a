exports = async function (arg) {
	try {
		const { RegistrationNumber,AnalysisIDs } = arg
		const currentYear = new Date().getFullYear()
		let query = {
			RegistrationNumber: RegistrationNumber,
			PeriodYear: {
				$gte: "2022",
				$lte: `${currentYear}`,
			},
		}
		if (AnalysisIDs) {
			query["Analysis"] = {
				$elemMatch: {
					AnalysisID: {
						$in: [...AnalysisIDs],
					},
				},
			}
		}

		const Data = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("transaction")
			.aggregate([
				{
					$match: {
						...query
					},
				},
				{
					$facet: {
						scopeData: [
							{
								$group: {
									_id: "$PeriodYear",
									Scope1: {
										$sum: "$Scope_1",
									},
									Scope2: {
										$sum: "$Scope_2",
									},
									Scope3: {
										$sum: "$Scope_3",
									},
								},
							},
						],
						downStreamEmissions: [
							{ $match: { Scope_3_Category: { $gt: 8 } } },
							{
								$group: {
									_id: "$PeriodYear",
									emission: {
										$sum: "$Scope_3",
									},
								},
							},
						],
					},
				},
			])
			.toArray()

		// Data may not contain all years emission make sure they contain
		// all years data so that it would be easy to show it on frontend

		let { scopeData, downStreamEmissions } = Data[0]

		// Create a map for quick lookup of downstream emissions

          const emissionMap = new Map(downStreamEmissions.map(item => [item._id, item.emission]));

// Create the modified scopeData
const modifiedScopeData = scopeData.map(item => ({
  ...item,
  Scope3: item.Scope3 - (emissionMap.get(item._id) || 0) // Subtract downstream emission if available
}));


        const years = modifiedScopeData.map((item) => item._id)

      for (let i = 2022; i <= currentYear; i++) {
            if (!years.includes(i.toString())) {
                modifiedScopeData.push({
                    _id: i.toString(),
                    Scope1: 0,
                    Scope2: 0,
                    Scope3: 0,
                })
            }
        }

        // sort the data by the years

        modifiedScopeData.sort((a,b)=>a._id - b._id)

		return {success: true, Data:modifiedScopeData}
	} catch (error) {
		return {success: false, error}
	}
}
