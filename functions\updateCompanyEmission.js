exports = async (args) => {
	const { RegistrationNumber, Revenue, year } = args
	try {
		const company = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("company")
			.findOne({ RegistrationNumber: RegistrationNumber })
		let Emissions = company.Emissions

		let newEmissions = []

		if (Emissions) {
			newEmissions = { ...Emissions, [year]: { Revenue } }
		} else {
			newEmissions = { [year]: { Revenue } }
		}

		const request = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("company")
			.updateOne({ RegistrationNumber: RegistrationNumber }, { $set: { Emissions: newEmissions } })
		if (request) {
			return { success: true }
		} else {
			return { success: false }
		}
	} catch (error) {
		console.log(error)
		return { success: false }
	}
}
