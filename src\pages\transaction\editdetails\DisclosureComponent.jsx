import { Disclosure, DisclosureButton, DisclosurePanel } from "@headlessui/react"
import { ChevronDownIcon } from "@heroicons/react/20/solid" // Or ChevronUpIcon depending on state
import React from "react"

const DisclosureComponent = ({ title, children, disabled, isOpen = false }) => {
	return (
		<Disclosure as="div" disabled={disabled} defaultOpen={isOpen && !disabled}>
			{({ open }) => (
				<>
					<DisclosureButton
						disabled={disabled}
						className={`p-2.5 group flex w-full items-center justify-between ${
							disabled ? "cursor-not-allowed opacity-50" : "data-[open]:bg-sky-100"
						}`}
					>
						<span className="text-sm/6 font-medium text-black group-data-[open]:text-sky-600">
							{title}
						</span>
						<ChevronDownIcon
							className={`${open ? "rotate-180 transform" : ""} size-5 fill-black/60 group-data-[hover]:fill-black/50`}
						/>
					</DisclosureButton>
					<DisclosurePanel className="mt-2 py-2 px-3 text-sm/5 text-black/50">
						{children}
					</DisclosurePanel>
				</>
			)}
		</Disclosure>
	)
}

export default DisclosureComponent
