//import { useUser } from "@clerk/clerk-react"
import { useUser } from "@clerk/clerk-react"
import { Table, TableBody, TableCell, TableRow } from "@tremor/react"
import React, { useRef, useState, useEffect } from "react"
// import { CheckCircleIcon } from "@heroicons/react/20/solid"
import { useDispatch } from "react-redux"
import { useNavigate } from "react-router-dom"

import Alert from "../../../components/Alert.component"
import Button from "../../../components/ui/Button"
import FileUpload from "../../../components/ui/FileUpload"
import Spinner from "../../../components/ui/Spinner"
import { useGetLastUploadedTransactionDate, useToast } from "../../../hooks"
import { useRealmApp } from "../../../realm/RealmAppProvider"
import {
	fileUploaded,
	handleWorkingOnData,
	isCompanyRegistered,
	currentOrganizationAction,
} from "../../../store/actions/UserAction"
import { store } from "../../../store/configureStore"
import { firebaseAuth } from "../../firebase/firebase"

import "../../../styles/welcome.css"
// eslint-disable-next-line import/no-unresolved
import { uploadFileToGCS, deleteFile } from "@/services/gcs-service"

export default function Setting({ t, loginUser }) {
	const dropRef = useRef()
	const selectRef = useRef()
	const [file, setFile] = useState()
	//const [isEmailAvailable, setIsEmailAvailable] = useState(true)
	const [fileName, setFileName] = useState("")
	const [userEmail, setUserEmail] = useState(null)
	const [isFileSelected, setIsFileSelected] = useState()
	const dispatch = useDispatch()
	const navigate = useNavigate()
	const { user } = useUser()
	const [progress, setProgress] = useState(null)
	const [isFileUploading, setIsFileUploading] = useState(false)
	const app = useRealmApp()
	const [date, setDate] = useState(null)
	const [showDeletionWarning, setShowDeletionWarning] = useState(false)
	const [indexOfFile, setIndexOfFile] = useState(null)
	const Toast = useToast()

	const [loading, setLoading] = useState(false)

	//const isCompanyRegistered = useSelector((state) => state.company)

	const lastTransactionDate = useGetLastUploadedTransactionDate(app, {
		RegistrationNumber: loginUser?.RegistrationNumber,
	})

	const validateFile = file => {
		const allowedTypes = ["text/xml", "application/zip", "application/x-zip-compressed"]
		if (!allowedTypes.includes(file.type)) {
			Toast("error", "Only XML and ZIP files are allowed")

			return false
		}

		return true
	}

	const uploadFile = async () => {
		if (!file) return

		if (!validateFile(file)) return

		//setUploading(true)
		//setProgress(0)

		try {
			// Upload file to Google Cloud Storage via backend proxy

			const foldername = user.id

			setIsFileUploading(true)

			setProgress(0)

			await uploadFileToGCS(file, foldername, progress => {
				setProgress(progress)
			})

			setIsFileUploading(false)

			Toast("success", `${file.name} has been uploaded to Scope321`)

			setFile(null)
			setFileName("")
			dispatch(fileUploaded(true))
			dispatch(handleWorkingOnData(true))
			dispatch(isCompanyRegistered(true))
			navigate("/dashboard")
		} catch (error) {
			console.error("Upload failed:", error)
			Toast("error", "There was an error uploading your file. Please try again.")
			setFile(null)
			setFileName("")
			setIsFileSelected(false)
			setIsFileUploading(false)
			setProgress(0)
			if (selectRef?.current) {
				selectRef.current.value = ""
			}
		}
	}

	const handleStoreValueChange = () => {
		const stateData = store.getState()
		if (stateData.user) {
			if (!userEmail) {
				setUserEmail(stateData.user.user.email)
			}
		}
	}

	const onSelectFile = () => {
		if (selectRef?.current?.files && selectRef.current.files.length) {
			if (!userEmail) {
				handleStoreValueChange()
			}

			setFile(selectRef.current.files[0])
			setIsFileSelected(true)
			setIsFileUploading(false)
			//setIsEmailAvailable(true)
		}
	}
	// handle drag
	const handleDrag = e => {
		e.preventDefault()
		e.stopPropagation()
	}

	const handleDragIn = e => {
		e.preventDefault()
		e.stopPropagation()
		if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
			//setDrag(true)
		}
	}

	const deleteFileFromCloudStorage = async () => {
		try {
			setShowDeletionWarning(false)
			//const filePath = "gs://development-scope321/user_2WZ8jxuhmeAclchpN9G6Or2qmRk/SAF-T Financial_Analysis.xml"
			const file = `${loginUser.netlifyID}/${loginUser.company.SAFT_files[indexOfFile]}`

			setLoading(true)

			await deleteFile(file)

			const data = await app.deleteSAFTFile({
				RegistrationNumber: loginUser.RegistrationNumber,
				index: indexOfFile,
			})
			setLoading(false)

			const Files = loginUser.company.SAFT_files.filter((_, i) => i !== indexOfFile)

			loginUser.company.SAFT_files = Files

			dispatch(currentOrganizationAction({ ...loginUser }))

			if (data.success) {
				Toast("success", t("file_deleted_message"))
			}

			setShowDeletionWarning(false)
		} catch (error) {
			// handle error

			console.log(error)
		}
	}

	const handleDeleteFile = index => {
		setIndexOfFile(index)
		setShowDeletionWarning(true)
	}

	const handleDrop = e => {
		// when user drops file set its file details in state
		e.preventDefault()
		e.stopPropagation()
		//setDrag(false)
		if (e.dataTransfer.files && e.dataTransfer.files.length) {
			selectRef.current.files = e.dataTransfer.files
			if (!userEmail) {
				handleStoreValueChange()
			}
			setFile(e.dataTransfer.files[0])

			setIsFileSelected(true)
			//setIsEmailAvailable(true)
		}
	}

	useEffect(() => {
		// this div refresh to drag and drop container. we add event listeners for drag and drop functionality
		let div = dropRef.current
		div.addEventListener("dragenter", handleDragIn)
		div.addEventListener("dragover", handleDrag)
		div.addEventListener("drop", handleDrop)
		const unsubscribe = store.subscribe(handleStoreValueChange)
		return () => {
			// eslint-disable-next-line
			let div = dropRef.current
			if (div) {
				div.removeEventListener("dragenter", handleDragIn)
				div.removeEventListener("dragover", handleDrag)
				div.removeEventListener("drop", handleDrop)
			}
			unsubscribe()
			firebaseAuth?.signOut()
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])

	useEffect(() => {
		// Upon getting file set it's filename to show it on page.
		if (file?.name) {
			setFileName(file.name)
		} else {
			setFileName("")
		}
	}, [file])

	useEffect(() => {
		if (lastTransactionDate.data?.success) {
			const MONTHS = [
				"",
				"January",
				"February",
				"March",
				"April",
				"May",
				"June",
				"July",
				"August",
				"September",
				"October",
				"November",
				"December",
			]
			const parsedDate = lastTransactionDate.data.date.split("-")
			const date = `${parsedDate[2]} ${MONTHS[Number(parsedDate[1])]} ${parsedDate[0]}`
			setDate(date)
		}
	}, [lastTransactionDate.data])

	const openFileUpload = () => {
		selectRef.current.click()
	}

	return (
		<>
			{/* <nav
				type="button"
				onClick={() => {
					backToAccount()
					//props.navigate("/")
				}}
				className="inline-flex items-center gap-x-1.5 rounded-md px-3 py-1.5 text-base font-semibold text-sky-700 shadow-sm bg-slate-100 hover:bg-sky-100 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-sky-600"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					strokeWidth="1.5"
					stroke="currentColor"
					className="w-6 h-6"
				>
					<path strokeLinecap="round" strokeLinejoin="round" d="M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3" />
				</svg>
				{t("back_to_account")}
			</nav> */}

			<Alert
				show={showDeletionWarning}
				handleClose={() => setShowDeletionWarning(false)}
				handleContinue={() => deleteFileFromCloudStorage()}
				text={t("confirm_delete_message")}
				title={t("deletion_warning")}
				t={t}
			/>
			<div className="cl-component cl-user-profile">
				<div className="cl-main mt-2">
					<div className="cl-user-profile-card cl-themed-card">
						{!loading ? (
							<div className="cl-titled-card-list">
								<h4 className="font-extrabold text-2xl">{t("Data")}</h4>
								<h6 className="font-bold text-lg my-2">{t("upload_fin_data")}</h6>

								<div className="rounded-md border-l-8 border-sky-600 bg-sky-200 p-3">
									{date ? (
										<p>
											{t("last_upload_trans")} <b>{date}</b>{" "}
										</p>
									) : (
										<p className="font-medium text-lg">{t("no_trans")}</p>
									)}
									<p>{t("file_upload_warning")}</p>
								</div>
								<div className="grid grid-cols-1 lg:grid-cols-2 justify-between gap-4 mt-3">
									{loginUser?.company?.SAFT_files && loginUser?.company?.SAFT_files.length > 0 ? (
										<Table className="max-h-72">
											<TableBody>
												{loginUser?.company?.SAFT_files.map((file, index) => (
													<TableRow key={index}>
														<TableCell className="max-w-24 w-full truncate" title={file}>
															{file}
														</TableCell>
														<TableCell>
															<Button
																variation="danger"
																title="Delete"
																handleClick={() => handleDeleteFile(index)}
															/>
														</TableCell>
													</TableRow>
												))}
											</TableBody>
										</Table>
									) : (
										<>
											<p>{t("no_file_attached")}</p>
										</>
									)}

									<FileUpload
										progress={progress}
										dropRef={dropRef}
										selectRef={selectRef}
										isFileUploading={isFileUploading}
										uploadFile={uploadFile}
										onSelectFile={onSelectFile}
										openFileUpload={openFileUpload}
										isFileSelected={isFileSelected}
										isDisable={isFileUploading || !isFileSelected}
										fileName={fileName}
										t={t}
									/>
								</div>
								{/* <FileUpload dropRef={dropRef} selectRef={selectRef} /> */}
							</div>
						) : (
							<Spinner />
						)}
					</div>
				</div>
			</div>
		</>
	)
}
