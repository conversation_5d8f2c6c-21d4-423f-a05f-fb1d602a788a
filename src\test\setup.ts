import "@testing-library/jest-dom"

// Mock environment variables for testing
// Note: NODE_ENV is set by the test runner

// Mock window.matchMedia
Object.defineProperty(window, "matchMedia", {
	writable: true,
	value: vi.fn().mockImplementation(query => ({
		matches: false,
		media: query,
		onchange: null,
		addListener: vi.fn(), // deprecated
		removeListener: vi.fn(), // deprecated
		addEventListener: vi.fn(),
		removeEventListener: vi.fn(),
		dispatchEvent: vi.fn(),
	})),
})

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
	observe: vi.fn(),
	unobserve: vi.fn(),
	disconnect: vi.fn(),
}))

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
	observe: vi.fn(),
	unobserve: vi.fn(),
	disconnect: vi.fn(),
}))

// Mock window.scrollTo
Object.defineProperty(window, "scrollTo", {
	writable: true,
	value: vi.fn(),
})

// Mock localStorage
const localStorageMock = {
	getItem: vi.fn(),
	setItem: vi.fn(),
	removeItem: vi.fn(),
	clear: vi.fn(),
}
Object.defineProperty(window, "localStorage", {
	value: localStorageMock,
})

// Mock sessionStorage
const sessionStorageMock = {
	getItem: vi.fn(),
	setItem: vi.fn(),
	removeItem: vi.fn(),
	clear: vi.fn(),
}
Object.defineProperty(window, "sessionStorage", {
	value: sessionStorageMock,
})

// Mock URL.createObjectURL
Object.defineProperty(URL, "createObjectURL", {
	writable: true,
	value: vi.fn(() => "mocked-url"),
})

// Mock URL.revokeObjectURL
Object.defineProperty(URL, "revokeObjectURL", {
	writable: true,
	value: vi.fn(),
})

// Mock fetch if not available
if (!global.fetch) {
	global.fetch = vi.fn()
}

// Enhanced console methods for better test debugging
const originalError = console.error
console.error = (...args: unknown[]) => {
	if (
		typeof args[0] === "string" &&
		args[0].includes("Warning: ReactDOM.render is no longer supported")
	) {
		return
	}
	originalError.call(console, ...args)
}

// Global test utilities
declare global {
	// eslint-disable-next-line no-var
	var testUtils: {
		mockLocalStorage: typeof localStorageMock
		mockSessionStorage: typeof sessionStorageMock
	}
}

globalThis.testUtils = {
	mockLocalStorage: localStorageMock,
	mockSessionStorage: sessionStorageMock,
}
