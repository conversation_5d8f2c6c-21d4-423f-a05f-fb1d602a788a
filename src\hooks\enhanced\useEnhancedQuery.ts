/**
 * Enhanced Query Hook with Advanced Caching and Deduplication
 * Provides optimized data fetching with intelligent caching strategies
 */

import { useQuery, UseQueryOptions, UseQueryResult } from "@tanstack/react-query"
import { useCallback, useEffect, useRef } from "react"
import { cacheManager, CacheTag, CACHE_TAGS } from "../../services/cache-manager"
import {
	createCachedQueryFn,
	QueryCacheConfig,
	requestDeduplicator,
} from "../../services/query-cache-integration"

export interface EnhancedQueryOptions<T> extends Omit<UseQueryOptions<T>, "queryFn"> {
	queryFn: () => Promise<T>
	cacheConfig?: QueryCacheConfig
	deduplicationEnabled?: boolean
	warmOnMount?: boolean
	invalidateOnTags?: CacheTag[]
}

/**
 * Enhanced useQuery hook with advanced caching features
 */
export function useEnhancedQuery<T>(
	queryKey: string | string[],
	options: EnhancedQueryOptions<T>
): UseQueryResult<T> & {
	invalidateRelated: (tags: CacheTag[]) => void
	getCacheStats: () => any
	warmCache: () => Promise<void>
} {
	const {
		queryFn,
		cacheConfig,
		deduplicationEnabled = true,
		warmOnMount = false,
		invalidateOnTags = [],
		...queryOptions
	} = options

	const keyString = Array.isArray(queryKey) ? queryKey.join(":") : queryKey
	const mountedRef = useRef(false)

	// Create enhanced query function with caching
	const enhancedQueryFn = useCallback(() => {
		if (deduplicationEnabled) {
			return requestDeduplicator.deduplicate(keyString, queryFn, cacheConfig?.ttl || 30000)
		}
		return queryFn()
	}, [keyString, queryFn, deduplicationEnabled, cacheConfig?.ttl])

	// Use React Query with our enhanced function
	const queryResult = useQuery({
		queryKey: Array.isArray(queryKey) ? queryKey : [queryKey],
		queryFn: createCachedQueryFn(enhancedQueryFn, cacheConfig),
		staleTime: cacheConfig?.ttl || 5 * 60 * 1000, // 5 minutes default
		gcTime: (cacheConfig?.ttl || 5 * 60 * 1000) * 2, // 2x stale time
		...queryOptions,
	})

	// Warm cache on mount if requested
	useEffect(() => {
		if (warmOnMount && !mountedRef.current) {
			mountedRef.current = true

			// Warm cache in background
			cacheManager
				.get(keyString, queryFn, {
					...cacheConfig,
					priority: "high",
				})
				.catch(error => {
					console.warn(`Cache warming failed for ${keyString}:`, error)
				})
		}
	}, [warmOnMount, keyString, queryFn, cacheConfig])

	// Invalidate related data function
	const invalidateRelated = useCallback((tags: CacheTag[]) => {
		cacheManager.invalidateByTags(tags)
	}, [])

	// Get cache statistics
	const getCacheStats = useCallback(() => {
		return cacheManager.getStats()
	}, [])

	// Warm cache function
	const warmCache = useCallback(async () => {
		try {
			await cacheManager.get(keyString, queryFn, {
				...cacheConfig,
				priority: "high",
			})
		} catch (error) {
			console.warn(`Manual cache warming failed for ${keyString}:`, error)
			throw error
		}
	}, [keyString, queryFn, cacheConfig])

	return {
		...queryResult,
		invalidateRelated,
		getCacheStats,
		warmCache,
	}
}

/**
 * Hook for transactions with optimized caching
 */
export function useEnhancedTransactions(app: any, input: any) {
	return useEnhancedQuery(["transactions", input], {
		queryFn: () => app.getTransactions(input),
		enabled: input !== null,
		cacheConfig: {
			tags: [CACHE_TAGS.TRANSACTIONS],
			ttl: 5 * 60 * 1000, // 5 minutes
			staleWhileRevalidate: true,
			priority: "high",
		},
		deduplicationEnabled: true,
		warmOnMount: false,
		invalidateOnTags: [CACHE_TAGS.TRANSACTIONS],
	})
}

/**
 * Hook for suppliers with optimized caching
 */
export function useEnhancedSuppliers(app: any, input: any) {
	return useEnhancedQuery(["suppliers", input], {
		queryFn: () => app.getSuppliers(input),
		enabled: input !== undefined,
		cacheConfig: {
			tags: [CACHE_TAGS.SUPPLIERS],
			ttl: 15 * 60 * 1000, // 15 minutes (suppliers change less frequently)
			staleWhileRevalidate: true,
			priority: "high",
		},
		deduplicationEnabled: true,
		warmOnMount: true, // Warm suppliers on mount
		invalidateOnTags: [CACHE_TAGS.SUPPLIERS],
	})
}

/**
 * Hook for dashboard data with optimized caching
 */
export function useEnhancedDashboard(app: any, input: any) {
	return useEnhancedQuery(["dashboard", input], {
		queryFn: () => app.getYearlyChartData(input),
		enabled: input !== null,
		cacheConfig: {
			tags: [CACHE_TAGS.DASHBOARD],
			ttl: 10 * 60 * 1000, // 10 minutes
			staleWhileRevalidate: true,
			priority: "high",
		},
		deduplicationEnabled: true,
		warmOnMount: true, // Warm dashboard on mount
		invalidateOnTags: [CACHE_TAGS.DASHBOARD, CACHE_TAGS.TRANSACTIONS],
	})
}

/**
 * Hook for NACE data with long-term caching
 */
export function useEnhancedNace(app: any, input: any) {
	return useEnhancedQuery(["nace", input], {
		queryFn: () => app.getNace(input),
		enabled: input !== null,
		cacheConfig: {
			tags: [CACHE_TAGS.NACE],
			ttl: 60 * 60 * 1000, // 1 hour (NACE data rarely changes)
			staleWhileRevalidate: true,
			priority: "low",
		},
		deduplicationEnabled: true,
		warmOnMount: true,
		invalidateOnTags: [CACHE_TAGS.NACE],
	})
}

/**
 * Hook for analysis data with medium-term caching
 */
export function useEnhancedAnalysis(app: any, input: any) {
	return useEnhancedQuery(["analysis", input], {
		queryFn: () => app.getCompanyAnalysis(input),
		enabled: input !== null,
		cacheConfig: {
			tags: [CACHE_TAGS.ANALYSIS],
			ttl: 20 * 60 * 1000, // 20 minutes
			staleWhileRevalidate: true,
			priority: "medium",
		},
		deduplicationEnabled: true,
		warmOnMount: false,
		invalidateOnTags: [CACHE_TAGS.ANALYSIS, CACHE_TAGS.TRANSACTIONS],
	})
}

/**
 * Hook for settings with long-term caching
 */
export function useEnhancedSettings(
	app: any,
	input: any,
	queryType: "lock" | "publish" | "lastUpload"
) {
	const queryFnMap = {
		lock: () => app.getLockStatus(input),
		publish: () => app.getPublishedDataStatus(input),
		lastUpload: () => app.getLastUploadedTransactionDate(input),
	}

	return useEnhancedQuery(["settings", queryType, input], {
		queryFn: queryFnMap[queryType],
		enabled: input !== null,
		cacheConfig: {
			tags: [CACHE_TAGS.SETTINGS],
			ttl: 30 * 60 * 1000, // 30 minutes
			staleWhileRevalidate: true,
			priority: "low",
		},
		deduplicationEnabled: true,
		warmOnMount: false,
		invalidateOnTags: [CACHE_TAGS.SETTINGS],
	})
}
