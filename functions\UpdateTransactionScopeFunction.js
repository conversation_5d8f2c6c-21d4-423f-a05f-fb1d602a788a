exports = async (input) => {
	try {
		// Update trasactions.
		const request = context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("transaction")
		const transactionId = new BSON.ObjectId(input.transactionId)
		const result = await request.updateOne(
			{
				_id: transactionId,
			},
			{
				$set: input.transaction,
			}
		)

		return { status: "done", transaction: input.transaction }
	} catch (err) {
		return { status: "error", error: err }
	}
}
