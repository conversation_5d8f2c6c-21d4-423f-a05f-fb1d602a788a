/* eslint-disable array-callback-return */
import { extend } from "@syncfusion/ej2-base"
import { DropDownListComponent } from "@syncfusion/ej2-react-dropdowns"
import React from "react"
import Select from "react-select"
import AsyncSelect from "react-select/async"
import { v1 as uuidV1 } from "uuid"

import blue_flag from "../../assets/blue_flag.png"
import green_flag from "../../assets/green_flag.png"
import lightGreen_flag from "../../assets/lightGreen_flag.png"
import orange_flag from "../../assets/orange_flag.png"
import red_flag from "../../assets/red_flag.png"
import Pop from "../../components/PopUp.component"
import Input from "../../components/ui/Input"
import TextArea from "../../components/ui/TextArea"

export class TransactionsEditDialogForm extends React.Component {
	// this is called while editing/adding data in grid.
	constructor(props) {
		super(props)

		let data = JSON.parse(JSON.stringify(props))
		data.Scope_1 = ((data.Scope_1 || 0) * 1000).toFixed(2)
		data.Scope_2 = ((data.Scope_2 || 0) * 1000).toFixed(2)
		data.Scope_3 = ((data.Scope_3 || 0) * 1000).toFixed(2)
		data.Scope_1C = data.Scope_1
		data.Scope_2C = data.Scope_2
		data.Scope_3C = data.Scope_3
		data["customEmmisonFactor"] = ""

		if (data.supplierFactor) {
			if (typeof data.supplierFactor === "object") {
				data.supplierFactor = data.supplierFactor[this.props.reportingYear]
			}
		}

		if (data.supplierData.length > 0)
			data.supplierData = [{ label: "Add without supplier", value: "" }, ...data.supplierData]

		let SUPPLIER = data.supplierData.filter(supplier => {
			if (supplier.value === data.SupplierID) {
				return supplier
			}
		})

		if (SUPPLIER.length > 0) {
			data.SupplierValue = SUPPLIER[0]
		} else {
			data.SupplierValue = data.supplierData[0]
		}

		let record = data.naceData.filter(rec => rec.code === data.NaceCode)

		data["industryFactor"] =
			record.length > 0 ? Number(record[0].co2Intensity[this.props.reportingYear]).toFixed(2) : ""
		data["industry"] = record.length > 0 ? record[0].name : ""
		data["naceShortName"] = record.length > 0 ? record[0].shortName : ""
		data["naceDescription"] = record.length > 0 ? record[0].naceNotes : ""
		data["changedScopeValue"] = 0
		let Scope3Catagory = this.getScope3CategoryOption()
		const date = this.getTodayDate()
		const dateValues = date.split("-")
		const todayDate = `${data.currentYear}-${dateValues[1]}`
		data["TransactionDate"] = todayDate
		data["defaultScope3Catagory"] = { value: 0, label: "" }

		let filteredCatagory = Scope3Catagory.filter(catagory => {
			if (catagory.value == Number(data.Scope_3_Category)) {
				return catagory
			}
		})

		data["defaultScope3Catagory"] = { ...filteredCatagory[0] }
		data["Scope_3_Catagory"] = this.getScope3CategoryOption()

		// let supplierFactorData = data?.supplierData?.filter((supplier) => {
		//   if (data?.SupplierID === supplier.SupplierID) {
		//     return supplier
		//   }
		// })
		if (data.Description) {
			if (Array.isArray(data.Description)) {
				data.Description = data.Description.join(", ")
			}
		}

		this.state = extend({}, {}, data, true)
		let TransactionID = "TR-" + uuidV1()
		//let today = this.getTodayDate()
		let Scope3CategoryOption = this.getScope3CategoryOption()
		if (props.isAdd) {
			this.state = {
				...data,
				ScopeData: [
					{ label: "Scope 1", value: 1 },
					{ label: "Scope 2", value: 2 },
					{ label: "Scope 3", value: 3 },
				],
				Scope: this.getDefaultScope(),
				TransactionID: TransactionID,
				Status: 0,
				Supplier: data.supplierData && data.supplierData.length ? data.supplierData[0] : "",
				SupplierName:
					data.supplierData && data.supplierData.length ? data.supplierData[0].label : "",
				SupplierFactor: 0,
				industry:
					this.props.supplierData && this.props.supplierData.length
						? this.props.supplierData[0]?.Industry
						: "",
				industryFactor:
					this.props.supplierData && this.props.supplierData.length
						? Number(this.props.supplierData[0].IndustryCo2Intensity[this.props.reportingYear])
						: 0,
				NaceCode: this.props?.supplierData[0]?.NaceCode || "",
				Description: [],
				Scope_1: 0,
				Scope_2: 0,
				Scope_3: 0,
				Amount: 0,
				Scope_3_Category: 0,
				SelectedScope3Category: Scope3CategoryOption[2],
				Scope3CategoryOption: Scope3CategoryOption,
				//TransactionDate: today,
				Notes: "",
				Relation: "non-production",
				selectedTransactionType: "1", // 1  for Emission Source Registration
				selectedRecalculation: "",
			}
		}

		this.initialStatus = this.state.Status
		this.editorData = [
			{ text: "blue flag", value: 0 },
			{ text: "green flag", value: 1 },
			{ text: "orange flag", value: 2 },
			{ text: "red flag", value: 3 },
			{ text: "lightgreen flag", value: 4 },
		]
		// Mapping DropDownList fields property
		this.dropDownFields = { text: "text", value: "value" }
		this.valueToFlag = {
			0: blue_flag,
			1: green_flag,
			2: orange_flag,
			3: red_flag,
			4: lightGreen_flag,
		}
		this.displayNameForFlag = {
			0: "Info",
			1: "Ok",
			2: "Processed",
			3: "Warning",
			4: "Checked",
		}
		// Dropdown list
		this.flagItemTemplate = props => {
			return (
				<div className="flex items-center">
					<img src={this.valueToFlag[props.value]} alt={props.value} />
					{this.displayNameForFlag[props.value]}
				</div>
			)
		}
		this.flagValueTemplate = props => {
			return (
				<div className="flex items-center">
					<img src={this.valueToFlag[props.value]} alt={props.value} />
					{this.displayNameForFlag[props.value]}
				</div>
			)
		}
	}

	onClick(event) {
		this.setState({
			Scope_1: 0,
			Scope_2: 0,
			Scope_3: 0,
			Scope_1C: 0,
			Scope_2C: 0,
			Scope_3C: 0,
		})
		event.preventDefault()
	}

	onChange(args) {
		let key = args.target.name
		let value = args.target.value
		this.setState({ [key]: value })
	}

	changeTransactionPeriodYear(args) {
		let key = args.target.name
		let value = args.target.value

		const reportingYear = value.split("-")[0]
		const supplierIntensity = this.props.supplierData[0].SupplierCo2Intensity[reportingYear]
		const industryIntensity = this.props.supplierData[0].IndustryCo2Intensity[reportingYear]

		this.setState({
			[key]: value,
			SupplierFactor: supplierIntensity,
			industryFactor: industryIntensity,
		})
	}

	changeSelectedScope3Category(value) {
		this.setState({ SelectedScope3Category: value, Scope_3_Category: value.value })
	}

	onChangeDescription(args) {
		let value = args.target.value
		//let data = value.split(",")
		this.setState({ Description: value })
	}

	onChangeFlag(args) {
		this.setState({
			Status: args.itemData.value,
		})
	}

	getTodayDate() {
		let now = new Date()
		let month = now.getMonth() + 1
		let day = now.getDate()
		let today =
			now.getFullYear() +
			"-" +
			month.toString().padStart(2, "0") +
			"-" +
			day.toString().padStart(2, "0")
		return today
	}

	getScope3CategoryOption() {
		let data = [
			{ value: 1, label: this.props.t("Category1-PurchasedGoodsAndServices") },
			{ value: 2, label: this.props.t("Category2-CapitalGoods") },
			{ value: 3, label: this.props.t("Category3-FuelAndEnergyRelatedActivities") },
			{ value: 4, label: this.props.t("Category4-UpstreamTransportationAndDistribution") },
			{ value: 5, label: this.props.t("Category5-WasteGeneratedInOperations") },
			{ value: 6, label: this.props.t("Category6-BusinessTravel") },
			{ value: 7, label: this.props.t("Category7-EmployeeCommuting") },
			{ value: 8, label: this.props.t("Category8-UpstreamLeasedAssets") },
			{ value: 9, label: this.props.t("Category9-DownstreamTransportationandDistribution") },
			{ value: 10, label: this.props.t("Category10-ProcessingofSoldProducts") },
			{ value: 11, label: this.props.t("Category11-UseofSoldProducts") },
			{ value: 12, label: this.props.t("Category12-EndofLifeTreatmentofSoldProducts") },
			{ value: 13, label: this.props.t("Category13-DownstreamLeasedAssets") },
			{ value: 14, label: this.props.t("Category14-Franchises") },
			{ value: 15, label: this.props.t("Category15-Investments") },
		]
		return data
	}

	// this function will return default scope value for adding transaction

	getDefaultScope() {
		const { scopeType } = this.props

		switch (scopeType) {
			case "scope1":
				return { label: "Scope 1", value: 1 }
			case "scope2":
				return { label: "Scope 2", value: 2 }
			case "scope3":
				return { label: "Scope 3", value: 3 }
			default:
				return -1
		}
	}

	selectedPurchaseRelatedChange(value) {
		this.setState({ Relation: value })
	}

	selectedTransactionTypeChange(value) {
		if (value != this.state.selectedTransactionType) {
			let Scope3CategoryOption = this.getScope3CategoryOption()
			/* eslint eqeqeq: 0 */
			if (value == 1) {
				this.setState({
					Status: null,
					Supplier:
						this.state.supplierData && this.state.supplierData.length
							? this.state.supplierData[0]
							: "",
					SupplierName:
						this.state.supplierData && this.state.supplierData.length
							? this.state.supplierData[0].label
							: "",
					SupplierFactor: 0,
					industry: "",
					industryFactor: 0,
					NaceCode: "",
					Description: [],
					Scope_1: 0,
					Scope_2: 0,
					Scope_3: 0,
					Amount: 0,
					Scope_3_Category: 0,
					SelectedScope3Category: Scope3CategoryOption[2],
					// TransactionDate: today,
					Notes: "",
					Relation: "",
					selectedTransactionType: "1", // 1  for Emission Source Registration
				})
				/* eslint eqeqeq: 0 */
			} else if (value == 2) {
				this.initialStatus = 2
				this.setState({
					Status: 2,
					Supplier:
						this.state.supplierData && this.state.supplierData.length
							? this.state.supplierData[0]
							: "",
					SupplierName: "",
					SupplierFactor: 0,
					industry: "",
					industryFactor: 0,
					NaceCode: "",
					Description: [],
					Scope_1: 0,
					Scope_2: 0,
					Scope_3: 0,
					Scope: { label: "Scope 3", value: "3" },
					Amount: 0,
					Scope_3_Category: 0,
					SelectedScope3Category: Scope3CategoryOption[0],
					// TransactionDate: date,
					Notes: "",
					selectedTransactionType: "2",
					Relation: "non-production",
				})
				/* eslint eqeqeq: 0 */
			} else if (value == 3) {
				this.initialStatus = 1
				this.setState({
					Status: 1,
					Supplier:
						this.state.supplierData && this.state.supplierData.length
							? this.state.supplierData[0]
							: "",
					SupplierName: "",
					SupplierFactor: 0,
					industry: "",
					industryFactor: 0,
					NaceCode: "",
					Description: [],
					Scope_1: 0,
					Scope_2: 0,
					Scope_3: 0,
					Amount: 0,
					Scope_3_Category: Scope3CategoryOption[0].value,
					SelectedScope3Category: Scope3CategoryOption[0],
					Scope: this.getDefaultScope(),
					//TransactionDate: today,
					Notes: "",
					selectedTransactionType: "3",
					Relation: "non-production",
				})
			}
		}
	}

	naceChange(event) {
		if (event && event.data && event.value) {
			if (event.data && event.data.code) {
				this.setState({
					NaceCode: event.data.code,
					Scope_1: 0,
					Scope_2: 0,
					Scope_3: 0,
					industry: event.data.name,
					industryFactor: event.data.co2Intensity[this.props.reportingYear],
					naceDescription: event.data.naceNotes,
					naceShortName: event.data.shortName,
				})
			} else {
				this.setState({ NaceCode: "", industry: "", industryFactor: "" })
			}
			let co2Factor = event.data.co2Intensity[this.props.reportingYear]
			let { Scope, Scope_1, Scope_2, Scope_3, Amount } = this.state
			if (Scope === 1) {
				Scope_1 = ((co2Factor * Amount) / 1000).toFixed(2)
			}
			if (Scope === 2) {
				Scope_2 = ((co2Factor * Amount) / 1000).toFixed(2)
			}
			if (Scope === 3) {
				Scope_3 = ((co2Factor * Amount) / 1000).toFixed(2)
			}
			this.setState({ Scope_1, Scope_2, Scope_3 })
		}
	}

	naceInputChange(inputValue) {
		const value = inputValue.toLowerCase()
		return new Promise(resolve => {
			if (this.state.naceData && this.state.naceData.length) {
				let naceData = []
				this.state.naceData.forEach(nace => {
					if (nace.code.includes(inputValue) || nace.name.toLowerCase().includes(value)) {
						naceData.push({ label: `(${nace?.code}) ${nace?.name}`, value: nace.code, data: nace })
					}
				})
				resolve(naceData)
			} else {
				resolve([])
			}
		})
	}

	selectedRecalculationChange(value) {
		// if (value === "NaceCode-Industry") {
		// 	let { Scope, Scope_1C, Scope_2C, Scope_3C, Amount, industryFactor } = this.state
		// 	industryFactor = industryFactor ? industryFactor : 0
		// 	if (Scope === 1) {
		// 		Scope_1C = (industryFactor * Amount) / 1000
		// 	}
		// 	if (Scope === 2) {
		// 		Scope_2C = (industryFactor * Amount) / 1000
		// 	}
		// 	if (Scope === 3) {
		// 		Scope_3C = ((industryFactor * Amount) / 1000).toFixed(2)
		// 	}
		// 	this.setState({ Scope_1: Scope_1C, Scope_2: Scope_2C, Scope_3: Scope_3C })
		// }

		if (value === "SupplierFactor") {
			let { Scope, Scope_1, Scope_2, Scope_3, Amount, supplierFactor } = this.state
			supplierFactor = supplierFactor ? supplierFactor : 0
			if (Scope === 1) {
				Scope_1 = ((supplierFactor * Amount) / 1000).toFixed(2)
			}
			if (Scope === 2) {
				Scope_2 = ((supplierFactor * Amount) / 1000).toFixed(2)
			}
			if (Scope === 3) {
				Scope_3 = ((supplierFactor * Amount) / 1000).toFixed(2)
			}
			this.setState({ Scope_1: Scope_1, Scope_2: Scope_2, Scope_3: Scope_3, Factor: -1 })
		}
		this.setState({
			selectedRecalculation: value,
		})
	}

	handleChangeScope(event) {
		this.setState({ Scope_3_Category: event.id })
		this.setState({ defaultScope3Catagory: event })
	}

	handleChangeCustomEmmisionFactor(value) {
		let { Scope, Amount } = this.state
		let calculated = (Number(value) * Amount) / 1000
		this.setState({ changedScopeValue: calculated })
		if (Scope === 1) {
			this.setState({ Scope_1: calculated })
		} else if (Scope === 2) {
			this.setState({ Scope_2: calculated })
		} else if (Scope === 3) {
			this.setState({ Scope_3: calculated })
		}

		// I am doing this because of we need custom factor value when user will select
		// custom factor. If user will select any other option then we will reset the factor value
		// to -1

		this.setState({ customEmmisonFactor: value, Factor: value })
	}

	handleChangeSupplierName(event) {
		this.setState({
			SupplierName: event.label,
			supplierFactor:
				typeof event.SupplierCo2Intensity == "object"
					? event.SupplierCo2Intensity[this.props.reportingYear] || 0
					: event.SupplierCo2Intensity,
			SupplierValue: event,
			SupplierID: event.value,
			NaceCode: event.NaceCode || null,
			industryFactor:
				typeof event.IndustryCo2Intensity == "object"
					? event.IndustryCo2Intensity[this.props.reportingYear] || null
					: event.IndustryCo2Intensity,
			industry: event.Industry,
		})
	}

	componentDidMount() {
		if (this.SupplierName) this.SupplierName.focus()
		if (!this.props.isAdd) {
			let checkEditDetailsBtnContainer = setInterval(() => {
				//Add edit details btn
				var button = document.createElement("input")
				button.type = "button"
				button.id = "edit-details-btn"
				button.value = this.props.t("EditDetails")
				button.classList =
					"rounded-lg px-3 py-1 font-bold text-base text-sky-700 bg-sky-100 hover:bg-sky-200 duration-300"

				button.onclick = () => {
					const dialogButtons = document.querySelectorAll(
						"#transaction-grid_dialogEdit_wrapper .e-footer-content button"
					)
					if (dialogButtons && dialogButtons.length >= 2) {
						const cancelButton = dialogButtons[1]
						cancelButton.click()
						this.props.openEditDetailsModal(this.state)
					}
				}
				let selectPanel = document
					.getElementById("transaction-grid_dialogEdit_wrapper")
					.getElementsByClassName("e-footer-content")[0]
				if (selectPanel) {
					let checkBtn = document.getElementById("edit-details-btn")
					if (!checkBtn) {
						selectPanel.appendChild(button)
					}
					clearInterval(checkEditDetailsBtnContainer)
				}
			}, 500)
		}
	}

	closeDialogue() {
		const dialogButtons = document.querySelectorAll(
			"#transaction-grid_dialogEdit_wrapper .e-footer-content button"
		)
		if (dialogButtons && dialogButtons.length >= 2) {
			const cancelButton = dialogButtons[1]
			cancelButton.click()
			this.props.navigate("/suppliers")
			//history('')
			//this.props.setTransactionsForEditDetails(this.state)

			//this.props.setShowEditDetailsDialog(true)
		}
	}

	render() {
		const { t, reportingYear } = this.props
		return (
			<>
				<div className="transactions-dialog-container min-w-[22rem] md:min-w-[32rem] max-w-[45rem]">
					{this.props.isAdd ? (
						<>
							{this.state.selectedTransactionType === "2" && (
								<div
									className="bg-sky-100 border-l-4 mb-3 border-sky-500 text-blue-700 p-2"
									role="alert"
								>
									{t("Scope 1 and Scope 2 cannot be added using this method ")}{" "}
								</div>
							)}
							{this.state.selectedTransactionType === "2" && this.state.SupplierFactor == 0 && (
								<div
									className="bg-rose-100 border-l-4 mb-3 border-rose-500 text-rose-700 p-2"
									role="alert"
								>
									{t("Supplier is required ")}{" "}
									<span
										aria-hidden
										className="text-blue underline cursor-pointer"
										onClick={this.closeDialogue}
									>
										{t("add_supplier")}
									</span>
								</div>
							)}
							{this.state.selectedTransactionType === "2" &&
								this.state.supplierData.length === 0 && (
									<div
										className="bg-rose-100 border-l-4 mb-3 border-rose-500 text-rose-700 p-2"
										role="alert"
									>
										{t("Supplier_list_empty")}{" "}
										<span
											aria-hidden
											className="text-blue underline cursor-pointer"
											onClick={this.closeDialogue}
										>
											{t("add_supplier")}
										</span>
									</div>
								)}
							{this.state.selectedTransactionType === "2" && this.state.Amount <= 0 && (
								<div
									className="bg-rose-100 border-l-4 mb-3 border-rose-500 text-rose-700 p-2"
									role="alert"
								>
									{t("Amount_zero")}{" "}
								</div>
							)}
							<input
								id="SupplierFactor"
								name="SupplierFactor"
								type="hidden"
								disabled={false}
								value={this.state.SupplierFactor}
								required={true}
								onChange={() => {}}
							/>
							<input
								id="SupplierName"
								name="SupplierName"
								type="hidden"
								disabled={false}
								value={this.state.SupplierName}
								required={true}
								onChange={() => {}}
							/>

							<input
								id="NaceCode"
								name="NaceCode"
								type="hidden"
								disabled={false}
								value={this.state.NaceCode}
								required={true}
								onChange={() => {}}
							/>
							{/* eslint eqeqeq: 0  */}
							{this.state.Status == 3 ? (
								<div className="alert alert-danger">{t("RedFlagMsg")}</div>
							) : (
								<></>
							)}
							<div className="grid grid-cols-1 gap-4">
								<div className="col-span-1">
									<Input
										label={t("TransactionID")}
										id="TransactionID"
										name="TransactionID"
										type="text"
										disable={true}
										value={this.state.TransactionID}
									/>
								</div>
							</div>
							<div className="grid grid-cols-1 gap-4 my-3">
								<div className="col-span-1">
									<input
										type="radio"
										value="1"
										key="EmissionSourceRegistration"
										name="new-transaction-type"
										checked={this.state.selectedTransactionType == "1"}
										onChange={() => this.selectedTransactionTypeChange("1")}
									/>
									<span className="ml-2">{t("EmissionSourceRegistration")}</span>
									<br />
									<input
										type="radio"
										value="2"
										key="PurchaseWithKnownSupplierAmount"
										name="new-transaction-type"
										checked={this.state.selectedTransactionType == "2"}
										onChange={() => this.selectedTransactionTypeChange("2")}
									/>
									<span className="ml-2">{t("PurchaseWithKnownSupplierAmount")}</span>
									<br />
									<input
										type="radio"
										value="3"
										key="TransactionWithKnownEmission"
										name="new-transaction-type"
										checked={this.state.selectedTransactionType == "3"}
										onChange={() => this.selectedTransactionTypeChange("3")}
									/>
									<span className="ml-2">{t("TransactionWithKnownEmission")}</span>
								</div>
							</div>

							{/* Emission Source Registration Options */}
							{
								// eslint eqeqeq: 0
								this.state.selectedTransactionType == "1" ? (
									<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
										<div className="flex flex-col">
											<label className="block font-semibold text-sm leading-4 mb-1 text-slate-800">
												{t("common:SupplierName")}
											</label>
											<input
												type="text"
												value={this.state.Supplier}
												readOnly={true}
												style={{ display: "none" }}
											/>

											<Select
												id="Supplier"
												name="Supplier"
												value={this.state.Supplier}
												onChange={event => {
													this.setState({
														Supplier: event,
														SupplierName: event.label,
														SupplierFactor:
															typeof event.SupplierCo2Intensity == "object"
																? event.SupplierCo2Intensity[reportingYear] || 0
																: event.SupplierCo2Intensity,
														industry: event.Industry || "",
														industryFactor:
															typeof event.IndustryCo2Intensity == "object"
																? event.IndustryCo2Intensity[reportingYear] || 0
																: event.IndustryCo2Intensity || 0,
														NaceCode: event.NaceCode || "",
													})
												}}
												isDisabled={!this.state.supplierData.length > 0}
												options={this.state.supplierData}
												maxMenuHeight={150}
												className="shadow-sm"
											/>
										</div>
										<div>
											<Input
												label={t("TransactionDate")}
												id="TransactionDate"
												name="TransactionDate"
												type="month"
												className={"px-2"}
												value={this.state.TransactionDate}
												handleChange={e => {
													this.onChange(e)
												}}
											/>
										</div>
										<div className="md:col-span-2">
											<Input
												label={t("Description")}
												id="Description"
												name="Description"
												type="text"
												value={this.state.Description}
												handleChange={e => this.onChangeDescription(e)}
											/>
										</div>
									</div>
								) : (
									<></>
								)
							}
							{/* Emission Source Registration Options */}
							{/* Purchase With a Known Supplier Amount Options */}
							{
								/* eslint eqeqeq: 0 */
								this.state.selectedTransactionType == "2" ? (
									<div className="grid grid-cols-1 md:grid-cols-1 gap-4">
										<div className="flex flex-col">
											<input
												type="text"
												value={this.state.Supplier}
												readOnly={true}
												style={{ display: "none" }}
											/>
											<label className="block font-semibold text-sm leading-4 mb-1 text-slate-800">
												{t("common:SupplierName")}
											</label>
											<Select
												id="Supplier"
												name="Supplier"
												value={this.state.Supplier}
												onChange={event => {
													this.setState({
														Supplier: event,
														SupplierName: event.label,
														SupplierFactor:
															typeof event.SupplierCo2Intensity == "object"
																? event.SupplierCo2Intensity[reportingYear] || 0
																: event.SupplierCo2Intensity,
														industry: event.Industry || "",
														industryFactor:
															typeof event.IndustryCo2Intensity == "object"
																? event.IndustryCo2Intensity[reportingYear] || 0
																: event.IndustryCo2Intensity || 0,
														NaceCode: event.NaceCode || "",
													})
												}}
												isDisabled={!this.state.supplierData.length > 0}
												options={this.state.supplierData}
												className={
													this.state.SupplierFactor == 0
														? "!border-2 !border-red-500 rounded-md"
														: ""
												}
												maxMenuHeight={150}
											/>
											{this.state.SupplierFactor == 0 && (
												<span className="text-red-500">Supplier is required</span>
											)}
										</div>
										<div>
											<Input
												label={t("DebitAmount")}
												id="Amount"
												name="Amount"
												type="number"
												disable={false}
												hasError={this.state.Amount == 0 ? true : false}
												errorMessage="Debit Amount can not be 0"
												// value={this.state.Amount}
												placeholder="0"
												handleChange={e => this.onChange(e)}
											/>
										</div>

										<div className="flex flex-col">
											<input
												type="number"
												name="Scope"
												value={this.state.Scope.value}
												readOnly={true}
												style={{ display: "none" }}
											/>
											<label className="block text-sm font-semibold leading-4 mb-1 text-slate-800">
												{t("Scope")}
											</label>
											<Select
												id="ScopeData"
												name="ScopeData"
												className="bg-gray-200"
												value={this.state.Scope}
												onChange={event => {
													this.setState({ Scope: event })
												}}
												isDisabled={true}
												options={this.state.ScopeData}
												maxMenuHeight={150}
											/>
										</div>

										<div className="flex flex-col">
											<input
												type="text"
												value={this.state.Scope_3_Category}
												readOnly={true}
												style={{ display: "none" }}
											/>
											<label className="block font-semibold text-sm leading-4 mb-1 text-slate-800">
												{t("Scope_3_Category")}
											</label>
											<Select
												id="Scope_3_Category"
												name="Scope_3_Category"
												value={this.state.SelectedScope3Category}
												onChange={this.changeSelectedScope3Category.bind(this)}
												options={this.state.Scope3CategoryOption}
												maxMenuHeight={150}
											/>
										</div>

										<div className="flex flex-col justify-content-center">
											<div>
												<input
													type="radio"
													value="Production"
													name="purchase-related"
													checked={this.state.Relation == "Production"}
													onChange={() => this.selectedPurchaseRelatedChange("Production")}
												/>
												<span className="ml-2 radio-label">{t("ProductionRelatedPurchase")}</span>
											</div>

											<div>
												<input
													type="radio"
													value="non-production"
													name="purchase-related"
													checked={this.state.Relation == "non-production"}
													onChange={() => this.selectedPurchaseRelatedChange("non-production")}
												/>
												<span className="ml-2 radio-label">
													{t("NonProductionRelatedPurchase")}
												</span>
											</div>
										</div>

										<div>
											<Input
												label={t("TransactionDate")}
												id="TransactionDate"
												name="TransactionDate"
												type="month"
												className={"px-2"}
												value={this.state.TransactionDate}
												handleChange={e => {
													this.changeTransactionPeriodYear(e)
												}}
											/>
										</div>

										<div className="md:col-span-2">
											<Input
												label={t("Description")}
												id="Description"
												name="Description"
												type="text"
												value={this.state.Description}
												handleChange={e => this.onChangeDescription(e)}
											/>
										</div>

										<div className="flex flex-col">
											<label className="block font-semibold text-sm leading-4 mb-1 text-slate-800">
												{t("Status")}
											</label>
											<DropDownListComponent
												id="Status"
												name="Status"
												value={this.initialStatus}
												itemTemplate={this.flagItemTemplate}
												valueTemplate={this.flagValueTemplate}
												dataSource={this.editorData}
												fields={this.dropDownFields}
												change={this.onChangeFlag.bind(this)}
											/>
											<input style={{ display: "none" }} />
										</div>
									</div>
								) : (
									<></>
								)
							}

							{/* Purchase With a Known Supplier Amount Options */}
							{/* Transaction With a Known Emission Options */}
							{
								/* eslint eqeqeq: 0 */
								this.state.selectedTransactionType === "3" && (
									<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
										<div className="flex flex-col">
											<input
												type="text"
												value={this.state.Supplier}
												readOnly
												style={{ display: "none" }}
											/>
											<label className="block font-semibold text-sm leading-4 mb-1 text-slate-800">
												{t("common:SupplierName")}
											</label>
											<Select
												id="Supplier"
												name="Supplier"
												value={this.state.Supplier}
												onChange={event => {
													this.setState({
														Supplier: event,
														SupplierName: event.label,
														SupplierFactor:
															typeof event.SupplierCo2Intensity === "object"
																? event.SupplierCo2Intensity[reportingYear] || 0
																: event.SupplierCo2Intensity,
														industry: event.Industry || "",
														industryFactor:
															typeof event.IndustryCo2Intensity === "object"
																? event.IndustryCo2Intensity[reportingYear] || 0
																: event.IndustryCo2Intensity || 0,
														NaceCode: event.NaceCode || "",
													})
												}}
												isDisabled={this.state.supplierData.length <= 0}
												options={this.state.supplierData}
												maxMenuHeight={150}
											/>
										</div>
										<div>
											<Input
												label={t("TransactionDate")}
												id="TransactionDate"
												name="TransactionDate"
												type="month"
												className={"px-2"}
												value={this.state.TransactionDate}
												handleChange={e => {
													this.onChange(e)
												}}
											/>
										</div>
										<div>
											<Input
												label={t("Scope 1")}
												id="Scope_1"
												name="Scope_1"
												type="number"
												disable={false}
												value={this.state.Scope_1}
												handleChange={e => this.onChange(e)}
												unit={t("Kg-Co2e")}
											/>
										</div>
										<div>
											<Input
												label={t("Scope 2")}
												id="Scope_2"
												name="Scope_2"
												type="number"
												value={this.state.Scope_2}
												handleChange={e => this.onChange(e)}
												unit={t("Kg-Co2e")}
											/>
										</div>
										<div>
											<Input
												label={t("Scope 3")}
												id="Scope_3"
												name="Scope_3"
												type="number"
												disable={false}
												value={this.state.Scope_3}
												handleChange={e => this.onChange(e)}
												unit={t("Kg-Co2e")}
											/>
										</div>
										<div className="flex flex-col">
											<input
												type="number"
												name="Scope"
												value={this.state.Scope.value}
												readOnly
												style={{ display: "none" }}
											/>
											<label className="block text-sm font-semibold leading-4 mb-1 text-slate-800">
												{t("Scope")}
											</label>
											<Select
												id="ScopeData"
												name="ScopeData"
												value={this.state.Scope}
												onChange={event => {
													this.setState({ Scope: event })
												}}
												options={this.state.ScopeData}
												maxMenuHeight={150}
												className={
													this.state.Scope == -1 ? "!border-2 !border-red-500 rounded-md" : ""
												}
											/>
											{this.state.Scope === -1 && (
												<span className="text-red-500">Scope is required</span>
											)}
										</div>
										<div className="flex flex-col justify-center mt-3">
											<div>
												<input
													type="radio"
													value="Production"
													name="Relation"
													checked={this.state.Relation === "Production"}
													onChange={() => this.selectedPurchaseRelatedChange("Production")}
												/>
												<span className="ml-2 radio-label">{t("ProductionRelatedPurchase")}</span>
											</div>
											<div>
												<input
													type="radio"
													value="non-production"
													name="Relation"
													checked={this.state.Relation === "non-production"}
													onChange={() => this.selectedPurchaseRelatedChange("non-production")}
												/>
												<span className="ml-2 radio-label">
													{t("NonProductionRelatedPurchase")}
												</span>
											</div>
										</div>
										<div className="flex flex-col">
											<input
												type="text"
												value={this.state.Scope_3_Category}
												readOnly
												style={{ display: "none" }}
											/>
											<label className="block text-sm font-semibold leading-4 mb-1 text-slate-800">
												{t("Scope_3_Category")}
											</label>
											<Select
												id="Scope_3_Category"
												name="Scope_3_Category"
												value={this.state.SelectedScope3Category}
												onChange={this.changeSelectedScope3Category.bind(this)}
												options={this.state.Scope3CategoryOption}
												maxMenuHeight={150}
											/>
										</div>
										<div className="md:col-span-2">
											<Input
												label={t("Description")}
												id="Description"
												name="Description"
												type="text"
												value={this.state.Description}
												handleChange={e => this.onChangeDescription(e)}
											/>
										</div>
										<div className="flex flex-col">
											<label className="block font-semibold text-sm leading-4 mb-1 text-slate-800">
												{t("Status")}
											</label>
											<DropDownListComponent
												id="Status"
												name="Status"
												value={this.initialStatus}
												itemTemplate={this.flagItemTemplate}
												valueTemplate={this.flagValueTemplate}
												dataSource={this.editorData}
												fields={this.dropDownFields}
												change={this.onChangeFlag.bind(this)}
											/>
											<input style={{ display: "none" }} />
										</div>
									</div>
								)
							}

							{/* Transaction With a Known Emission Options */}
						</>
					) : (
						<>
							{/* eslint eqeqeq: 0 */}
							{this.state.Status == 3 ? (
								<div
									className="bg-rose-100 border-l-4 mb-3 border-rose-500 text-rose-700 p-2"
									role="alert"
								>
									{t("RedFlagMsg")}
								</div>
							) : (
								<></>
							)}

							<input
								id="SupplierName"
								name="SupplierName"
								type="hidden"
								disabled={false}
								value={this.state.SupplierName}
								required={true}
								onChange={() => {}}
							/>
							<input
								id="customFactor"
								name="customFactor"
								type="hidden"
								disabled={false}
								value={this.state.Factor || -1}
								required={true}
								onChange={() => {}}
							/>
							<input
								id="SupplierID"
								name="SupplierID"
								type="hidden"
								disabled={false}
								value={this.state.SupplierID}
								required={true}
								onChange={() => {}}
							/>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<Input
										label={t("TransactionID")}
										id="TransactionID"
										name="TransactionID"
										type="text"
										disable={true}
										value={this.state.TransactionID}
									/>
								</div>

								<div>
									<label className="font-semibold block text-sm leading-4 mb-1 text-slate-800">
										{t("Status")}
									</label>
									<DropDownListComponent
										id="Status"
										name="Status"
										value={this.initialStatus}
										itemTemplate={this.flagItemTemplate}
										valueTemplate={this.flagValueTemplate}
										dataSource={this.editorData}
										fields={this.dropDownFields}
										change={this.onChangeFlag.bind(this)}
									/>
								</div>
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
								<div>
									<label className="font-semibold block text-sm leading-4 mb-1 text-slate-800">
										{t("common:SupplierName")}
									</label>
									<Select
										id="supplier"
										name="supplier"
										value={this.state.SupplierValue}
										onChange={event => {
											this.handleChangeSupplierName(event)
										}}
										options={this.state.supplierData}
										isDisabled={!this.state.supplierData.length > 0}
										maxMenuHeight={120}
									/>
								</div>

								<div>
									<Input
										label={t("DebitAmount")}
										id="DebitAmount"
										name="Amount"
										type="number"
										disable={true}
										value={this.state.Amount}
									/>
								</div>
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
								<div>
									<Input
										label={t("AccountID")}
										id="AccountID"
										name="AccountID"
										type="text"
										value={this.state.AccountID}
										disable={true}
									/>
								</div>

								<div>
									<Input
										label={t("AccountDescription")}
										id="AccountDescription"
										name="AccountDescription"
										type="text"
										disable={true}
										value={this.state?.AccountDescription || ""}
									/>
								</div>
							</div>

							<div className="grid grid-cols-1 mt-3">
								<div>
									<TextArea
										label={t("Description")}
										id="Description"
										rows={2}
										name="Description"
										type="text"
										value={this.state.Description}
										handleChange={e => this.onChangeDescription(e)}
									/>
								</div>
							</div>

							<div className="border-b-2 border-dotted border-slate-800 mb-4 mt-3">
								<h5 className="text-slate-800 font-bold">Emission Details</h5>
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<Input
										label={t("Scope1")}
										id="Scope_1"
										name="Scope_1"
										type="number"
										disable={false}
										value={this.state.Scope_1}
										handleChange={e => this.onChange(e)}
										unit={t("Kg-Co2e")}
									/>
								</div>

								<div>
									<Input
										label={t("Scope2")}
										id="Scope_2"
										name="Scope_2"
										type="number"
										disable={false}
										value={this.state.Scope_2}
										handleChange={e => this.onChange(e)}
										unit={t("Kg-Co2e")}
									/>
								</div>

								<div>
									<Input
										label={t("Scope3")}
										id="Scope_3"
										name="Scope_3"
										type="number"
										disable={false}
										value={this.state.Scope_3}
										handleChange={e => this.onChange(e)}
										unit={t("Kg-Co2e")}
									/>
								</div>

								<div>
									<label className="font-semibold block text-sm leading-4 mb-1 text-slate-800">
										{t("Scope 3 Category")}
									</label>
									<Select
										id="Scope_3_Catagory"
										name="Scope_3_Catagory"
										value={this.state.defaultScope3Catagory}
										onChange={event => {
											this.handleChangeScope(event)
										}}
										options={this.state.Scope_3_Catagory}
										maxMenuHeight={150}
									/>
								</div>

								<div className="flex items-center">
									<input
										type="radio"
										value="1"
										name="recalculation-type"
										checked={this.state.selectedRecalculation == "SupplierFactor"}
										onChange={() => {
											this.selectedRecalculationChange("SupplierFactor")
											//this.handleChangeCustomEmmisionFactor(this.state.supplierFactor)
										}}
									/>
									<span className="ml-2 mt-1">{t("SupplierFactor")}</span>
								</div>

								<div>
									<Input
										id="SupplierFactor"
										name="SupplierFactor"
										type="number"
										disable={true}
										value={this.state.supplierFactor ? this.state?.supplierFactor.toFixed(2) : ""}
										unit={t("KGPer1000NOK")}
									/>
								</div>

								<div className="flex items-center">
									<input
										type="radio"
										value="1"
										name="recalculation-type"
										checked={this.state.selectedRecalculation == "CustomEmissionFactor"}
										onChange={() => this.selectedRecalculationChange("CustomEmissionFactor")}
									/>
									<span className="ml-2 mt-1">{t("CustomEmissionFactor")}</span>
								</div>

								<div>
									<Input
										id="customSuplier"
										name="customSuplier"
										type="number"
										disable={this.state.selectedRecalculation != "CustomEmissionFactor"}
										value={this.state.customEmmisonFactor}
										handleChange={e => this.handleChangeCustomEmmisionFactor(e.target.value)}
										unit={t("KGPer1000NOK")}
									/>
								</div>

								<div className="flex items-center">
									<input
										type="radio"
										value="1"
										name="isNaceCode"
										checked={this.state.selectedRecalculation == "NaceCode-Industry"}
										onChange={() => this.selectedRecalculationChange("NaceCode-Industry")}
									/>
									<span className="ml-2 mt-1">{t("NaceCode-Industry")}</span>
								</div>

								<div className="e-control-wrapper">
									<AsyncSelect
										id="industry-nace-list"
										isDisabled={this.state.selectedRecalculation != "NaceCode-Industry"}
										placeholder={t("SearchByIndustryOrNaceCode")}
										onChange={this.naceChange.bind(this)}
										loadOptions={this.naceInputChange.bind(this)}
										styles={this.customStyles}
									/>
								</div>

								<div
									className={
										this.state.industryFactor
											? "flex flex-row gap-4 rounded-md border-l-4 col-span-2 border-sky-400 bg-sky-100 p-3 text-sm text-slate-800"
											: "flex flex-row gap-4 rounded-md border-l-4 col-span-2 border-rose-400 bg-red-100 p-3 text-sm text-slate-800"
									}
									role="alert"
								>
									<div className="min-w-fit">
										<h6 className="font-bold text-slate-800">{t("common:NaceCode")}</h6>
										<input
											id="NaceCode"
											name="NaceCode"
											className={
												this.state.industryFactor
													? "w-20 border-0 bg-sky-100 text-base"
													: "border-0 bg-red-100 text-base"
											}
											value={this.state.NaceCode}
											disabled
										/>
									</div>
									<div className="grow">
										<h6 className="flex flex-row font-bold">
											{t("common:Industry")}
											<Pop
												content={{
													color: "sky",
													title: this.state.naceShortName,
													body: this.state.naceDescription,
												}}
											/>
										</h6>
										<p className="text-base mb-0">{this.state.industry}</p>
									</div>
									<div className="min-w-fit">
										<h6 className="font-bold text-slate-800">{t("common:IndustryFactor")}</h6>
										<p className="text-base mb-0">
											{`${this.state.industryFactor ? Number(this.state.industryFactor) : "0.0"}`}
											<span className="pl-2 text-xs text-slate-600">Kg Co2e/kNOK</span>
										</p>
									</div>
								</div>

								<div className="col-span-2">
									<TextArea
										label={t("common:Notes")}
										id="Notes"
										rows={2}
										name="Notes"
										value={this.state.Notes}
										handleChange={e => {
											this.onChange(e)
										}}
									/>
								</div>
							</div>
						</>
					)}
				</div>
			</>
		)
	}
}
