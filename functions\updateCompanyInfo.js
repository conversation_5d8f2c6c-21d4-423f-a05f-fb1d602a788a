exports = async (args) => {
	const { RegistrationNumber, data } = args

	try {
		const company = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("company")
			.updateOne({ RegistrationNumber: RegistrationNumber }, { $set: { ...data } })

		if (company) {
			return { success: true, data: company }
		} else {
			return { success: false, company: [] }
		}
	} catch (error) {
		console.log(error)
		return { success: false }
	}
}
