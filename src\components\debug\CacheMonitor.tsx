/**
 * Cache Monitor Component
 * Provides debugging and monitoring interface for cache performance
 */

import React, { useState, useEffect } from "react"
import { cacheManager, CacheStats } from "../../services/cache-manager"
import { cacheWarmingService } from "../../services/cache-warming"
import { requestDeduplicator } from "../../services/query-cache-integration"

interface CacheMonitorProps {
	isOpen: boolean
	onClose: () => void
}

export const CacheMonitor: React.FC<CacheMonitorProps> = ({ isOpen, onClose }) => {
	const [stats, setStats] = useState<CacheStats | null>(null)
	const [entries, setEntries] = useState<Array<{ key: string; entry: any }>>([])
	const [warmingHistory, setWarmingHistory] = useState<Map<string, any>>(new Map())
	const [currentlyWarming, setCurrentlyWarming] = useState<string[]>([])
	const [pendingRequests, setPendingRequests] = useState<number>(0)
	const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null)

	useEffect(() => {
		if (isOpen) {
			refreshData()
			const interval = setInterval(refreshData, 2000) // Refresh every 2 seconds
			setRefreshInterval(interval)
		} else {
			if (refreshInterval) {
				clearInterval(refreshInterval)
				setRefreshInterval(null)
			}
		}

		return () => {
			if (refreshInterval) {
				clearInterval(refreshInterval)
			}
		}
	}, [isOpen])

	const refreshData = () => {
		setStats(cacheManager.getStats())
		setEntries(cacheManager.getEntries())
		setWarmingHistory(cacheWarmingService.getWarmingHistory())
		setCurrentlyWarming(cacheWarmingService.getCurrentlyWarming())
		setPendingRequests(requestDeduplicator.getPendingCount())
	}

	const clearCache = () => {
		cacheManager.clear()
		refreshData()
	}

	const clearWarmingHistory = () => {
		cacheWarmingService.clearWarmingHistory()
		refreshData()
	}

	const formatBytes = (bytes: number) => {
		if (bytes === 0) return "0 B"
		const k = 1024
		const sizes = ["B", "KB", "MB", "GB"]
		const i = Math.floor(Math.log(bytes) / Math.log(k))
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
	}

	const formatDuration = (ms: number) => {
		if (ms < 1000) return `${ms}ms`
		return `${(ms / 1000).toFixed(2)}s`
	}

	const getEntryAge = (timestamp: number) => {
		const age = Date.now() - timestamp
		return formatDuration(age)
	}

	const getEntryStatus = (entry: any) => {
		const now = Date.now()
		const age = now - entry.timestamp

		if (age > entry.ttl * 2) return "expired"
		if (age > entry.ttl) return "stale"
		return "fresh"
	}

	const getStatusColor = (status: string) => {
		switch (status) {
			case "fresh":
				return "text-green-600"
			case "stale":
				return "text-yellow-600"
			case "expired":
				return "text-red-600"
			default:
				return "text-gray-600"
		}
	}

	if (!isOpen) return null

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
			<div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
				<div className="flex items-center justify-between p-4 border-b">
					<h2 className="text-xl font-semibold">Cache Monitor</h2>
					<button onClick={onClose} className="text-gray-500 hover:text-gray-700">
						✕
					</button>
				</div>

				<div className="p-4 overflow-y-auto max-h-[calc(90vh-120px)]">
					{/* Cache Statistics */}
					<div className="mb-6">
						<h3 className="text-lg font-medium mb-3">Cache Statistics</h3>
						{stats && (
							<div className="grid grid-cols-2 md:grid-cols-5 gap-4">
								<div className="bg-blue-50 p-3 rounded">
									<div className="text-sm text-gray-600">Total Entries</div>
									<div className="text-xl font-semibold">{stats.totalEntries}</div>
								</div>
								<div className="bg-green-50 p-3 rounded">
									<div className="text-sm text-gray-600">Hit Rate</div>
									<div className="text-xl font-semibold">{stats.hitRate.toFixed(1)}%</div>
								</div>
								<div className="bg-red-50 p-3 rounded">
									<div className="text-sm text-gray-600">Miss Rate</div>
									<div className="text-xl font-semibold">{stats.missRate.toFixed(1)}%</div>
								</div>
								<div className="bg-yellow-50 p-3 rounded">
									<div className="text-sm text-gray-600">Evictions</div>
									<div className="text-xl font-semibold">{stats.evictionCount}</div>
								</div>
								<div className="bg-purple-50 p-3 rounded">
									<div className="text-sm text-gray-600">Pending Requests</div>
									<div className="text-xl font-semibold">{pendingRequests}</div>
								</div>
							</div>
						)}
					</div>

					{/* Currently Warming */}
					{currentlyWarming.length > 0 && (
						<div className="mb-6">
							<h3 className="text-lg font-medium mb-3">Currently Warming</h3>
							<div className="bg-blue-50 p-3 rounded">
								{currentlyWarming.map(key => (
									<div key={key} className="flex items-center">
										<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
										<span className="text-sm">{key}</span>
									</div>
								))}
							</div>
						</div>
					)}

					{/* Cache Entries */}
					<div className="mb-6">
						<div className="flex items-center justify-between mb-3">
							<h3 className="text-lg font-medium">Cache Entries</h3>
							<button
								onClick={clearCache}
								className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
							>
								Clear Cache
							</button>
						</div>
						<div className="overflow-x-auto">
							<table className="min-w-full border border-gray-200">
								<thead className="bg-gray-50">
									<tr>
										<th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
											Key
										</th>
										<th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
											Status
										</th>
										<th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
											Age
										</th>
										<th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
											TTL
										</th>
										<th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
											Access Count
										</th>
										<th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
											Priority
										</th>
										<th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
											Tags
										</th>
									</tr>
								</thead>
								<tbody className="bg-white divide-y divide-gray-200">
									{entries.map(({ key, entry }) => {
										const status = getEntryStatus(entry)
										return (
											<tr key={key} className="hover:bg-gray-50">
												<td className="px-4 py-2 text-sm font-mono">{key}</td>
												<td className={`px-4 py-2 text-sm font-medium ${getStatusColor(status)}`}>
													{status}
												</td>
												<td className="px-4 py-2 text-sm">{getEntryAge(entry.timestamp)}</td>
												<td className="px-4 py-2 text-sm">{formatDuration(entry.ttl)}</td>
												<td className="px-4 py-2 text-sm">{entry.accessCount}</td>
												<td className="px-4 py-2 text-sm">
													<span
														className={`px-2 py-1 rounded text-xs ${
															entry.priority === "high"
																? "bg-red-100 text-red-800"
																: entry.priority === "medium"
																	? "bg-yellow-100 text-yellow-800"
																	: "bg-gray-100 text-gray-800"
														}`}
													>
														{entry.priority}
													</span>
												</td>
												<td className="px-4 py-2 text-sm">
													{entry.tags.map((tag: string) => (
														<span
															key={tag}
															className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-1"
														>
															{tag}
														</span>
													))}
												</td>
											</tr>
										)
									})}
								</tbody>
							</table>
						</div>
					</div>

					{/* Warming History */}
					<div className="mb-6">
						<div className="flex items-center justify-between mb-3">
							<h3 className="text-lg font-medium">Warming History</h3>
							<button
								onClick={clearWarmingHistory}
								className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
							>
								Clear History
							</button>
						</div>
						<div className="overflow-x-auto">
							<table className="min-w-full border border-gray-200">
								<thead className="bg-gray-50">
									<tr>
										<th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
											Key
										</th>
										<th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
											Status
										</th>
										<th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
											Duration
										</th>
										<th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
											Error
										</th>
									</tr>
								</thead>
								<tbody className="bg-white divide-y divide-gray-200">
									{Array.from(warmingHistory.entries()).map(([key, result]) => (
										<tr key={key} className="hover:bg-gray-50">
											<td className="px-4 py-2 text-sm font-mono">{key}</td>
											<td className="px-4 py-2 text-sm">
												<span
													className={`px-2 py-1 rounded text-xs ${
														result.success
															? "bg-green-100 text-green-800"
															: "bg-red-100 text-red-800"
													}`}
												>
													{result.success ? "Success" : "Failed"}
												</span>
											</td>
											<td className="px-4 py-2 text-sm">{formatDuration(result.duration)}</td>
											<td className="px-4 py-2 text-sm text-red-600">
												{result.error?.message || "-"}
											</td>
										</tr>
									))}
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}

/**
 * Hook to toggle cache monitor
 */
export function useCacheMonitor() {
	const [isOpen, setIsOpen] = useState(false)

	const openMonitor = () => setIsOpen(true)
	const closeMonitor = () => setIsOpen(false)
	const toggleMonitor = () => setIsOpen(!isOpen)

	return {
		isOpen,
		openMonitor,
		closeMonitor,
		toggleMonitor,
		CacheMonitor: (props: Omit<CacheMonitorProps, "isOpen" | "onClose">) => (
			<CacheMonitor {...props} isOpen={isOpen} onClose={closeMonitor} />
		),
	}
}
