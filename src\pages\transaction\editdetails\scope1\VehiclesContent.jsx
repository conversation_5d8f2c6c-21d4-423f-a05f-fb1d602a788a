/* eslint-disable import/no-unresolved */
import React, { useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"
import Select from "react-select"

import { co2_mix } from "../scope2/constants/electricTransportation"

import { FACTORS_DATA_COMBUSTION } from "./constants/combustion"
import { FUEL_TYPE_LIST, VEHICLE_TYPE_LIST } from "./constants/vehicles"

import Button from "@/components/ui/Button"
import Input from "@/components/ui/Input"
import { useToast } from "@/hooks"

function VehicleContent(props) {
	const { vehicleRows, updateVehiclesScope, PeriodYear } = props
	const distanceInputRef = useRef()
	const { t } = useTranslation("transactions")
	const toast = useToast()

	const [vehicle, setVehicle] = useState(VEHICLE_TYPE_LIST[0].label)
	const [fuel, setFuel] = useState(FUEL_TYPE_LIST[0].label)

	const [vehicleType, setVehicleType] = useState(VEHICLE_TYPE_LIST[0])
	const [economy, setEconomy] = useState(VEHICLE_TYPE_LIST[0].economy)
	const [fuelType, setFuelType] = useState(FUEL_TYPE_LIST[0])
	const [distance, setDistance] = useState(0)

	const addVehicleRow = (
		scope1,
		scope2,
		scope3,
		distance,
		economy,
		kwh,
		mobileCombustion,
		renewable,
		non_renewable_energy
	) => {
		const newVehicleRow = {
			vehicleType: vehicle,
			fuelType: fuel,
			distance: Number(distance),
			economy: economy,
			scope1: Number(scope1),
			scope2: Number(scope2),
			scope3: Number(scope3),
			distanceUnit: "km",
			economyUnit: fuelType.value === "electric" ? "kwh/100km" : "km/100km",
			kwh: Number(kwh),
			mobileCombustion: Number(mobileCombustion),
			renewable: Number(renewable),
			non_renewable_energy: Number(non_renewable_energy),
		}

		const newVehicles = [...vehicleRows, newVehicleRow]

		updateVehiclesScope(newVehicleRow, false, newVehicles)
	}

	const calculateVehicleEmission = async () => {
		let renewable = 0
		let non_renewable_energy = 0
		let scope1 = 0
		let scope3 = 0

		if (vehicleType.value === "heavytruck" && !economy) {
			toast("error", t("economy_heavy_truck_error"))
			return
		} else if (!(parseFloat(distance) >= 0)) {
			toast("error", t("vehicle_distance_error"))
		}

		let kwh = 0

		let mobileComb = 0

		let combustionMetric = ""
		if (fuelType.value === "petrol") {
			combustionMetric = "PetrolForecourt"
		} else if (fuelType.value === "diesel") {
			combustionMetric = "DieselForecourt"
		} else if (fuelType.value === "hybrid") {
			if (fuelType.name === "Petrol") {
				combustionMetric = "PetrolForecourt"
			} else if (fuelType.name === "Diesel") {
				combustionMetric = "DieselForecourt"
			}
		}

		if (fuelType.value != "electric") {
			if (combustionMetric) {
				let newEconomy = economy ? economy : vehicleType.economy

				if (fuelType.value === "hybrid") {
					newEconomy = vehicleType.economy * 0.85
				}
				let newDistance = distance ? Number(distance) : 1

				scope1 = (
					FACTORS_DATA_COMBUSTION[combustionMetric].scope1 *
					distance *
					(newEconomy / 100)
				).toFixed(2)
				scope3 = (
					FACTORS_DATA_COMBUSTION[combustionMetric].scope3 *
					distance *
					(newEconomy / 100)
				).toFixed(2)
				let liters = (economy / 100) * newDistance
				mobileComb += (liters * FACTORS_DATA_COMBUSTION[combustionMetric].kwh).toFixed(2)

				addVehicleRow(
					scope1,
					0,
					scope3,
					newDistance,
					newEconomy,
					0,
					mobileComb,
					renewable,
					non_renewable_energy
				)
				//vehiclesData[i].scope3 = scope3 + scope1
			}
		} else {
			// if economy is 0 then use API data for calculations
			let factor = 0

			//console.log(combustionMetric)

			if (!economy) {
				factor = vehicleType.elEconomy - 0.014
			} else {
				factor = economy - 0.014
			}

			kwh += ((distance * factor) / 100).toFixed(2)

			non_renewable_energy = (kwh * 0.02).toFixed(2)
			renewable = (kwh * 0.98).toFixed(2)
			// let scope2 = 0;

			let scope2 = (kwh * co2_mix[PeriodYear]["NO"]["consumption"]).toFixed(2)
			let scope3 = 0.014
			//let s = factorData.kwh ? factorData.kwh : 10economy

			addVehicleRow(
				scope1,
				scope2,
				scope3,
				distance,
				economy,
				kwh,
				0,
				renewable,
				non_renewable_energy
			)
			//carIndex++
		}

		return { success: true }
	}

	const deleteVehicleField = index => {
		let vehicleRow = vehicleRows[index]
		let newVehicleRows = [...vehicleRows]
		newVehicleRows.splice(index, 1)

		updateVehiclesScope(vehicleRow, true, newVehicleRows)
	}

	const handleFocus = event => event.target.select()

	return (
		<>
			{vehicleRows.map((vehicle, index) => (
				<div key={index}>
					<div className="grid grid-cols-6 gap-4 my-1 saved-emission">
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("VehicleType")}
								<span className="text-red-600">*</span>
							</label>
							<div>{vehicle.vehicleType}</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("FuelType")}
								<span className="text-red-600">*</span>
							</label>
							<div>{vehicle.fuelType}</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("Distance")}
								<span className="text-red-600">*</span>
							</label>
							<div className="flex relative">
								<span>{vehicle.distance}</span>
								<span className="text-nowrap custom-span-unit-value-save">
									{vehicle.distanceUnit}
								</span>
							</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("Economy")}
							</label>
							<div className="flex relative">
								<span>{vehicle.economy ? vehicle.economy : "--"}</span>
								<span className="text-nowrap custom-span-unit-value-save">
									{vehicle.economy && vehicle.economyUnit
										? vehicle.economyUnit.replace("per", "/")
										: ""}
								</span>
							</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("TotalCo2e")}
							</label>
							<div className="flex relative">
								<span>{Number(vehicle.scope1 + vehicle.scope2 + vehicle.scope3).toFixed(2)}</span>
								<span className="text-nowrap custom-span-unit-value-save">{t("kg")}</span>
							</div>
						</div>

						<div className="delete-icon !items-center">
							<span aria-hidden onClick={() => deleteVehicleField(index)}>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}
			<div className="grid grid-cols-5 gap-5 my-2">
				<div>
					<label
						className="text-slate-800 font-semibold whitespace-nowrap"
						htmlFor={"vehicle-type"}
					>
						{t("VehicleType")}
						<span className="text-red-600">*</span>
					</label>
					<Select
						id={"vehicle-type"}
						value={vehicleType || null}
						onChange={e => {
							setVehicle(e.label)
							setVehicleType(e)
							setEconomy(e.economy)
						}}
						options={VEHICLE_TYPE_LIST}
					/>
				</div>
				<div>
					<label
						className="text-slate-800 font-semibold whitespace-nowrap"
						htmlFor={"fuel-vehicle"}
					>
						{t("FuelType")}
						<span className="text-red-600">*</span>
					</label>
					<Select
						id={"fuel-vehicle"}
						value={fuelType || null}
						onChange={e => {
							setFuelType(e)
							setFuel(e.label)
							if (e.value === "electric") {
								setEconomy(vehicleType.elEconomy)
							} else {
								setEconomy(vehicleType.economy)
							}
						}}
						options={FUEL_TYPE_LIST}
					/>
				</div>

				<div>
					<Input
						label={t("Distance")}
						type={"number"}
						required
						placeholder={"distance"}
						labelColor="text-sky-500"
						value={distance}
						ref={distanceInputRef}
						unit={"km"}
						handleChange={e => setDistance(e.target.value)}
						handleFocus={handleFocus}
					/>
				</div>
				<div>
					<Input
						label={t("Economy")}
						type={"number"}
						labelColor="text-sky-500"
						value={economy}
						handleChange={e => setEconomy(e.target.value)}
						handleFocus={handleFocus}
						placeholder={"economy"}
						unit={fuelType.value === "electric" ? "kwh/100km" : "km/100"}
					/>
				</div>
				<div className="self-end mb-1">
					<Button
						handleClick={() => calculateVehicleEmission()}
						title={`${t("Add")} | +`}
						color="sky-500"
					/>
				</div>
			</div>
		</>
	)
}

export default VehicleContent
