import { Card, Title, BarChart } from "@tremor/react"
import React from "react"

import { valueFormat } from "../../services/helper"

export default function BarChartComponent({
	language = "en",
	Data = [],
	title = "",
	index = "month",
	height = "266px",
	showLegend = true,
	showCustomLegend = false,
}) {
	const customTooltip = ({ payload, active }) => {
		if (!active || !payload) return null
		return (
			<div className="w-56 rounded-tremor-default text-tremor-default bg-tremor-background p-2 shadow-tremor-dropdown border border-tremor-border">
				{payload.map((category, idx) => (
					<div key={idx} className="flex items-center space-x-2.5">
						<div className={`w-1.5 h-1.5 flex flex-row bg-${category.color}-500 rounded`} />
						<div className="flex items-center first-line:space-x-8">
							<p className="text-tremor-content ml-3">{category.dataKey}</p>
							<p className="font-medium text-tremor-content-emphasis ml-3">
								{valueFormat(category.value, language, "value")}
							</p>
						</div>
					</div>
				))}
			</div>
		)
	}

	const format = number => {
		return Intl.NumberFormat(language, {
			maximumFractionDigits: 2,
		}).format(number)
	}

	const filterData = () => {
		// filter monthly data so that if there is no data in any of the scope, it will not be shown
		const filterData = Data.filter(data => data.Scope1 > 0 || data.Scope2 > 0 || data.Scope3 > 0)

		// if data is not empty, return the monthly data

		if (filterData.length > 0) return Data

		// if data is empty, return an empty array

		return []
	}

	return (
		<Card>
			<Title>{title}</Title>
			{showCustomLegend && (
				<div className="flex justify-start items-center ml-4 my-1">
					<div className="w-1.5 h-1.5 bg-rose-500 rounded" />
					<p className="text-tremor-content ml-2">Scope1</p>
					<div className="w-1.5 h-1.5 bg-sky-500 rounded ml-2" />
					<p className="text-tremor-content ml-2">Scope2</p>
					<div className="w-1.5 h-1.5 bg-amber-500 rounded ml-2" />
					<p className="text-tremor-content ml-2">Scope3</p>
				</div>
			)}
			<BarChart
				style={{ height: height, marginTop: "10px" }}
				data={filterData()}
				index={`${index}`}
				showLegend={showLegend}
				categories={["Scope1", "Scope2", "Scope3"]}
				colors={["rose", "sky", "amber"]}
				stack={true}
				customTooltip={customTooltip}
				valueFormatter={format}
				yAxisWidth={40}
				showAnimation
			/>
		</Card>
	)
}
