import { combineReducers } from "redux"

import CompanyReducer from "./CompanyReducer"
import ProjectReportReducer from "./ProjectReportReducer"
import ReportingEntityReducer from "./ReportingEntityReducer"
import ReportingReducer from "./ReportingReducer"
import ReportUploadingReducer from "./ReportUploadingReducer"
import ScopeReducer from "./ScopeReducer"
import SubscriptionReducer from "./SubscriptionReducer"
import UserReducers from "./UserReducers"

const rootReducer = combineReducers({
	user: UserReducers,
	reporting: ReportingReducer,
	subscription: SubscriptionReducer,
	uploading: ReportUploadingReducer,
	reportingEntity: ReportingEntityReducer,
	company: CompanyReducer,
	ScopeData: ScopeReducer,
	ProjectReport: ProjectReportReducer,
})

export default rootReducer
