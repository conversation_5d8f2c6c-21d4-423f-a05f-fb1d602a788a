/* eslint-disable import/no-unresolved */
import axios from "axios"
import React, { useState } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line, RiInformationLine } from "react-icons/ri"
import Select from "react-select"

import airports from "../contants/Airports.json"
import { CATEGORY6F_FLIGHT_CLASS_LIST, CATEGORY6F_FLIGHT_TYPE_LIST } from "../contants/category6"

import Button from "@/components/ui/Button"
import Input from "@/components/ui/Input"
import { useToast } from "@/hooks"

function Category6F(props) {
	const { flightRows, updateFloghtEmission, showLoader, hideLoader } = props
	const { t } = useTranslation("transactions")

	const toast = useToast()

	const [fromFlights, setFromFlights] = useState([])
	const [viaFlights, setViaFlights] = useState([])
	const [destinationFlights, setDestinationFlights] = useState([])

	const [flightType, setFlightType] = useState(CATEGORY6F_FLIGHT_TYPE_LIST[0])
	const [flightTypeLabel, setFlightTypeLabel] = useState(CATEGORY6F_FLIGHT_TYPE_LIST[0].label)

	const [flightFrom, setFlightFrom] = useState({})
	const [flightFromLabel, setFlightFromLabel] = useState("")

	const [flightVia, setFlightVia] = useState({})
	const [flightViaLabel, setFlightViaLabel] = useState("")

	const [flightDestination, setFlightDestination] = useState({})
	const [flightDestinationLabel, setFlightDestinationLabel] = useState("")

	const [flightClass, setFlightClass] = useState(CATEGORY6F_FLIGHT_CLASS_LIST[0])
	const [flightClassLabel, setFlightClassLabel] = useState(CATEGORY6F_FLIGHT_CLASS_LIST[0].label)

	const [travellers, setTravellers] = useState(1)

	const [isRadioActive, setIsRadioActive] = useState(false)

	const flightTypeChange = event => {
		setFlightType(event)
		setFlightTypeLabel(event.label)
	}

	const searchAirportName = airportName => {
		let searchList = airports.airports
			.filter(airport => airport.airport_code.toLowerCase() === airportName)
			.map(airport => {
				return {
					label: airport.airport,
					value: airport.airport_code,
				}
			})

		//If no matches found by code, check other attributes
		if (searchList.length === 0) {
			searchList = airports.airports
				.filter(
					airport =>
						airport.airport_name.toLowerCase().includes(airportName) ||
						airport.town.toLowerCase().includes(airportName) ||
						airport.country.toLowerCase().includes(airportName)
				)
				.map(airport => {
					return {
						label: airport.airport,
						value: airport.airport_code,
					}
				})
		}

		return searchList
	}

	const fromInputChange = event => {
		if (event.trim()) {
			if (event.length <= 2) {
				setFromFlights([])
				return
			}

			let searchAirport = event.toLowerCase()

			let searchList = searchAirportName(searchAirport)

			setFromFlights(() => [...searchList])
		} else {
			setFromFlights([])
		}
	}

	const viaInputChange = event => {
		if (event.trim()) {
			if (event.length <= 2) {
				setFromFlights([])
				return
			}

			let searchAirport = event.toLowerCase()

			let searchList = searchAirportName(searchAirport)

			setViaFlights(() => [...searchList])
		} else {
			setViaFlights([])
		}
	}

	const destinationInputChange = event => {
		if (event.trim()) {
			if (event.length <= 2) {
				setDestinationFlights([])
				return
			}

			let searchAirport = event.toLowerCase()

			let searchList = searchAirportName(searchAirport)

			setDestinationFlights(() => [...searchList])
		} else {
			setDestinationFlights([])
		}
	}

	const fromChange = event => {
		setFlightFrom(event)
		setFlightFromLabel(event.label)
	}

	const viaChange = event => {
		setFlightVia(event)
		setFlightViaLabel(event.label)
	}

	const destinationChange = event => {
		setFlightDestination(event)
		setFlightDestinationLabel(event.label)
	}

	const flightClassChange = event => {
		setFlightClass(event)
		setFlightClassLabel(event.label)
	}

	const travellerChange = event => {
		setTravellers(event.target.value)
	}

	const radiativeFactorChange = event => {
		setIsRadioActive(event.target.checked)
	}

	const addFlightRow = scope3 => {
		const newCategory6F = {
			flightType: flightTypeLabel,
			from: flightFromLabel,
			via: flightViaLabel,
			destination: flightDestinationLabel,
			flightClass: flightClassLabel,
			traveller: travellers,
			radiativeFactor: isRadioActive,
			scope3: scope3,
		}

		const newRows = [...flightRows, newCategory6F]

		updateFloghtEmission(newCategory6F, false, "scope3Category6FlightRows", newRows, 6)
	}

	const transportationObj = async () => {
		const date = new Date()

		const route = [flightFrom.value]
		if (flightVia?.value) {
			route.push(flightVia?.value)
		}
		route.push(flightDestination.value)

		showLoader()

		const payload = {
			route,
			return: flightType.value === "return",
			class: flightClass.value,
			departureDate: `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`,
			ir_factor: isRadioActive,
			travellers: Number(travellers),
		}

		const response = await axios.post(
			`https://g-flightapi-318955611692.europe-north1.run.app/api/v1/calculate-emissions`,
			payload,
			{
				headers: {
					"Content-Type": "application/json",
					Accept: "application/json",
				},
			}
		)

		hideLoader()

		return response.data.total_emissions
	}

	const calculateFlightEmission = async () => {
		if (!flightFrom?.value && !flightDestination?.value && parseInt(travellers) === 0) {
			toast("error", t("flight_details_error"))
			return
		}

		let scope3 = await transportationObj()

		addFlightRow(scope3)
	}

	const deleteCategory6FField = index => {
		const newRows = [...flightRows]
		newRows.splice(index, 1)

		updateFloghtEmission(flightRows[index], true, "scope3Category6FlightRows", newRows, 6)
	}

	const handleFocus = event => event.target.select()

	return (
		<>
			{flightRows.map((category6F, index) => (
				<div key={index}>
					<div className="grid grid-cols-5 gap-3 my-2 saved-emission">
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("FlightType")}
							</label>
							<div>{category6F.flightType}</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">{t("From")}</label>
							<div>{category6F.from}</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">{t("Via")}</label>
							<div>
								<span>{category6F.via}</span>
							</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("Destination")}
							</label>
							<div>
								<span>{category6F.destination}</span>
							</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("FlightClass")}
							</label>
							<div>
								<span>{category6F.flightClass}</span>
							</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("Traveller")}
							</label>
							<div>
								<span>{category6F.traveller}</span>
							</div>
						</div>
						<div className="flex items-center">
							<input
								type="checkbox"
								id={"radiative-factor-" + index}
								className="radiative-factor"
								defaultChecked={category6F.radiativeFactor}
								disabled="true"
							></input>

							<label className="text-slate-800 font-semibold whitespace-nowrap ml-1">
								{t("AddRadiativeFactor")}
							</label>
						</div>

						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("TotalCo2e")}
							</label>
							<div className="d-flex position-relative">
								<span>{Number(category6F.scope3).toFixed(2)}</span>
								<span className="text-nowrap custom-span-unit-value-save">{t("kg")}</span>
							</div>
						</div>

						<div className="!items-center delete-icon">
							<span aria-hidden onClick={() => deleteCategory6FField(index)}>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}
			<div className="grid grid-cols-4 gap-3 my-2">
				<div>
					<label
						className="text-slate-800 font-semibold whitespace-nowrap"
						htmlFor={"c6f-flight_type-"}
					>
						{t("FlightType")}
					</label>
					<Select
						id={"c6f-flight_type-"}
						value={flightType}
						onChange={flightTypeChange}
						options={CATEGORY6F_FLIGHT_TYPE_LIST}
					/>
				</div>
				<div>
					<label className="text-slate-800 font-semibold whitespace-nowrap" htmlFor={"c6f-from-"}>
						{t("From")}
					</label>
					<Select
						id={"c6f-from-"}
						value={flightFrom}
						onChange={fromChange}
						options={fromFlights}
						onInputChange={fromInputChange}
						placeholder={t("SearchAirport")}
					/>
				</div>
				<div>
					<label className="text-slate-800 font-semibold whitespace-nowrap" htmlFor={"c6f-via-"}>
						{t("Via")}
					</label>
					<Select
						id={"c6f-via-"}
						value={flightVia}
						onChange={viaChange}
						options={viaFlights}
						onInputChange={viaInputChange}
						placeholder={t("SearchAirport")}
					/>
				</div>
				<div>
					<label
						className="text-slate-800 font-semibold whitespace-nowrap"
						htmlFor={"c6f-destination-"}
					>
						{t("Destination")}
					</label>
					<Select
						id={"c6f-destination-"}
						value={flightDestination}
						onChange={destinationChange}
						options={destinationFlights}
						onInputChange={destinationInputChange}
						placeholder={t("SearchAirport")}
					/>
				</div>
				<div>
					<label
						className="text-slate-800 font-semibold whitespace-nowrap"
						htmlFor={"c6f-flight-class-"}
					>
						{t("FlightClass")}
					</label>
					<Select
						id={"c6f-flight-class-"}
						value={flightClass}
						onChange={flightClassChange}
						options={CATEGORY6F_FLIGHT_CLASS_LIST}
					/>
				</div>
				<div>
					<Input
						labelColor="text-sky-500"
						label={t("Traveller")}
						value={travellers}
						handleChange={travellerChange}
						placeholder="Traveller"
						handleFocus={handleFocus}
					/>
				</div>
				<div className=" self-end">
					<div className="flex items-center ">
						<input
							type="checkbox"
							id={"c6f-radiative-factor-"}
							className="radiative-factor"
							defaultChecked={isRadioActive}
							onChange={radiativeFactorChange}
							onFocus={handleFocus}
						></input>
						<span className="ml-1 radiative-factor-text flex">
							<span className="radiative-value text-wrap">{t("AddRadiativeFactor")}</span>
							<span className="tooltip">
								<RiInformationLine className="ml-1 tooltip-icon" />
								<span className="tooltiptext">
									{
										"Carbon emissions from planes at high altitude have an increased effect on global warming. Tick the box if you would like to multiply aviation emissions by DEFRA's recommended Radiative Forcing factor of 1.891."
									}
								</span>
							</span>
						</span>
					</div>
				</div>
				<div className=" self-end mb-1">
					<Button
						title={`${t("Add")} | +`}
						handleClick={() => calculateFlightEmission()}
						color="sky-500"
					/>
				</div>
			</div>
		</>
	)
}

export default Category6F
