const fileService = require("../src/server/upload-handler.cjs")

exports.handler = async (event) => {
	if (event.httpMethod !== "GET") {
		return {
			statusCode: 405,
			body: JSON.stringify({ error: "Method Not Allowed" }),
			headers: { "Content-Type": "application/json" },
		}
	}

	return new Promise((resolve) => {
		// Mock Express req and res for getting files
		const req = {} // No request body needed for GET
		const res = {
			status: function (statusCode) {
				return {
					json: function (data) {
						resolve({
							statusCode,
							body: JSON.stringify(data),
							headers: { "Content-Type": "application/json" },
						})
					},
				}
			},
		}

		try {
			fileService.getFiles(req, res)
		} catch (error) {
			resolve({
				statusCode: 500,
				body: JSON.stringify({ error: "Internal Server Error" }),
				headers: { "Content-Type": "application/json" },
			})
		}
	})
}
