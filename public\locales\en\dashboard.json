{"Including_dwonstream": "Including downstream", "Category9_15": "Category 9 - 15 - Downstream Emissions", "fetch_intensity_error": "Sorry something went wrong you need to add revenue manually", "lookup_return_revenue": "Lookup returned revenue for year", "not_set_yet": "not set yet", "missing data": "There are mainly two reasons why we do not have data on these. The supplier has ceased operations, or it is a foreign supplier for which we do not have an industry code. To prevent this from being lost, we use a template factor for these.", "Røde flagg": "Red flags", "p1": "For the best possible reporting, we have flagged some transactions with a red flag.", "p1.1": "Please review red flagged transactions manually. These are possibly important transactions.", "travel_heading": "Travel", "travel": "It is important to obtain accurate data on travel activities. Many people record their travels under payroll in the accounting system, and unfortunately, they do not automatically appear in the transactions list as we don't include any payroll accountIds for privacy reasons. Travel expenses reimbursed over payroll need to be entered manually here.", "travel2": "Employees' commuting to and from work is not recorded in the accounting system and must therefore be added manually.", "commuting": "Add employee commuting.", "Emission_Per_Scope": "Emission Per <PERSON>", "Dashboard": "Dashboard", "total CO2 emissions": "total CO2 emissions", "Scope 1 emissions": "Scope 1 emissions", "Scope 2 emissions": "Scope 2 emissions", "Scope 3 emissions": "Scope 3 upstream emissions", "Category": "Category", "Scope 3 categories": "Scope 3 Categories", "Purchased Goods and Services": "Purchased Goods and Services", "Capital Goods": "Capital Goods", "Fuel and Energy Related Activities": "Fuel and Energy Related Activities", "Upstream Transportation and Distribution": "Upstream Transportation and Distribution", "Waste Generated in Operations": "Waste Generated in Operations", "Business Travel": "Business Travel", "Employee Commuting": "Employee Commuting", "Upstream leased assets": "Upstream leased assets", "Top 5 suppliers": "Top 5 suppliers", "Supplier Name": "Supplier Name", "Emissions": "Emissions", "CO2 added": "CO2 added", "Total Co2 added": "Total Co2 added", "Total emissions": "Total emissions", "Production related capital goods": "Production related capital goods", "Production related intermediate products": "Production related intermediate products", "Production related final products": "Production related final products", "Energy overview": "Energy overview", "Stationary combustion": "Stationary combustion", "Mobile combustion": "Mobile combustion", "Renewable electricity": "Renewable electricity", "Non-Renewable electricity": "Non-Renewable electricity", "Nuclear energy": "Nuclear energy", "Total energy to output": "Total energy consumption", "Percentage": "Percentage", "Kilowatt hours": "<PERSON><PERSON><PERSON> hours", "Energy source": "Energy source", "consumption-based": "Consumption-based", "location-based": "Location-based", "market-based": "Market-based", "Carbon intensity": "Carbon intensity", "Total emissions for": "Total emissions for ", "Revenue for reporting year": "Revenue for reporting year ", "Energy": "Energy"}