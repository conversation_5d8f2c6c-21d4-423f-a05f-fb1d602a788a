import { Root, Indicator } from "@radix-ui/react-progress"
import * as React from "react"

// eslint-disable-next-line import/no-unresolved
import { cn } from "@/lib/utils"

const Progress = React.forwardRef(({ className, value, ...props }, ref) => (
	<Root
		ref={ref}
		className={cn("relative h-4 w-full overflow-hidden rounded-full bg-secondary", className)}
		{...props}
	>
		<Indicator
			className="h-full w-full flex-1 bg-blue_d transition-all"
			style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
		/>
	</Root>
))

Progress.displayName = "Progress"

export { Progress }
