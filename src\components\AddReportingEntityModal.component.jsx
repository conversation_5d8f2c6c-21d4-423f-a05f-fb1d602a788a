/* eslint-disable import/no-unresolved */
import { Dialog, Transition } from "@headlessui/react"
import { Fragment } from "react"
import { useTranslation } from "react-i18next"
import { useDispatch } from "react-redux"
import { useNavigate } from "react-router-dom"

import { closeReportingEntityModal } from "@/store/actions/UserAction"

export default function AddReportingEntityModal() {
	//const [open, setOpen] = useState(true)

	const [t] = useTranslation("common")

	//const open = useSelector((state) => state.reportingEntity)

	const dispatch = useDispatch()

	const navigate = useNavigate()

	return (
		<div className="z-50">
			<Transition.Root show={true} as={Fragment}>
				<Dialog
					as="div"
					className="relative z-50"
					onClose={() => dispatch(closeReportingEntityModal())}
				>
					<Transition.Child
						as={Fragment}
						enter="ease-out duration-300"
						enterFrom="opacity-0"
						enterTo="opacity-100"
						leave="ease-in duration-200"
						leaveFrom="opacity-100"
						leaveTo="opacity-0"
					>
						<div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
					</Transition.Child>

					<div className="fixed inset-0 z-50 overflow-y-auto">
						<div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
							<Transition.Child
								as={Fragment}
								enter="ease-out duration-300"
								enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
								enterTo="opacity-100 translate-y-0 sm:scale-100"
								leave="ease-in duration-200"
								leaveFrom="opacity-100 translate-y-0 sm:scale-100"
								leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
							>
								<Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
									<div className="flex align-middle ">
										<svg
											xmlns="http://www.w3.org/2000/svg"
											fill="none"
											viewBox="0 0 24 24"
											strokeWidth={1.5}
											stroke="#F8BC56"
											className="w-6 h-6"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												d="M12 9v3.75m0-10.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.75c0 5.592 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.57-.598-3.75h-.152c-3.196 0-6.1-1.25-8.25-3.286Zm0 13.036h.008v.008H12v-.008Z"
											/>
										</svg>
										<Dialog.Title
											as="h2"
											className="text-lg font-semibold leading-6 ml-3 text-yellow-500"
										>
											{t("Add_Reporting_Entity")}
										</Dialog.Title>
									</div>

									<p className="text-sm text-slate-700 mt-2">{t("create_reporting_entity")}</p>
									<div className="w-full mt-3 flex justify-center">
										<button
											type="button"
											className="mt-3 inline-flex w-full justify-center rounded-md bg-sky-600 px-5 py-2 text-sm font-semibold text-white shadow-sm border border-inset hover:bg-sky-600 sm:mt-0 sm:w-auto"
											onClick={() => {
												navigate("/welcome")
											}}
										>
											OKAY
										</button>
									</div>
								</Dialog.Panel>
							</Transition.Child>
						</div>
					</div>
				</Dialog>
			</Transition.Root>
		</div>
	)
}
