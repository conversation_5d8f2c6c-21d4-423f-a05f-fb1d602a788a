import React from "react"

export default function FormInput({
	title,
	value,
	handleChange,
	type,
	required = false,
	placeholder = "",
	name = "",
	error = false,
}) {
	return (
		<>
			<label className="font-bold text-sm tracking-wide block text-slate-700">{title}</label>
			<input
				className={`block w-full px-3 py-2 bg-white ${
					error ? "border border-red-500 !text-red-600" : "border border-slate-300"
				} rounded-md text-sm shadow-sm placeholder-slate-400
                focus:outline-none  focus:!border-sky-600 disabled:!bg-slate-50 disabled:!text-slate-500 disabled:!border-slate-200 
                disabled:shadow-none invalid:!border-red-500 invalid:!text-red-600 focus:invalid:!border-red-500 focus:invalid:!ring-red-500 `}
				type={type}
				name={name}
				placeholder={placeholder}
				value={value}
				onChange={e => handleChange(e)}
				required={required}
			/>
		</>
	)
}
