/* eslint-disable import/no-unresolved */
import React, { useState } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"

import Button from "@/components/ui/Button" // Assuming alias setup
import Input from "@/components/ui/Input" // Assuming alias setup
import { useToast } from "@/hooks"

function MaterialRowContent(props) {
	// Props expected:
	// - category1MaterialRows: Array of existing material objects [{ metric, factor, numberOfUnits, weightPerUnit, totalWeight, scope1, scope2, scope3 }, ...]
	// - setCategory1MaterialRows: Function to update the array in the parent state
	// - updateCategory1MaterialEmission: Optional function to notify parent of changes
	const { category1MaterialRows = [], updateCategory1MaterialEmission } = props
	const { t } = useTranslation("transactions")
	const toast = useToast()

	// Local state for the "Add New" form inputs
	const [metric, setMetric] = useState("") // Corresponds to 'Description'
	const [factor, setFactor] = useState(0.0) // kg Co2e / kg
	const [numberOfUnits, setNumberOfUnits] = useState(0.0) // Units (e.g., Pcs)
	const [weightPerUnit, setWeightPerUnit] = useState(0.0) // kg

	// Adds the new row data to the main list
	const addCategory1MaterialRow = (calculatedScope3, calculatedTotalWeight) => {
		const newCategory1MaterialRow = {
			metric: metric,
			factor: Number(factor),
			numberOfUnits: Number(numberOfUnits),
			weightPerUnit: Number(weightPerUnit),
			totalWeight: Number(calculatedTotalWeight), // Calculated
			scope3: Number(calculatedScope3), // Calculated emission
		}

		const newCategory1MaterialRowsList = [...category1MaterialRows, newCategory1MaterialRow]

		updateCategory1MaterialEmission(
			newCategory1MaterialRow,
			false,
			"scope3Category1MaterialRows",
			newCategory1MaterialRowsList,
			1
		) // false indicates addition

		// Reset form fields
		setMetric("")
		setFactor(0.0)
		setNumberOfUnits(0.0)
		setWeightPerUnit(0.0)
	}

	// Calculates emission and triggers adding the row
	const handleAddEmission = () => {
		// Basic validation
		if (metric.trim() === "") {
			toast("error", t("discription_error"))
			return
		}

		if (
			parseFloat(factor) <= 0 &&
			parseFloat(numberOfUnits) <= 0 &&
			parseFloat(weightPerUnit) <= 0
		) {
			toast("error", t("factor_unit_weight_greater_0_error"))
			return
		}
		const numFactor = parseFloat(factor)
		const numUnits = parseFloat(numberOfUnits)
		const numWeight = parseFloat(weightPerUnit)

		// Calculation based on original component structure:
		// Total Weight = Number of Units * Weight Per Unit
		// Total Emission (Scope 3) = Factor * Total Weight
		let calculatedTotalWeight = numUnits * numWeight
		let calculatedScope3 = numFactor * calculatedTotalWeight
		addCategory1MaterialRow(calculatedScope3, calculatedTotalWeight)
		return { success: true }
	}

	// Deletes a row from the list by index
	const deleteCategory1MaterialRow = index => {
		const rowToDelete = category1MaterialRows[index]
		const newCategory1MaterialRowsList = [...category1MaterialRows]
		newCategory1MaterialRowsList.splice(index, 1)
		updateCategory1MaterialEmission(
			rowToDelete,
			true,
			"scope3Category1MaterialRows",
			newCategory1MaterialRowsList,
			1
		) // true indicates deletion
	}

	const handleFocus = event => event.target.select()

	return (
		<>
			{/* Display existing/saved rows */}
			{category1MaterialRows.map((category1Material, index) => (
				<div key={index}>
					{/* Using the display structure from the original component's 'isSaved' block */}
					<div className="grid grid-cols-6 gap-8 my-1 saved-emission">
						{" "}
						{/* 6 columns for saved view */}
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("Metric")}
							</label>
							<div>{category1Material.metric}</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("Factor")}
							</label>
							<div className="flex relative">
								<span>{category1Material.factor}</span>
								<span className="custom-span-unit-value-save">{t("kg Co2e/kg")}</span>
							</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("Weight Per Unit")}
							</label>
							<div className="flex relative">
								<span>{category1Material.weightPerUnit}</span>
								<span className="custom-span-unit-value-save">{t("Kg")}</span>
							</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("Total Weight")}
							</label>
							<div className="flex relative">
								{/* Display calculated total weight */}
								<span>{Number(category1Material.totalWeight || 0).toFixed(2)}</span>
								<span className="custom-span-unit-value-save">{t("Kg")}</span>
							</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("TotalCo2e")}
							</label>
							<div className="flex relative">
								<span>
									{Number(
										(category1Material.scope1 || 0) +
											(category1Material.scope2 || 0) +
											(category1Material.scope3 || 0)
									).toFixed(2)}
								</span>
								<span className="text-nowrap custom-span-unit-value-save">{t("kg")}</span>
							</div>
						</div>
						<div className="!items-center delete-icon">
							<span
								aria-hidden
								onClick={() => deleteCategory1MaterialRow(index)}
								style={{ cursor: "pointer" }}
							>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}

			{/* Form for adding new rows */}
			{/* 5 columns needed: Description, Factor, Units, Weight/Unit, Add Button */}
			<div className="grid grid-cols-5 gap-10 my-2">
				<div>
					<Input
						label={t("Description")}
						type="text"
						labelColor="text-sky-500"
						value={metric}
						handleChange={e => setMetric(e.target.value)}
						handleFocus={handleFocus}
						placeholder={t("Enter description")}
					/>
				</div>
				<div>
					<Input
						label={t("Factor")}
						labelColor="text-sky-500"
						type="number"
						value={factor}
						handleChange={e => setFactor(e.target.value)}
						handleFocus={handleFocus}
						unit={t("kg Co2e/kg")}
						min="0"
						step="any"
					/>
				</div>
				<div>
					<Input
						label={t("Units")} // Corresponds to numberOfUnits
						labelColor="text-sky-500"
						type="number"
						value={numberOfUnits}
						handleChange={e => setNumberOfUnits(e.target.value)}
						handleFocus={handleFocus}
						// No specific unit displayed in original input, add if needed e.g., unit={t("Pcs")}
						min="0"
						step="any"
					/>
				</div>
				<div>
					<Input
						label={t("Weight Per Unit")}
						labelColor="text-sky-500"
						type="number"
						value={weightPerUnit}
						handleChange={e => setWeightPerUnit(e.target.value)}
						handleFocus={handleFocus}
						unit={t("Kg")}
						min="0"
						step="any"
					/>
				</div>
				<div className="self-end mb-1">
					<Button title={`${t("Add")} | +`} handleClick={handleAddEmission} color="sky-500" />
				</div>
			</div>
		</>
	)
}

export default MaterialRowContent
