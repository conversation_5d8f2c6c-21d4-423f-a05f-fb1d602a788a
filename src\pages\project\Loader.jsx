import React, { forwardRef } from "react"

import { Skeleton } from "../../components/ui/skeleton" // Adjust the path as necessary

const Loader = forwardRef((_, ref) => (
	<div ref={ref} className="h-screen py-8 shadow-lg">
		<div className="flex flex-row items-center h-full w-full">
			<div className="flex flex-col items-center h-fit w-1/3 gap-20">
				<Skeleton className="h-[180px] w-[180px] rounded-full" />
				<Skeleton className="h-[180px] w-[180px] rounded-full" />
			</div>
			<div className="flex flex-col items-center h-fit w-2/3 gap-5">
				<Skeleton className="h-[150px] w-11/12" />
				<Skeleton className="h-[150px] w-11/12" />
				<Skeleton className="h-[150px] w-11/12" />
			</div>
		</div>
	</div>
))

export default Loader
