import { extend } from "@syncfusion/ej2-base"
import React from "react"

export class TransactionsChildGridDialogForm extends React.Component {
	// this is called while editing/adding data in grid.
	constructor(props) {
		super(props)
		let data = JSON.parse(JSON.stringify(props))
		this.state = extend({}, {}, data, true)
		if (props.isAdd) {
			this.state = {
				...data,
				lineNumber: "",
				RecordID: "",
				AccountID: "",
				AccountDescription: "",
				DebitAmount: { Amount: "" },
				CreditAmount: { Amount: "" },
				Description: "",
				SupplierID: "",
				ReferenceNumber: "",
			}
		}
	}
	onChange(args) {
		let key = args.target.name
		let value = args.target.value
		this.setState({ [key]: value })
	}

	componentDidMount() {
		this.Description.focus()
	}

	render() {
		const { t } = this.props
		return (
			<div>
				<div className="form-row">
					<div className="form-group col-md-6">
						<div className="e-float-input e-control-wrapper">
							<input
								id="RecordID"
								name="RecordID"
								type="text"
								disabled={true}
								value={this.state.RecordID}
							/>
							<span className="e-float-line"></span>
							<label className="e-float-text e-label-top">{t("RecordID")}</label>
						</div>
					</div>
				</div>

				<div className="form-row">
					<div className="form-group col-md-6">
						<div className="e-float-input e-control-wrapper">
							<input
								id="AccountID"
								name="AccountID"
								type="text"
								disabled={true}
								value={this.state.AccountID}
							/>
							<span className="e-float-line"></span>
							<label className="e-float-text e-label-top">{t("AccountID")}</label>
						</div>
					</div>
					<div className="form-group col-md-6">
						<div className="e-float-input e-control-wrapper">
							<input
								id="ReferenceNumber"
								name="ReferenceNumber"
								type="text"
								disabled={true}
								value={this.state.ReferenceNumber}
							/>
							<span className="e-float-line"></span>
							<label className="e-float-text e-label-top">{t("ReferenceNumber")}</label>
						</div>
					</div>
				</div>

				<div className="form-row">
					<div className="form-group col-md-6">
						<div className="e-float-input e-control-wrapper">
							<input
								id="DebitAmount"
								name="DebitAmount.Amount"
								type="number"
								disabled={true}
								value={this.state.DebitAmount.Amount}
							/>
							<span className="e-float-line"></span>
							<label className="e-float-text e-label-top">{t("DebitAmount")}</label>
						</div>
					</div>

					<div className="form-group col-md-6">
						<div className="e-float-input e-control-wrapper">
							<input
								id="CreditAmount"
								name="CreditAmount.Amount"
								type="number"
								disabled={true}
								value={this.state.CreditAmount.Amount}
							/>
							<span className="e-float-line"></span>
							<label className="e-float-text e-label-top">{t("CreditAmount")}</label>
						</div>
					</div>
				</div>

				<div className="form-row">
					<div className="form-group col-md-6">
						<div className="e-float-input e-control-wrapper">
							<input
								ref={input => (this.Description = input)}
								id="Description"
								name="Description"
								type="text"
								value={this.state.Description}
								onChange={this.onChange.bind(this)}
							/>
							<span className="e-float-line"></span>
							<label className="e-float-text e-label-top">{t("Description")}</label>
						</div>
					</div>
					<div className="form-group col-md-6">
						<div className="e-float-input e-control-wrapper">
							<input
								id="SupplierID"
								name="SupplierID"
								type="text"
								disabled={true}
								value={this.state.SupplierID}
							/>
							<span className="e-float-line"></span>
							<label className="e-float-text e-label-top">{t("SupplierID")}</label>
						</div>
					</div>
				</div>
			</div>
		)
	}
}
