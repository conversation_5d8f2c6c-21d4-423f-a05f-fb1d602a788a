exports = async (input) => {
    try {
        const user = await context.services
            .get("mongodb-atlas")
            .db(context.environment.values.database)
            .collection("user")
            .findOne({ netlifyID: input.netlifyID });
        if (!user) {
            return { status: "error" };
        }

        const request = context.services
            .get("mongodb-atlas")
            .db(context.environment.values.database)
            .collection("user");
            
            const result = await request.updateOne({netlifyID: input.netlifyID},
            {
              $set: { "organizationId" : input.organizationId } 
            })

        
        return { status: "done", organizationId: input.organizationId };
    } catch (error) {
        console.log(error);
        return { status: "error" };
    }
    return {};
};
