{"_id": {"$oid": "63568c897cad6571f5dbc753"}, "RegistrationNumber": "*********", "SupplierID": "20357", "SupplierName": "", "AccountID": "65530", "AccountDescription": "", "Amount": 6000, "Scope_1": {"$numberLong": "0"}, "Scope_2": {"$numberLong": "0"}, "Scope_3": 0.13386, "TransactionID": "ExF-108104#2021-01-01#1000-062746", "Period": "1", "PeriodYear": "2021", "TransactionDate": "2021-01-01", "Description": ["Vendor invoice", "Vendor invoice", "Vendor invoice", "Vendor invoice", "Vendor invoice", "Vendor invoice", "Vendor invoice"], "ImportID": "1a12a4ce-6ba7-424c-8920-9f025095f289", "Status": {"$numberLong": "2"}, "ReferenceNumber": "16626", "SystemID": "", "Lines": [{"RecordID": "**********", "AccountID": "24210", "SupplierID": "", "ReferenceNumber": "16626", "Description": "Vendor invoice", "CreditAmount": {"Amount": "6000.00", "CurrencyCode": "NOK", "CurrencyAmount": "6000.00"}, "DebitAmount": {"Amount": ""}}, {"RecordID": "**********", "AccountID": "24000", "SupplierID": "20357", "ReferenceNumber": "16626", "Description": "Vendor invoice", "CreditAmount": {"Amount": "7500.00", "CurrencyCode": "NOK", "CurrencyAmount": "7500.00"}, "DebitAmount": {"Amount": ""}}, {"RecordID": "**********", "AccountID": "24200", "SupplierID": "20357", "ReferenceNumber": "16626", "Description": "Vendor invoice", "CreditAmount": {"Amount": ""}, "DebitAmount": {"Amount": "7500.00", "CurrencyCode": "NOK", "CurrencyAmount": "7500.00"}}, {"RecordID": "**********", "AccountID": "27100", "SupplierID": "", "ReferenceNumber": "16626", "Description": "Vendor invoice", "CreditAmount": {"Amount": ""}, "DebitAmount": {"Amount": "1500.00", "CurrencyCode": "NOK", "CurrencyAmount": "1500.00"}}, {"RecordID": "**********", "AccountID": "24211", "SupplierID": "", "ReferenceNumber": "16626", "Description": "Vendor invoice", "CreditAmount": {"Amount": "1500.00", "CurrencyCode": "NOK", "CurrencyAmount": "1500.00"}, "DebitAmount": {"Amount": ""}}, {"RecordID": "**********", "AccountID": "65530", "SupplierID": "", "ReferenceNumber": "16626", "Description": "Vendor invoice", "CreditAmount": {"Amount": "", "CurrencyAmount": "", "CurrencyCode": "", "ExchangeRate": ""}, "DebitAmount": {"Amount": "6000.00", "CurrencyCode": "NOK", "CurrencyAmount": "6000.00", "ExchangeRate": ""}, "NaceCode": "", "Status": ""}], "AccountIDInRange": true, "DescriptionDetails": "", "NaceCode": "", "Notes": "", "Relation": "non-production", "Scope": 3, "Scope_3_Category": 1, "Type": "final", "Updated": true, "kwh": {"$numberLong": "0"}, "liter": {"$numberLong": "0"}, "mobileCombustion": {"$numberLong": "0"}, "non_renewable_energy": {"$numberLong": "0"}, "renewable_energy": {"$numberLong": "0"}, "stationaryCombustion": {"$numberLong": "0"}}