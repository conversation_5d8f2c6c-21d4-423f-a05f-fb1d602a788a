{"fullDocument": {"_id": {"$oid": "6305fa3998d3b92abdfe4faf"}, "RegistrationNumber": "*********", "Name": "Test Selskapet AS", "AuthId": "user_2DnXuBZR301LqHdvTGdYKso38E3", "ContactPerson": {"FirstName": "<PERSON><PERSON><PERSON>", "LastName": "Lie"}, "Address": [{"StreetName": "Tøyenstredet 22", "City": "Oslo", "PostalCode": "0235", "Country": "NO"}], "Accounts": [{"AccountID": "1250", "AccountDescription": "Inventar", "StandardAccountID": "12", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "132500", "ClosingDebitBalance": "145500"}, {"AccountID": "1420", "AccountDescription": "Varer under <PERSON><PERSON><PERSON><PERSON>", "StandardAccountID": "14", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "957000", "ClosingDebitBalance": "957000"}, {"AccountID": "1440", "AccountDescription": "<PERSON><PERSON>ge egentilvirkede varer", "StandardAccountID": "14", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "1578330", "ClosingDebitBalance": "1578330"}, {"AccountID": "1460", "AccountDescription": "Innkj<PERSON><PERSON><PERSON> varer for videresalg", "StandardAccountID": "14", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "30580", "ClosingDebitBalance": "30580"}, {"AccountID": "1500", "AccountDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "StandardAccountID": "15", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "15000", "ClosingDebitBalance": "103700"}, {"AccountID": "1900", "AccountDescription": "<PERSON><PERSON><PERSON>", "StandardAccountID": "19", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "12000", "ClosingDebitBalance": "11367.50"}, {"AccountID": "1920", "AccountDescription": "Bankinnskudd", "StandardAccountID": "19", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "370000", "ClosingDebitBalance": "670568.75"}, {"AccountID": "2000", "AccountDescription": "Egenkapital", "StandardAccountID": "20", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningCreditBalance": "225000", "ClosingCreditBalance": "225000"}, {"AccountID": "2400", "AccountDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "StandardAccountID": "24", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningCreditBalance": "175000", "ClosingCreditBalance": "212025.00"}, {"AccountID": "2700", "AccountDescription": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> me<PERSON>gift, høy sats", "StandardAccountID": "27", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningCreditBalance": "300000", "ClosingCreditBalance": "326375"}, {"AccountID": "2710", "AccountDescription": "Inngå<PERSON><PERSON> me<PERSON>avgift, høy sats", "StandardAccountID": "27", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "150000", "ClosingDebitBalance": "72762.50"}, {"AccountID": "2711", "AccountDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> merve<PERSON>gift, middels sats", "StandardAccountID": "27", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "0", "ClosingDebitBalance": "0"}, {"AccountID": "2740", "AccountDescription": "Oppgjørskonto merverdiavgift", "StandardAccountID": "27", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningCreditBalance": "0", "ClosingCreditBalance": "0"}, {"AccountID": "3000", "AccountDescription": "Salgsinntekt handelsvarer, avgiftspliktig, høy sats", "StandardAccountID": "30", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "0", "ClosingCreditBalance": "2316338"}, {"AccountID": "4000", "AccountDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "StandardAccountID": "40", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "0", "ClosingDebitBalance": "186802.00"}, {"AccountID": "4112", "AccountDescription": "Motkonto 4111 - innfø<PERSON>l 25%", "StandardAccountID": "7780", "AccountType": "GL", "OpeningDebitBalance": "0.00", "ClosingDebitBalance": "0"}, {"AccountID": "4090", "AccountDescription": "Beholdningsendring", "StandardAccountID": "4090", "AccountType": "GL", "OpeningCreditBalance": "0.00", "ClosingCreditBalance": "0"}, {"AccountID": "4022", "AccountDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> and<PERSON>, middels mva", "StandardAccountID": "4030", "AccountType": "GL", "OpeningDebitBalance": "0.00", "ClosingDebitBalance": "0"}, {"AccountID": "5000", "AccountDescription": "<PERSON><PERSON><PERSON> til an<PERSON>t", "StandardAccountID": "50", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "0", "ClosingDebitBalance": "1496000"}, {"AccountID": "5092", "AccountDescription": "<PERSON><PERSON><PERSON><PERSON>", "StandardAccountID": "50", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "0", "ClosingDebitBalance": "0"}, {"AccountID": "6200", "AccountDescription": "Strøm til produksjon", "StandardAccountID": "62", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "0", "ClosingDebitBalance": "40000"}, {"AccountID": "6290", "AccountDescription": "<PERSON><PERSON> brensel til produksjon", "StandardAccountID": "62", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "0", "ClosingDebitBalance": "0"}, {"AccountID": "6300", "AccountDescription": "<PERSON><PERSON> lo<PERSON>e", "StandardAccountID": "63", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "0", "ClosingDebitBalance": "150000"}, {"AccountID": "6320", "AccountDescription": "Renovasjon, vann, avløp o.l.", "StandardAccountID": "63", "AccountType": "GL", "AccountCreationDate": "2018-06-08", "OpeningDebitBalance": "0", "ClosingDebitBalance": "0"}, {"AccountID": "6321", "AccountDescription": "Renovasjon og avfall", "StandardAccountID": "63", "AccountType": "GL", "AccountCreationDate": "2018-06-08", "OpeningDebitBalance": "0", "ClosingDebitBalance": "0"}, {"AccountID": "6322", "AccountDescription": "Vann og avløp", "StandardAccountID": "63", "AccountType": "GL", "AccountCreationDate": "2018-06-08", "OpeningDebitBalance": "0", "ClosingDebitBalance": "0"}, {"AccountID": "6360", "AccountDescription": "Renhold", "StandardAccountID": "63", "AccountType": "GL", "AccountCreationDate": "2018-06-08", "OpeningDebitBalance": "0", "ClosingDebitBalance": "0"}, {"AccountID": "6390", "AccountDescription": "<PERSON>n k<PERSON>d lo<PERSON>er", "StandardAccountID": "63", "AccountType": "GL", "AccountCreationDate": "2018-06-08", "OpeningDebitBalance": "0", "ClosingDebitBalance": "0"}, {"AccountID": "6400", "AccountDescription": "<PERSON><PERSON> maskiner", "StandardAccountID": "64", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "0", "ClosingDebitBalance": "66000"}, {"AccountID": "7000", "AccountDescription": "<PERSON><PERSON><PERSON><PERSON>", "StandardAccountID": "7000", "AccountType": "GL", "OpeningDebitBalance": "0.00", "ClosingDebitBalance": "0.00"}, {"AccountID": "7100", "AccountDescription": "Bilgodtgjørelse, oppgavepliktig", "StandardAccountID": "71", "AccountType": "GL", "AccountCreationDate": "2020-01-23", "OpeningDebitBalance": "0", "ClosingDebitBalance": "0"}, {"AccountID": "7130", "AccountDescription": "Reisekostnad, oppgavepliktig", "StandardAccountID": "71", "AccountType": "GL", "AccountCreationDate": "2020-01-23", "OpeningDebitBalance": "0", "ClosingDebitBalance": "0"}, {"AccountID": "7140", "AccountDescription": "Reisekostnad, ikke oppgavepliktig", "StandardAccountID": "71", "AccountType": "GL", "AccountCreationDate": "2020-01-23", "OpeningDebitBalance": "0", "ClosingDebitBalance": "0"}, {"AccountID": "7145", "AccountDescription": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> pendling", "StandardAccountID": "71", "AccountType": "GL", "AccountCreationDate": "2020-01-23", "OpeningDebitBalance": "0", "ClosingDebitBalance": "0"}, {"AccountID": "7195", "AccountDescription": "Arbeidstøygodtgjørelse", "StandardAccountID": "71", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "0", "ClosingDebitBalance": "699"}, {"AccountID": "7320", "AccountDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "StandardAccountID": "73", "AccountType": "GL", "AccountCreationDate": "2015-01-01", "OpeningDebitBalance": "0", "ClosingDebitBalance": "62000"}], "Suppliers": [{"RegistrationNumber": "*********", "Name": "LodeStar AS", "SupplierID": "2000", "ClosingCreditBalance": "0", "Contact": {"ContactPerson": {"FirstName": "<PERSON><PERSON><PERSON>", "Initials": "EM", "LastName": "<PERSON><PERSON><PERSON>", "Salutation": "Hr.", "OtherTitles": "<PERSON><PERSON><PERSON> leder"}, "Telephone": "*********", "Fax": "********", "Email": "<EMAIL>", "Website": "http://www.dlm.offline/"}, "NaceCode": "68.310", "Address": [{"StreetName": "<PERSON> Gate", "Number": "32", "City": "Oslo", "PostalCode": "0451", "Country": "NO", "AddressType": "StreetAddress"}, {"StreetName": "<PERSON> Alls Gate 32", "City": "Oslo", "PostalCode": "0451", "Country": "NO", "AddressType": "PostalAddress"}], "Industry": "Eiendomsmegling", "NaicsCode": "531210"}, {"RegistrationNumber": "984979193MVA", "Name": "<PERSON><PERSON>", "SupplierID": "10364", "ClosingCreditBalance": "26825", "Contact": {"ContactPerson": {"FirstName": "<PERSON><PERSON><PERSON>", "Initials": "BLL", "LastName": "<PERSON><PERSON><PERSON><PERSON>", "Salutation": "Hr.", "OtherTitles": "<PERSON><PERSON>"}, "Telephone": "95181366", "Fax": "********", "Email": "<PERSON><PERSON><PERSON>@bøffel.com", "Website": "http://www.maskina.com/"}, "NaceCode": "77.390", "Address": [{"StreetName": "Moseveien", "Number": "29", "City": "Molven", "PostalCode": "4510", "Country": "NO", "AddressType": "StreetAddress"}, {"StreetName": "Moseveien 29", "City": "<PERSON><PERSON><PERSON>", "PostalCode": "4510", "Country": "NO", "AddressType": "PostalAddress"}], "Industry": "Utl./leas. andre mask./annet utstyr", "NaicsCode": "532120"}, {"RegistrationNumber": "988776652", "Name": "<PERSON><PERSON><PERSON><PERSON>", "SupplierID": "2001", "ClosingCreditBalance": "26825", "Contact": {"ContactPerson": {"FirstName": "<PERSON><PERSON><PERSON>", "Initials": "BLL", "LastName": "<PERSON>", "Salutation": "Hr.", "OtherTitles": "<PERSON><PERSON>"}, "Telephone": "95181366", "Fax": "********", "Email": "<PERSON><PERSON><PERSON>@maskina.com", "Website": "http://www.maskina.com/"}, "NaceCode": "77.390", "Address": [{"StreetName": "Moseveien", "Number": "29", "City": "Molven", "PostalCode": "4510", "Country": "NO", "AddressType": "StreetAddress"}, {"StreetName": "Moseveien 29", "City": "<PERSON><PERSON><PERSON>", "PostalCode": "4510", "Country": "NO", "AddressType": "PostalAddress"}], "Industry": "Utl./leas. andre mask./annet utstyr", "NaicsCode": "532120"}, {"RegistrationNumber": "987654651", "Name": "<PERSON><PERSON>", "SupplierID": "2002", "ClosingCreditBalance": "0", "Contact": {"ContactPerson": {"FirstName": "<PERSON><PERSON>", "Initials": "NS", "LastName": "<PERSON><PERSON>", "Salutation": "Hr.", "OtherTitles": "<PERSON><PERSON><PERSON>"}, "Telephone": "99100200", "Fax": "********", "Email": "<EMAIL>", "Website": "http://www.myketekstiler.offline/"}, "NaceCode": "63.110", "Address": [{"StreetName": "Grønland Torg", "Number": "5", "City": "Oslo", "PostalCode": "0236", "Country": "NO", "AddressType": "StreetAddress"}, {"StreetName": "Grønnland Torg", "City": "Oslo", "PostalCode": "0236", "Country": "NO", "AddressType": "PostalAddress"}], "Industry": "Databeh./-lagring og tilkn. tjen.", "NaicsCode": "518210"}, {"RegistrationNumber": "987654652MVA", "Name": "Fuel Supplier", "SupplierID": "2007", "ClosingCreditBalance": "0", "Contact": {"ContactPerson": {"FirstName": "Per", "Initials": "PM", "LastName": "<PERSON>", "Salutation": "Hr.", "OtherTitles": "<PERSON><PERSON><PERSON>"}, "Telephone": "99100200", "Fax": "********", "Email": "<EMAIL>", "Website": "http://www.fuel.offline/"}, "NaceCode": "47.300", "Address": [{"StreetName": "Ekofisk1", "Number": "5", "City": "Oslo", "PostalCode": "0236", "Country": "NO", "AddressType": "StreetAddress"}, {"StreetName": "Ekofisk1", "City": "Oslo", "PostalCode": "0236", "Country": "NO", "AddressType": "PostalAddress"}], "Industry": "Detaljh. drivstoff til motorvogner", "NaicsCode": "447110"}, {"RegistrationNumber": "984561321", "Name": "Overpriset Strøm AS", "SupplierID": "2003", "ClosingCreditBalance": "24000", "Contact": {"ContactPerson": {"FirstName": "<PERSON>", "Initials": "KKB", "LastName": "<PERSON><PERSON><PERSON>", "Salutation": "Hr.", "OtherTitles": "Kundekontakt"}, "Telephone": "81562333", "Fax": "81562332", "Email": "<EMAIL>", "Website": "http://www.os.offline/"}, "NaceCode": "35.140", "Address": [{"StreetName": "<PERSON><PERSON>", "Number": "77", "City": "Bergen", "PostalCode": "5022", "Country": "NO", "AddressType": "StreetAddress"}, {"StreetName": "PB 1003", "City": "Bergen", "PostalCode": "5020", "Country": "NO", "AddressType": "PostalAddress"}], "Industry": "Handel med elektrisitet", "NaicsCode": "221122"}, {"RegistrationNumber": "912391231", "Name": "Råvareleverandøren AS", "SupplierID": "2004", "ClosingCreditBalance": "11499.50", "Contact": {"ContactPerson": {"FirstName": "<PERSON>", "Initials": "ON", "LastName": "<PERSON><PERSON>", "Salutation": "Hr.", "OtherTitles": "<PERSON><PERSON><PERSON> leder"}, "Telephone": "92046703", "Fax": "92046707", "Email": "<EMAIL>", "Website": "--/"}, "NaceCode": "73.110", "Address": [{"StreetName": "<PERSON><PERSON><PERSON>", "Number": "1", "City": "Trondheim", "PostalCode": "7000", "Country": "NO", "AddressType": "StreetAddress"}, {"StreetName": "Postboks 43", "City": "Trondheim", "PostalCode": "7001", "Country": "NO", "AddressType": "PostalAddress"}], "Industry": "Rekla<PERSON>byr<PERSON><PERSON>", "NaicsCode": "541810"}, {"RegistrationNumber": "912391444", "Name": "Aleksanders Mediehus", "SupplierID": "2005", "ClosingCreditBalance": "", "Contact": {"ContactPerson": {"FirstName": "<PERSON>", "Initials": "ADUKS", "LastName": "<PERSON><PERSON>", "Salutation": "Hr.", "OtherTitles": "<PERSON><PERSON>"}, "Telephone": "********", "Fax": "********", "Email": "<EMAIL>", "Website": "http://www.aleksanders-mediehus.offline/"}, "NaceCode": "64.190", "Address": [{"StreetName": "K<PERSON><PERSON>rnerst<PERSON>", "Number": "45", "City": "Bærum", "PostalCode": "1295", "Country": "NO", "AddressType": "StreetAddress"}, {"StreetName": "Kværnerstua 45", "City": "Bærum", "PostalCode": "1295", "Country": "NO", "AddressType": "PostalAddress"}], "Industry": "Bankvirksomhet ellers", "NaicsCode": "522110"}]}}