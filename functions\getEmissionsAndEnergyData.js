const category_description = {
  1: "Purchased Goods and Services",
  2: "Capital Goods",
  3: "Fuel and Energy Related Activities",
  4: "Upstream Transportation and Distribution",
  5: "Waste Generated in Operations",
  6: "Business Travel",
  7: "Employee Commuting",
  8: "Upstream leased assets",
};

const getPercentage = (emission, totalEmission) => {
  if (emission && emission > 0 && totalEmission > 0) {
    return Number((emission / totalEmission) * 100).toFixed(2);
  } else {
    return 0;
  }
};

exports = async function (input) {
  try {
    const request = context.services
      .get("mongodb-atlas")
      .db(context.environment.values.database)
      .collection("transaction");
    let query = {
      RegistrationNumber: input.RegistrationNumber,
      PeriodYear: input.year,
    };
    if (input.AnalysisIDs) {
      query["Analysis"] = {
        $elemMatch: {
          AnalysisID: {
            $in: [...input.AnalysisIDs],
          },
        },
      };
    }

    const commonMatch = {
      $match: query,
    };

    const res = await request
      .aggregate([
        commonMatch,
        {
          $facet: {
            capitalGood: [
              { $match: { Relation: "Production", Type: "Capital good" } },
              { $group: { _id: undefined, Emission: { $sum: "$Scope_3" } } },
            ],
            intermediate: [
              { $match: { Relation: "Production", Type: "intermediate" } },
              { $group: { _id: undefined, Emission: { $sum: "$Scope_3" } } },
            ],
            final: [
              { $match: { Relation: "Production", Type: "final" } },
              { $group: { _id: undefined, Emission: { $sum: "$Scope_3" } } },
            ],
            categoriesData: [
              {
                $group: {
                  _id: "$Scope_3_Category",
                  Emission: { $sum: "$Scope_3" },
                },
              },
            ],
            primaryData: [
              { $match: { Status: 1, Scope_3_Category: { $lte: 8 } } },
              {
                $group: {
                  _id: "$Scope_3_Category",
                  Scope_1: { $sum: "$Scope_1" },
                  Scope_2: { $sum: "$Scope_2" },
                  Scope_3: { $sum: "$Scope_3" },
                },
              },
            ],
            totalEnergy: [
              {
                $group: {
                  _id: undefined,
                  totalNonRenewable: { $sum: "$non_renewable_energy" },
                  totalRenewable: { $sum: "$renewable_energy" },
                  totalStationary: { $sum: "$stationaryCombustion" },
                  totalMobile: { $sum: "$mobileCombustion" },
                  totalNuclear: { $sum: "$nuclear" },
                },
              },
            ],
            scopeData: [
              {
                $group: {
                  _id: undefined,
                  Scope_1: { $sum: "$Scope_1" },
                  Scope_2: { $sum: "$Scope_2" },
                  Scope_3: { $sum: "$Scope_3" },
                  marketBased: { $sum: "$marketBased" },
                  locationBased: { $sum: "$locationBased" },
                  consumptionBased: { $sum: "$consumptionBased" },
                },
              },
            ],
          },
        },
      ])
      .toArray();

    // here primaryData with scope data with scope 1
    // totalScope is scope data with all status eg ( 0,1,2,3)
    // categoriesData is all categories data

    let { primaryData, scopeData, categoriesData } = res[0];

    let downStreamEmission = 0;

    let totalEmission = categoriesData.reduce((sum, item) => {
      if (item._id && !isNaN(item._id) && item._id <= 8) {
        return sum + item.Emission;
      } else {
        return sum;
      }
    }, 0);

    let scope3CategoriesData = [];

    for (let i = 1; i <= 15; i++) {
      const item = categoriesData.find((category) => category._id === i);

      const primaryDataItem =
        primaryData.find((category) => category._id === i) || 0;

      if (i <= 8) {
        let percentage = 0;

        let primaryDataPercentage = 0;

        if (item || item != undefined) {
          // Calculate raw percentage
          percentage = getPercentage(item.Emission || 0, totalEmission);

          // Calculate percentage for primary data

          let primaryItemEmission =
            primaryDataItem?.Scope_1 ||
            0 + primaryDataItem?.Scope_2 ||
            0 + primaryDataItem?.Scope_3 ||
            0;

          primaryDataPercentage = getPercentage(
            primaryItemEmission,
            item.Emission,
          );

          scope3CategoriesData.push({
            Scope_3_Category: i,
            Description: category_description[i],
            Percentage: percentage, // Store the raw percentage for adjustment later
            Emission: item.Emission,
            primaryDataPercentage,
          });
        } else {
          scope3CategoriesData.push({
            Scope_3_Category: i,
            Description: category_description[i],
            Percentage: 0,
            Emission: 0,
            primaryDataPercentage: 0,
          });
        }
      } else {
        if (item) {
          downStreamEmission += item.Emission;
        }
      }
    }

    // need to subtract downstram emission from primaryData and total Scope
    // because downstream emission is also scope3 emission and we do sum
    // of scope 3 emissions it is also added in it

    // check if there is totalScope or not

    let {
      Scope_3,
      Scope_1,
      Scope_2,
      marketBased,
      locationBased,
      consumptionBased,
    } = scopeData[0];

    const scope3EmissionWithoutDownStream = Scope_3 || 0 - downStreamEmission;

    // check if there is primaryData or not ( primary data )
    //if (primaryData.length > 0)
    // this is check wheter scope 3 is presnt or not
    //primaryData[0].Scope_3 = primaryData[0].Scope_3 - downStreamEmission;

    const primaryEmissionObject =
      primaryData.length > 0
        ? primaryData.reduce(
            (acc, curr) => {
              acc.Scope_1 += curr.Scope_1;
              acc.Scope_2 += curr.Scope_2;
              acc.Scope_3 += curr.Scope_3;
              return acc;
            },
            { Scope_1: 0, Scope_2: 0, Scope_3: 0 },
          )
        : {};

    const scopeDataKeys = Object.keys(primaryEmissionObject);

    let totalPrimaryScope = 0;

    // this will return data with key value pairs key will be Scope_1, Scope_2
    // or Scope_3 value will be percentage

    const scopePercentageWithStatus1 = scopeDataKeys.reduce((acc, key) => {
      totalPrimaryScope += primaryEmissionObject[key];
      const percentage = primaryEmissionObject[key]
        ? ((primaryEmissionObject[key] / scopeData[0][key]) * 100).toFixed(2)
        : 0;
      return { ...acc, [key]: percentage };
    }, {});

		const data = {
			capitalGood: res[0].capitalGood.length == 0 ? 0.0 : res[0].capitalGood[0].Emission,
			intermediate: res[0].intermediate.length == 0 ? 0.0 : res[0].intermediate[0].Emission,
			final: res[0].final.length == 0 ? 0.0 : res[0].final[0].Emission,
			totalNonRenewable: res[0].totalEnergy.length == 0 ? 0.0 : res[0].totalEnergy[0].totalNonRenewable,
			totalRenewable: res[0].totalEnergy.length == 0 ? 0.0 : res[0].totalEnergy[0].totalRenewable,
			totalStationary: res[0].totalEnergy.length == 0 ? 0.0 : res[0].totalEnergy[0].totalStationary,
			totalMobile: res[0].totalEnergy.length == 0 ? 0.0 : res[0].totalEnergy[0].totalMobile,
			totalNuclear: res[0].totalEnergy.length == 0 ? 0.0 : res[0].totalEnergy[0].totalNuclear,
			scopePercentageWithStatus1,
			scope3CategoriesData,
			totalPrimaryScope,
			Scope_1: Scope_1 || 0,
			Scope_2: Scope_2 || 0,
			Scope_3: Scope_3 || 0,
			marketBased: marketBased || 0,
			locationBased: locationBased || 0,
			consumptionBased: consumptionBased || 0,
			scope3EmissionWithoutDownStream,
			downStreamEmission,
			success: true,
		}

		return data
	} catch (err) {
		console.error(err)
		return {
			capitalGood: 0.0,
			intermediate: 0.0,
			final: 0.0,
			totalNonRenewable: 0.0,
			totalRenewable: 0.0,
			totalStationary: 0.0,
			totalMobile:0.0,
			totalNuclear:0.0,
			scopePercentageWithStatus1:[],
			scope3CategoriesData:[],
			totalPrimaryScope:0,
			Scope_1:0.0,
			Scope_2:0.0,
			Scope_3:0.0,
			marketBased:0.0,
			locationBased:0.0,
			consumptionBased:0.0,
			scope3EmissionWithoutDownStream:0.0,
			downStreamEmission:0.0,
			success: false,
		}
	}
}
