import type { ReactElement } from "react"
import type { RenderOptions } from "@testing-library/react"
import { render } from "@testing-library/react"
import { BrowserRouter } from "react-router-dom"

// Custom render function that includes common providers
interface CustomRenderOptions extends Omit<RenderOptions, "wrapper"> {
	initialEntries?: string[]
	route?: string
}

const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
	return <BrowserRouter>{children}</BrowserRouter>
}

const customRender = (
	ui: ReactElement,
	options: CustomRenderOptions = {}
): ReturnType<typeof render> => {
	return render(ui, { wrapper: AllTheProviders, ...options })
}

// Test data factories
export const createMockUser = (overrides = {}) => ({
	id: "test-user-id",
	email: "<EMAIL>",
	firstName: "Test",
	lastName: "User",
	...overrides,
})

export const createMockTransaction = (overrides = {}) => ({
	id: "test-transaction-id",
	date: "2024-01-01",
	amount: 100,
	category: "Energy",
	scope: 1 as const,
	emissions: {
		co2: 50,
		ch4: 0.1,
		n2o: 0.05,
	},
	...overrides,
})

// Mock API responses
export const mockApiResponse = <T,>(data: T, delay = 0) => {
	return new Promise<T>(resolve => {
		setTimeout(() => resolve(data), delay)
	})
}

export const mockApiError = (message = "API Error", status = 500, delay = 0) => {
	return new Promise((_, reject) => {
		setTimeout(() => {
			const error = new Error(message) as Error & { status: number }
			error.status = status
			reject(error)
		}, delay)
	})
}

// Utility to wait for async operations
export const waitForAsync = () => new Promise(resolve => setTimeout(resolve, 0))

// Mock intersection observer entry
export const createMockIntersectionObserverEntry = (
	isIntersecting = true
): IntersectionObserverEntry => ({
	isIntersecting,
	intersectionRatio: isIntersecting ? 1 : 0,
	target: document.createElement("div"),
	boundingClientRect: {} as DOMRectReadOnly,
	intersectionRect: {} as DOMRectReadOnly,
	rootBounds: {} as DOMRectReadOnly,
	time: Date.now(),
})

// Re-export everything from testing-library
export * from "@testing-library/react"
export { default as userEvent } from "@testing-library/user-event"

// Export custom render with different name to avoid conflicts
export { customRender as renderWithProviders }
