// Scope 1 and 3 factors are in 'kgCo2e / km'
export const VEHICLE_TYPE_LIST = [
	{ name: "Motorbike", label: "Motorbike", value: "motorbike", economy: 3.5, elEconomy: 8 },
	{ name: "Mini", label: "Mini", value: "mini", economy: 4.5, elEconomy: 12 },
	{
		name: "Lower medium",
		label: "Lower medium",
		value: "lower medium",
		economy: 5.5,
		elEconomy: 15,
	},
	{ name: "Executive", label: "Executive", value: "executive", economy: 8, elEconomy: 25 },
	{ name: "Luxury car", label: "Luxury car", value: "luxuryCar", economy: 8, elEconomy: 25 },
	{ name: "SUV or Pickup", label: "SUV or Pickup", value: "suv", economy: 9, elEconomy: 25 },
	{
		name: "Multi purpose(MPV)",
		label: "Multi purpose(MPV)",
		value: "mpv",
		economy: 7.5,
		elEconomy: 20,
	},
	{ name: "Small van", label: "Small van(<3,5t)", value: "small van", economy: 10, elEconomy: 30 },
	{
		name: "Large van",
		label: "Large van(3,5t->7,5t) ",
		value: "large van",
		economy: 15,
		elEconomy: 50,
	},
	{ name: "Heavy truck", label: "Heavy truck", value: "heavytruck", economy: 36, elEconomy: 180 },
]

export const FUEL_TYPE_LIST = [
	{ name: "Petrol", label: "Petrol", value: "petrol", unit: "liters" },
	{ name: "Diesel", label: "Diesel", value: "diesel", unit: "liters" },
	{ name: "Electric", label: "Electric", value: "electric", unit: "kwh" },
	{ name: "Diesel", label: "Diesel Hybrid", value: "hybrid", unit: "liters" },
	{ name: "Petrol", label: "Petrol Hybrid", value: "hybrid", unit: "liters" },
]
