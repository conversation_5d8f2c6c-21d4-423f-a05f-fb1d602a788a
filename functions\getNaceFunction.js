/*
  This function is run when a GraphQL Query is made requesting your
  custom field name. The return value of this function is used to
  populate the resolver generated from your Payload Type.
*/

exports = async (input) => {
	try {
		const naceData = await context.services
			.get("mongodb-atlas")
			.db("co2-intensities-dev")
			.collection("nace")
			.find({})
			.toArray()
		return { status: true, data: naceData }
	} catch (err) {
		return { status: false, error: err }
	}
}
