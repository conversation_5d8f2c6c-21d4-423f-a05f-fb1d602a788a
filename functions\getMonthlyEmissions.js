let DOWNSTREAM_EMISSIONS = [];

const getMonthlyDownstreamEmission = (month) => {
  try {
    // filter DOWNSTREAM_EMISSIONS on base of month and month can in form of 1,2,4 or may be 01,02,04 that why there is || condition

    const dwonstreamEmissionData = DOWNSTREAM_EMISSIONS.filter(
      (data) => data._id == month || data._id == `0${month}`
    );

    // if length of dwonstreamEmissionData is greater then 0 it means there is some downstream emission for that month return that emission

    if (dwonstreamEmissionData.length > 0)
      return dwonstreamEmissionData[0].emission;

    // if length is 0 then return 0 because there will be no downstream emission for that particular month

    return 0;
  } catch (error) {
    console.log(error);
    return 0;
  }
};

exports = async function (input) {
  try {
    const { year, RegistrationNumber } = input;

    const MONTHS = {
      1: "January",
      2: "February",
      3: "March",
      4: "April",
      5: "May",
      6: "June",
      7: "July",
      8: "August",
      9: "September",
      10: "October",
      11: "November",
      12: "December",
    };

    let query = {
      RegistrationNumber: input.RegistrationNumber,
      PeriodYear: input.year,
    };
    if (input.AnalysisIDs) {
      query["Analysis"] = {
        $elemMatch: {
          AnalysisID: {
            $in: [...input.AnalysisIDs],
          },
        },
      };
    }

    const commonMatch = {
      $match: query,
    };

    const res = await context.services
      .get("mongodb-atlas")
      .db(context.environment.values.database)
      .collection("transaction")
      .aggregate([
        {
          ...commonMatch,
        },
        {
          $facet: {
            scopeData: [
              {
                $group: {
                  _id: "$Period",
                  Scope1: {
                    $sum: "$Scope_1",
                  },
                  Scope2: {
                    $sum: "$Scope_2",
                  },
                  Scope3: {
                    $sum: "$Scope_3",
                  },
                },
              },
            ],
            downStreamEmissions: [
              { $match: { Scope_3_Category: { $gt: 8 } } },
              {
                $group: {
                  _id: "$Period",
                  emission: {
                    $sum: "$Scope_3",
                  },
                },
              },
            ],
          },
        },
      ])
      .toArray();

    const data = res[0];

    // destructuring scopeData and categoriesData

    let { scopeData, downStreamEmissions } = data;

    // put downStreamEmissions into global variable

    DOWNSTREAM_EMISSIONS = [...downStreamEmissions];

    // Create an array of 12 objects, one for each month, and populate it with data from the database
    const Emissions = Array.from({ length: 12 }, (_, index) => {
      // Calculate the month ID (1-12) and convert it to a string

      const monthId = (index + 1).toString();

      const downStreamEmission = getMonthlyDownstreamEmission(monthId);

      // Find the existing data for the current month in the database result

      const existingData = scopeData.find(
        (data) => data._id === monthId || data._id === `0${monthId}`
      );

      // Return an object with the month ID, scope emissions, and month name
      return {
        _id: monthId,
        Scope1: existingData ? existingData.Scope1 : 0,
        Scope2: existingData ? existingData.Scope2 : 0,
        Scope3: existingData ? existingData.Scope3 - downStreamEmission : 0,
        month: MONTHS[monthId],
      };
    });
    // Sort the emissions array by month ID in ascending order
    const sortedEmissions = Emissions.sort((a, b) => a["_id"] - b["_id"]);

    // Return the sorted emissions data with a success flag
    return { success: true, EmissionData: sortedEmissions };
  } catch (err) {
    console.error(err);
    return { success: false };
  }
};
