.sidebarMenu {
	position: relative;
	height: 100%;
	width: 66px;
	background-color: #007ea7;
	align-items: flex-start !important;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.sidebarMenuToggle {
	height: 100%;
	width: 200px;
	align-items: flex-start !important;
	background-color: #007ea7;
	z-index: 999;
}

.side-menu-link-toggle-container {
	position: relative;
}

.navbar {
	padding: unset !important;
}

.active {
	background-color: #45a7c6;
	/* border-radius: 35px; */
}

.sidebarLink:hover {
	background-color: #3a93b0;
	/* border-radius: 35px; */
}

.sidebar-link-expand {
	border: solid #ffffff;
	border-width: 0 2px 2px 0;
	display: inline-block;
	padding: 3px;
}

.sidebar-sub-link-name {
	font-size: 15px;
}

.arrow-down {
	transform: rotate(45deg);
	-webkit-transform: rotate(45deg);
}

.arrow-up {
	transform: rotate(-135deg);
	-webkit-transform: rotate(-135deg);
}

.linkTag {
	text-decoration: none !important;
}

.linkTag:hover {
	color: #ffffff !important;
}

.logoContainer {
	padding-left: 0.8rem;
	align-self: center;
}

.logoSvg {
}

.logoTitle {
	align-self: center;
	color: #ffffff;
	font-size: 1.2rem;
	font-weight: 500;
}

.closeBtn {
	cursor: pointer;
	width: 40px;
	height: 40px;
}

.linkIcon {
	color: #ffffff;
	font-size: 1.6rem;
	opacity: 0.8;
}

.linkName {
	color: #f0f9ff;
	font-size: 1.1rem;
	width: 8rem;
}

.sub-link-name {
	font-size: 1rem;
}

.logout {
	cursor: pointer;
}

#profileImage {
	width: 44px;
	height: 46px;
	border-radius: 50%;
	background: #673dcb;
	font-size: 28px;
	color: #fff;
	text-align: center;
}

.sub-menu-link-show {
	display: block !important;
}

.sub-menu-link-hide {
	display: none;
}

.transaction-sub-link {
	background-color: #007ea7;
	width: 128px;
	z-index: 1;
	position: absolute;
	top: 0;
	right: -128px;
	display: none;
}

.transaction-link:hover > .transaction-sub-link {
	display: block;
}
