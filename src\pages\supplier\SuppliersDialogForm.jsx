/* eslint-disable array-callback-return */
import React from "react"
import { extend } from "@syncfusion/ej2-base"
import { v1 as uuidV1 } from "uuid"
import AsyncSelect from "react-select/async"
import { DropDownListComponent } from "@syncfusion/ej2-react-dropdowns"
import blue_flag from "../../assets/blue_flag.png"
import orange_flag from "../../assets/orange_flag.png"
import red_flag from "../../assets/red_flag.png"
import green_flag from "../../assets/green_flag.png"
import TextArea from "../../components/ui/TextArea"
import Input from "../../components/ui/Input"
import { valueFormat } from "../../services/helper"

export class SuppliersDialogForm extends React.Component {
	// this is called while editing/adding data of grid.
	constructor(props) {
		super(props)
		let data = JSON.parse(JSON.stringify(props))

		if (typeof data.Notes === "object") {
			data["Notes"] = null
		}
		if (typeof data.Contact.Telephone === "object") {
			data.Contact.Telephone = ""
		}
		if (!data.Industry) data["Industry"] = ""
		if (!data.Notes) data["Notes"] = ""

		let year = props.reportingYear

		data["IndustryCo2Intensity"] = data.IndustryCo2Intensity
			? typeof data.IndustryCo2Intensity === "object"
				? Number(data.IndustryCo2Intensity[year]).toFixed(2)
				: Number(data.IndustryCo2Intensity).toFixed(2)
			: 0

		const supplierCo2Factor = data.SupplierCo2Intensity
			? typeof data.SupplierCo2Intensity === "object"
				? Number(data.SupplierCo2Intensity[year]).toFixed(2)
				: Number(data.SupplierCo2Intensity).toFixed(2)
			: 0
		data["SupplierCo2Intensity"] = supplierCo2Factor

		this.state = extend({}, {}, data, true)
		this.editorData = [
			{ text: "blue flag", value: 0 },
			{ text: "green flag", value: 1 },
			{ text: "orange flag", value: 2 },
			{ text: "red flag", value: 3 },
		]
		this.valueToFlag = {
			0: blue_flag,
			1: green_flag,
			2: orange_flag,
			3: red_flag,
		}
		this.displayNameForFlag = {
			0: "Info",
			1: "Ok",
			2: "Processed",
			3: "Warning",
		}
		this.dropDownFields = { text: "text", value: "value" }
		// Dropdown list
		this.flagItemTemplate = props => {
			return (
				<div className="flex items-center">
					<img src={this.valueToFlag[props.value]} alt={props.value} />
					{this.displayNameForFlag[props.value]}
				</div>
			)
		}
		this.flagValueTemplate = props => {
			return (
				<div className="flex items-center">
					<img src={this.valueToFlag[props.value]} alt={props.value} />
					{this.displayNameForFlag[props.value]}
				</div>
			)
		}
		if (props.isAdd) {
			// Create new id for new supplier
			let supplierId = "SI-" + uuidV1()
			this.state = {
				...data,
				SupplierID: supplierId,
				RegistrationNumber: "",
				Contact: {
					ContactPerson: {
						FirstName: "",
						LastName: "",
					},
					Email: "",
					Telephone: "",
				},
				Name: "",
				ClosingCreditBalance: "",
				Emission: "",
				NaceCode: "",
				Industry: "",
				Notes: "",
				selectedCompany: { label: "", value: "" },
				selectedCompanyOption: [],
				selectedInsertType: "",
			}
		}
	}

	onChange(args) {
		let key = args.target.name
		let value = args.target.value
		this.setState({ [key]: value })
	}

	onChangeNaceCode(args) {
		let NaceCode = args.target.value
		this.setState({ NaceCode: NaceCode })
	}

	onChangeIndustry(args) {
		let Industry = args.target.value
		this.setState({ Industry: Industry })
	}

	onChangeName(args) {
		let Name = args.target.value
		this.setState({ Name: Name })
	}

	onChangeNotes(args) {
		let Notes = args.target.value
		this.setState({ Notes: Notes })
	}

	onChangeEmail(args) {
		let Contact = this.state.Contact
		Contact.Email = args.target.value
		this.setState({ Contact: Contact })
	}

	onChangeTelephone(args) {
		let Contact = this.state.Contact
		Contact.Telephone = args.target.value
		this.setState({ Contact: Contact })
	}

	onChangeFirstName(args) {
		let Contact = { ...this.state.Contact }
		Contact.ContactPerson.FirstName = args.target.value
		this.setState({ Contact: Contact })
	}

	onChangeLastName(args) {
		let Contact = this.state.Contact
		Contact.ContactPerson.LastName = args.target.value
		this.setState({ Contact: Contact })
	}

	companyChange(event) {
		if (event && event.data && event.value) {
			let RegistrationNumber = event.value
			this.setState({ RegistrationNumber: RegistrationNumber })

			if (event.data.navn) {
				this.setState({ Name: event.data.navn })
			} else {
				this.setState({ Name: "" })
			}
			if (event.data.naeringskode1 && event.data.naeringskode1.kode) {
				let code = event.data.naeringskode1.kode

				let industry = this.state.naceData.filter(nace => {
					if (code === nace.code) {
						return nace
					}
				})

				if (industry.length > 0) {
					let year = this.props.reportingYear

					if (!industry[0].co2Intensity[this.props.reportingYear]) {
						year -= 1
					}
					this.setState({
						NaceCode: event.data.naeringskode1.kode,
						IndustryCo2Intensity: industry[0].co2Intensity[year].toFixed(2),
						SupplierCo2Intensity: industry[0].co2Intensity[year].toFixed(2),
					})
				} else {
					this.setState({
						NaceCode: event.data.naeringskode1.kode,
						IndustryCo2Intensity: 0.0,
						SupplierCo2Intensity: 0.0,
					})
				}
			} else {
				this.setState({ NaceCode: "" })
			}
			if (event.data.naeringskode1 && event.data.naeringskode1.beskrivelse) {
				this.setState({ Industry: event.data.naeringskode1.beskrivelse })
			} else {
				this.setState({ Industry: "" })
			}
		}
	}

	companyInputChange(inputValue) {
		return new Promise((resolve, reject) => {
			if (!inputValue.trim() || inputValue.trim().length <= 3) {
				this.setState({ selectedCompanyOption: [] })
				return resolve([])
			}
			let totalElementPerRequest = 1000
			let url =
				"https://data.brreg.no/enhetsregisteret/api/enheter?navn=" +
				inputValue.trim() +
				"&size=" +
				totalElementPerRequest
			fetch(url)
				.then(response => response.json())
				.then(result => {
					if (result && result.page) {
						if (result.totalElements > totalElementPerRequest) {
							let url =
								"https://data.brreg.no/enhetsregisteret/api/enheter?navn=" +
								inputValue.trim() +
								"&size=" +
								result.totalElements
							fetch(url)
								.then(response => response.json())
								.then(result => {
									if (
										result._embedded &&
										result._embedded.enheter &&
										result._embedded.enheter.length
									) {
										let searchData = []
										for (let data of result._embedded.enheter) {
											let label = `${data.navn} (${data.organisasjonsnummer})`
											searchData.push({
												label: label,
												value: data.organisasjonsnummer,
												data: data,
											})
										}
										resolve(searchData)
									} else {
										resolve([])
									}
								})
						} else {
							if (result._embedded && result._embedded.enheter && result._embedded.enheter.length) {
								let searchData = []
								for (let data of result._embedded.enheter) {
									let label = `${data.navn} (${data.organisasjonsnummer})`
									searchData.push({
										label: label,
										value: data.organisasjonsnummer,
										data: data,
									})
								}
								resolve(searchData)
							} else {
								resolve([])
							}
						}
					} else {
						resolve([])
					}
				})
		})
	}

	naceChange(event) {
		if (event && event.data && event.value) {
			if (event.data && event.data.code) {
				this.setState({
					NaceCode: event.data.code,
					IndustryCo2Intensity: event.data.co2Intensity[this.props.reportingYear].toFixed(2),
					Industry: event.data.name,
					SupplierCo2Intensity: event.data.co2Intensity[this.props.reportingYear].toFixed(2),
				})
			} else {
				this.setState({ NaceCode: "" })
			}
			if (event.data && event.data.name) {
				this.setState({ Industry: event.data.name })
			} else {
				this.setState({ Industry: "" })
			}
		}
	}

	naceInputChange(inputValue) {
		return new Promise((resolve, reject) => {
			if (this.state.naceData && this.state.naceData.length) {
				let naceData = []
				this.state.naceData.forEach(nace => {
					if (nace.code.includes(inputValue) || nace.name.includes(inputValue)) {
						naceData.push({
							label: `(${nace?.code}) ${nace?.name}`,
							value: nace.code,
							data: nace,
						})
					}
				})
				resolve(naceData)
			} else {
				resolve([])
			}
		})
	}

	onChangeFlag(args) {
		this.setState({
			Status: args.itemData.value,
			hasFlagChanged: true,
		})
	}

	componentDidMount() {
		if (this.props.isAdd) {
			this.companyName.focus()
		}
	}

	customStyles = {
		control: base => ({
			...base,
			boxShadow: "0 !important",
			"&:hover": {
				boxShadow: "0 !important",
			},
			"&:focus": {
				boxShadow: "0 !important",
			},
		}),
	}

	handleChange = e => {
		let key = e.target.name
		let value = e.target.value
		this.setState({ [key]: value })
	}

	render() {
		const { t, language } = this.props

		const locale = language === "en" ? "en-EN" : "no-NO"

		return (
			<div>
				{this.props.isAdd ? (
					<div className="grid gap-2">
						{/* Hidden Input Fields */}
						<input
							id="RegistrationNumber"
							name="RegistrationNumber"
							type="hidden"
							value={this.state.RegistrationNumber}
							required
							onChange={() => {}}
						/>
						<input id="Status" name="Status" type="hidden" value={1} required onChange={() => {}} />
						<input
							id="Emission"
							name="Emission"
							type="hidden"
							value={0.0}
							required
							onChange={() => {}}
						/>
						<input
							id="numberOfTransactions"
							name="numberOfTransactions"
							type="hidden"
							value={0.0}
							required
							onChange={() => {}}
						/>

						{/* Search Company by Name */}
						<div className="col-span-full">
							<div className="custom-select-search">
								<AsyncSelect
									id="company-list"
									placeholder={t("SearchCompanyByName")}
									ref={input => (this.companyName = input)}
									onChange={this.companyChange.bind(this)}
									loadOptions={this.companyInputChange.bind(this)}
									styles={this.customStyles}
								/>
							</div>
						</div>

						<hr className="col-span-full" />

						{/* Supplier Name and Supplier ID */}
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<Input
									label={t("common:SupplierName")}
									id="Name"
									name="Name"
									className="border h-8 w-full rounded-lg px-2"
									type="text"
									handleChange={e => this.handleChange(e)}
									value={this.state.Name}
								/>
							</div>
							<div>
								<Input
									label={t("SupplierID")}
									id="SupplierID"
									name="SupplierID"
									type="text"
									disable
									value={uuidV1()}
								/>
							</div>
						</div>

						{/* Search by Industry or Nace Code */}
						<div className="col-span-full">
							<div className="custom-select-search">
								<AsyncSelect
									id="industry-nace-list"
									placeholder={t("SearchByIndustryOrNaceCode")}
									onChange={this.naceChange.bind(this)}
									loadOptions={this.naceInputChange.bind(this)}
									styles={this.customStyles}
								/>
							</div>
						</div>

						{/* Nace Code and Industry */}
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label className="text-sky-600">{t("common:NaceCode")}</label>
								<input
									id="NaceCode"
									name="NaceCode"
									type="number"
									className="h-8 w-full disabled:cursor-not-allowed bg-gray-100 rounded-lg px-2 relative border-gray-500 border-2 border-dashed"
									disabled
									readOnly
									value={this.state.NaceCode}
								/>
							</div>
							<div>
								<label className="text-sky-600">{t("common:Industry")}</label>
								<textarea
									id="Industry"
									name="Industry"
									className="h-fit w-full disabled:cursor-not-allowed bg-gray-100 rounded-lg px-2 py-2 relative border-gray-500 border-2 border-dashed"
									disabled
									value={this.state.Industry}
									readOnly
								/>
							</div>
						</div>

						{/* Industry Carbon Intensity and Supplier Carbon Intensity */}
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<Input
									label={t("Industry carbon intensity")}
									id="IndustryCo2Intensity"
									name="IndustryCo2Intensity"
									type="number"
									disable
									value={
										this.state.IndustryCo2Intensity
											? Number(this.state.IndustryCo2Intensity).toFixed(2)
											: "--"
									}
									showUnit
									unit={t("per 100 NOK")}
								/>
							</div>
							<div>
								<Input
									label={t("Supplier carbon intensity")}
									name="SupplierCo2Intensity"
									id="SupplierCo2Intensity"
									type="text"
									value={this.state.SupplierCo2Intensity}
									handleChange={e => {
										const sanitizedInput = e.target.value.replace(/[^0-9.,]/g, "")

										const normalizedInput = sanitizedInput.replace(",", ".")

										const dotOrCommaCount = sanitizedInput.split(/[.,]/).length - 1

										if (dotOrCommaCount <= 1) {
											e.target.value = normalizedInput
											this.handleChange(e)
										}
										//this.handleChange(e)
									}}
									showUnit
									unit={t("per 100 NOK")}
								/>
							</div>
						</div>

						<h5 className="mb-3 text-sky-900 col-span-full">Contact details</h5>

						{/* Contact Details */}
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<Input
									label={t("common:FirstName")}
									id="FirstName"
									name="Contact.ContactPerson.FirstName"
									type="text"
									value={this.state.Contact.ContactPerson.FirstName}
									handleChange={e => this.onChangeFirstName(e)}
								/>
							</div>
							<div>
								<Input
									label={t("common:LastName")}
									id="LastName"
									name="Contact.ContactPerson.LastName"
									type="text"
									value={this.state.Contact.ContactPerson.LastName}
									handleChange={e => this.onChangeLastName(e)}
								/>
							</div>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<Input
									label={t("common:Email")}
									id="Email"
									name="Contact.Email"
									type="text"
									value={this.state.Contact.Email}
									handleChange={e => this.onChangeEmail(e)}
								/>
							</div>
							<div>
								<Input
									label={t("common:Telephone")}
									id="phone"
									name="Contact.Telephone"
									type="text"
									value={this.state.Contact.Telephone}
									handleChange={e => this.onChangeTelephone(e)}
								/>
							</div>
						</div>

						<div className="col-span-full">
							<TextArea
								label={t("common:Notes")}
								placeholder="Input"
								id="Notes"
								name="Notes"
								type="text"
								className="h-10 w-full rounded-lg px-2 py-2 border-2"
								handleChange={e => this.handleChange(e)}
								value={this.state.Notes ? this.state.Notes : ""}
							/>
						</div>
					</div>
				) : (
					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<input
							id="RegistrationNumber"
							name="RegistrationNumber"
							type="hidden"
							disabled={false}
							value={this.state.RegistrationNumber}
							required={true}
							onChange={() => {}}
						/>
						<div className="md:col-span-1">
							<Input
								label={t("common:SupplierName")}
								id="Name"
								name="Name"
								value={this.state.Name}
								type="text"
								handleChange={e => {
									this.handleChange(e)
								}}
							/>
						</div>
						<div className="md:col-span-1">
							<label className="font-semibold block text-sm leading-4 mb-1 text-slate-800">
								{t("Status")}
							</label>
							<DropDownListComponent
								id="Status"
								name="Status"
								value={this.state?.Status}
								itemTemplate={this.flagItemTemplate}
								valueTemplate={this.flagValueTemplate}
								dataSource={this.editorData}
								fields={this.dropDownFields}
								change={e => this.onChangeFlag(e)}
							/>
							<input style={{ display: "none" }} />
						</div>
						{this.state.Status === 3 && (
							<div className="md:col-span-2">
								<div className="e-float-input e-control-wrapper custom-select-search">
									<AsyncSelect
										id="company-list"
										placeholder={t("SearchCompanyByName")}
										ref={input => (this.companyName = input)}
										onChange={this.companyChange.bind(this)}
										loadOptions={this.companyInputChange.bind(this)}
										styles={this.customStyles}
									/>
								</div>
							</div>
						)}
						<div className="md:col-span-1">
							<Input
								label={t("common:Emissions")}
								id="Emission"
								name="Emission"
								type="text"
								disable={true}
								value={
									this.state.Emission
										? valueFormat(Math.round(this.state.Emission * 1000), locale, "number")
										: 0.0
								}
								unit={t("Kg-Co2e")}
							/>
						</div>
						<div className="md:col-span-1">
							<Input
								label={t("SupplierID")}
								id="SupplierID"
								name="SupplierID"
								type="text"
								disable={true}
								value={this.state.SupplierID}
							/>
						</div>
						<div className="md:col-span-2">
							<div className="e-float-input e-control-wrapper custom-select-search">
								<AsyncSelect
									id="industry-nace-list"
									placeholder={t("SearchByIndustryOrNaceCode")}
									onChange={this.naceChange.bind(this)}
									loadOptions={this.naceInputChange.bind(this)}
									styles={this.customStyles}
								/>
							</div>
						</div>
						<div className="md:col-span-1">
							<Input
								label={t("common:NaceCode")}
								id="NaceCode"
								name="NaceCode"
								type="number"
								disable={true}
								value={this.state.NaceCode}
							/>
						</div>
						<div className="md:col-span-1">
							<TextArea
								label={t("common:Industry")}
								id="Industry"
								name="Industry"
								type="text"
								disable={true}
								value={this.state.Industry}
							/>
						</div>
						<div className="md:col-span-1">
							<Input
								label={t("Industry carbon intensity")}
								id="IndustryCo2Intensity"
								name="IndustryCo2Intensity"
								type="number"
								disable={true}
								value={
									this.state.IndustryCo2Intensity
										? Number(this.state.IndustryCo2Intensity).toFixed(2)
										: "--"
								}
								unit={t("per 100 NOK")}
							/>
						</div>
						<div className="md:col-span-1">
							<Input
								label={t("Supplier carbon intensity")}
								name={"SupplierCo2Intensity"}
								id={"SupplierCo2Intensity"}
								value={this.state.SupplierCo2Intensity ? this.state.SupplierCo2Intensity : ""}
								type="text"
								handleChange={e => {
									const sanitizedInput = e.target.value.replace(/[^0-9.,]/g, "")

									const normalizedInput = sanitizedInput.replace(",", ".")

									// Allow only one dot or comma
									const dotOrCommaCount = sanitizedInput.split(/[.,]/).length - 1
									if (dotOrCommaCount <= 1) {
										e.target.value = normalizedInput
										this.handleChange(e)
									}
								}}
								unit={t("per 100 NOK")}
							/>
						</div>
						<h5 className="md:col-span-2 mt-2 mb-2 text-sky-900">Contact details</h5>
						<div className="md:col-span-1">
							<Input
								label={t("common:FirstName")}
								id="FirstName"
								name="Contact.ContactPerson.FirstName"
								type="text"
								value={this.state.Contact.ContactPerson.FirstName}
								handleChange={e => {
									this.onChangeFirstName(e)
								}}
							/>
						</div>
						<div className="md:col-span-1">
							<Input
								label={t("common:LastName")}
								id="LastName"
								name="Contact.ContactPerson.LastName"
								type="text"
								value={this.state.Contact.ContactPerson.LastName}
								handleChange={e => {
									this.onChangeLastName(e)
								}}
							/>
						</div>
						<div className="md:col-span-1">
							<Input
								label={t("common:Email")}
								id="Email"
								name="Contact.Email"
								type="text"
								value={this.state.Contact.Email}
								handleChange={e => {
									this.onChangeEmail(e)
								}}
							/>
						</div>
						<div className="md:col-span-1">
							<Input
								label={t("common:Telephone")}
								id="phone"
								name="Contact.Telephone"
								type="text"
								value={this.state.Contact.Telephone}
								handleChange={e => {
									this.onChangeTelephone(e)
								}}
							/>
						</div>
						<div className="md:col-span-2">
							<TextArea
								label={t("common:Notes")}
								placeholder="Input"
								id="Notes"
								name="Notes"
								type="text"
								className="h-20 w-full rounded-lg px-2 py-2 border-2"
								handleChange={e => {
									this.handleChange(e)
								}}
								value={this.state.Notes ? this.state.Notes : ""}
							/>
						</div>
					</div>
				)}
			</div>
		)
	}
}
