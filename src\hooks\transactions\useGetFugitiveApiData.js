import { useQuery } from "@tanstack/react-query"
import axios from "axios"

import { METRIC_FUGITIVE_API_DATA } from "../../pages/transaction/EditDetails/Scope1/FUGITIVE_DATA"

const source = axios.CancelToken.source()

const getFugitiveApiData = async () => {
	try {
		const dataToSend = {
			summary: METRIC_FUGITIVE_API_DATA,
		}
		const res = await axios.post(
			`${import.meta.env.VITE_EMISSION_API_HOST}/emissions`,
			dataToSend,
			{
				headers: {
					"x-authorization": import.meta.env.VITE_EMISSION_API_SECRET,
				},
				cancelToken: source.token,
			}
		)

		return res.data // Ensure you return res.data or another relevant part of the response
	} catch (error) {
		throw new Error("Failed to fetch fugitive data")
	}
}
const useGetFugitiveApiData = () => {
	return useQuery({
		queryKey: ["FugitiveApiData"],
		queryFn: getFugitiveApiData,
	})
}

export default useGetFugitiveApiData
