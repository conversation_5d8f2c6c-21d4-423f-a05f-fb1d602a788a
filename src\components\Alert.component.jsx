import { Dialog, Transition } from "@headlessui/react"
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline"
import React, { Fragment } from "react"

const Alert = ({
	show,
	title,
	text,
	t,
	handleClose,
	handleContinue,
	hasInput = false,
	inputPlaceholder = "",
	handleInputChange,
}) => {
	//const [show, setShow] = useState(false);

	//const handleClose = () => setShow(false);
	//const handleShow = () => setShow(true);

	return (
		<div>
			<Transition.Root show={show} as={Fragment}>
				<Dialog as="div" className="relative z-50" onClose={handleClose}>
					<Transition.Child
						as={Fragment}
						enter="ease-out duration-300"
						enterFrom="opacity-0"
						enterTo="opacity-100"
						leave="ease-in duration-200"
						leaveFrom="opacity-100"
						leaveTo="opacity-0"
					>
						<div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
					</Transition.Child>

					<div className="fixed inset-0 z-10 overflow-y-auto">
						<div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
							<Transition.Child
								as={Fragment}
								enter="ease-out duration-300"
								enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
								enterTo="opacity-100 translate-y-0 sm:scale-100"
								leave="ease-in duration-200"
								leaveFrom="opacity-100 translate-y-0 sm:scale-100"
								leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
							>
								<Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
									<div className="sm:flex sm:items-start">
										<div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
											<ExclamationTriangleIcon
												className="h-6 w-6 text-red-600"
												aria-hidden="true"
											/>
										</div>
										<div className=" sm:ml-4 sm:mt-0 sm:text-left">
											<Dialog.Title
												as="h2"
												className="text-lg font-semibold leading-6 text-rose-700"
											>
												{title}
											</Dialog.Title>
											<div className="">
												<p className="text-sm text-slate-700 mb-0">{text}</p>
											</div>
										</div>
									</div>

									{hasInput && (
										<div className="mt-4">
											<input
												type="text"
												placeholder={inputPlaceholder}
												onChange={e => handleInputChange(e)}
												className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm"
											/>
										</div>
									)}

									<div className=" sm:flex mt-3 sm:flex-row-reverse">
										<button
											onClick={handleContinue}
											className="mt-3 inline-flex w-full justify-center rounded-md bg-sky-600 px-3 py-2 ml-2 text-sm font-semibold text-white shadow-sm border border-inset hover:bg-sky-600 sm:mt-0 sm:w-auto"
										>
											{t("continue")}
										</button>
										<button
											onClick={handleClose}
											className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-sky-700 shadow-sm border border-inset border-sky-400 hover:bg-sky-50 sm:mt-0 sm:w-auto"
										>
											{t("cancel")}
										</button>
									</div>
								</Dialog.Panel>
							</Transition.Child>
						</div>
					</div>
				</Dialog>
			</Transition.Root>
		</div>
	)
}

export default Alert
