exports = async (input) => {
	// Initialize an empty array to hold the suppliers of the company
	let suppliersOfCompany = []
	try {
		// Define the pipeline for the aggregation query
		const pipeline = [
			{
				// Filter companies by RegistrationNumber
				$match: { RegistrationNumber: input.RegistrationNumber },
			},
			{
				// Perform a lookup (join) with the transaction collection
				$lookup: {
					from: "transaction",
					let: { company_id: "$_id" },
					pipeline: [
						{
							// Match transactions by RegistrationNumber and PeriodYear
							$match: {
								$expr: {
									$and: [
										{ $eq: ["$RegistrationNumber", input.RegistrationNumber] },
										{ $eq: ["$PeriodYear", input.year] },
									],
								},
							},
						},
						{
							// Group the matched transactions by SupplierID
							// Calculate the total amount, total emission, and transaction count for each supplier
							$group: {
								_id: "$SupplierID",
								Amount: { $sum: "$Amount" },
								Emission: { $sum: { $add: ["$Scope_1", "$Scope_2", "$Scope_3"] } },
								numberOfTransactions: { $sum: 1 },
							},
						},
					],
					// Output the results of the lookup into the suppliersData field
					as: "suppliersData",
				},
			},
			{
				// Project the necessary fields in the final output
				$project: {
					_id: 1,
					Suppliers: 1,
					suppliersData: 1,
				},
			},
		]
		// Execute the aggregation pipeline
		const result = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("company")
			.aggregate(pipeline)
			.toArray()

		// Destructure the Suppliers and suppliersData from the first result
		const { Suppliers, suppliersData } = result[0]

		// Convert the suppliersData array into an object where each key is a SupplierID
		// and each value is an object containing Amount, Emission, and numberOfTransactions
		const refinedSupplierData = suppliersData.reduce((result, currentObject) => {
			const { _id, ...rest } = currentObject
			result[_id] = rest
			return result
		}, {})

		// Iterate through the Suppliers array and update the supplier data
		for (let i = 0; i < Suppliers.length; i++) {
			const supplierId = Suppliers[i].SupplierID
			let { Amount, Emission, numberOfTransactions } = refinedSupplierData[supplierId] || {}
			Amount = Amount || 0
			Emission = Emission || 0
			numberOfTransactions = numberOfTransactions || 0
			//Suppliers[i].SupplierCo2IntensitySaved = Suppliers[i].SupplierCo2Intensity
			Suppliers[i].SupplierCo2Intensity = Suppliers[i].SupplierCo2Intensity
			Suppliers[i].IndustryCo2Intensity =
				typeof Suppliers[i].IndustryCo2Intensity === "object"
					? Suppliers[i].IndustryCo2Intensity[input.year] || 0
					: Suppliers[i].IndustryCo2Intensity
			Suppliers[i].Emission = Emission || 0.0
			Suppliers[i].Amount = Amount || 0.0
			Suppliers[i].numberOfTransactions = numberOfTransactions || 0
			Suppliers[i].isSupplierFactorUpdated = Suppliers[i]?.isSupplierFactorUpdated || false
		}
		// Update the suppliersOfCompany array with the updated Suppliers array
		suppliersOfCompany = Suppliers
	} catch (err) {
		// Log any errors and return an empty object
		console.log(err)
		return {}
	}
	// Return the suppliersOfCompany array
	
	return suppliersOfCompany
}
