{"properties": {"Accounts": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"AccountCreationDate": {"bsonType": "string"}, "AccountDescription": {"bsonType": "string"}, "AccountID": {"bsonType": "string"}, "AccountType": {"bsonType": "string"}, "ClosingCreditBalance": {"bsonType": "string"}, "ClosingDebitBalance": {"bsonType": "string"}, "OpeningCreditBalance": {"bsonType": "string"}, "OpeningDebitBalance": {"bsonType": "string"}, "StandardAccountID": {"bsonType": "string"}}}}, "Address": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"AddressType": {"bsonType": "string"}, "City": {"bsonType": "string"}, "Country": {"bsonType": "string"}, "PostalCode": {"bsonType": "string"}, "StreetName": {"bsonType": "string"}}}}, "ContactPerson": {"bsonType": "object", "properties": {"FirstName": {"bsonType": "string"}, "LastName": {"bsonType": "string"}}}, "Name": {"bsonType": "string"}, "RegistrationNumber": {"bsonType": "string"}, "Suppliers": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"Address": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"AddressType": {"bsonType": "string"}, "City": {"bsonType": "string"}, "Country": {"bsonType": "string"}, "Number": {"bsonType": "string"}, "PostalCode": {"bsonType": "string"}, "StreetName": {"bsonType": "string"}}, "required": ["AddressType"]}}, "ClosingCreditBalance": {"bsonType": "string"}, "Contact": {"bsonType": "object", "properties": {"ContactPerson": {"bsonType": "object", "properties": {"FirstName": {"bsonType": "string"}, "Initials": {"bsonType": "string"}, "LastName": {"bsonType": "string"}, "OtherTitles": {"bsonType": "string"}, "Salutation": {"bsonType": "string"}}}, "Email": {"bsonType": "string"}, "Fax": {"bsonType": "string"}, "Telephone": {"bsonType": "string"}, "Website": {"bsonType": "string"}}}, "Industry": {"bsonType": "string"}, "NaceCode": {"bsonType": "string"}, "NaicsCode": {"bsonType": "string"}, "Name": {"bsonType": "string"}, "RegistrationNumber": {"bsonType": "string"}, "SupplierID": {"bsonType": "string"}}}}, "_id": {"bsonType": "objectId"}}, "title": "company"}