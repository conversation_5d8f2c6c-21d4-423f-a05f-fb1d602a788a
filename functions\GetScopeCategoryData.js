const category_description = {
	1: "Purchased Goods and Services",
	2: "Capital Goods",
	3: "Fuel and Energy Related Activities",
	4: "Upstream Transportation and Distribution",
	5: "Waste Generated in Operations",
	6: "Business Travel",
	7: "Employee Commuting",
	8: "Upstream leased assets",
}

let totalEmissionCategoriesData = []

function adjustPercentages(categoryData) {
	// Step 1: Extract and round each percentage to two decimal places

	let roundedPercentages = categoryData.map((item) => Number(item.Percentage))

	// Step 2: Calculate the total of rounded percentages
	let totalPercentage = roundedPercentages.reduce((sum, p) => Number(sum) + Number(p), 0)

	// if there is no data then no need to adjust percentages because its all zero

	if (totalPercentage === 0) return categoryData

	// Step 3: Calculate the difference to adjust the sum to 100%
	let roundingError = parseFloat((100 - totalPercentage).toFixed(2))

	// Step 4: Adjust the largest percentage to correct any rounding errors
	if (roundingError !== 0) {
		let maxIndex = roundedPercentages.findIndex((p) => p === Math.max(...roundedPercentages))
		roundedPercentages[maxIndex] = parseFloat((roundedPercentages[maxIndex] + roundingError).toFixed(2))
	}

	return categoryData.map((item, index) => ({
		...item,
		Percentage: roundedPercentages[index],
	}))
}

const getEmissionFromTotalEmissionCategories = (id) => {
	const data = totalEmissionCategoriesData.filter((category) => category._id == id)

	if (data.length == 0) return 0

	return data[0].Emission
}

const getPercentage = (item, totalEmission) => {
	if (item && item.Emission && item.Emission > 0 && totalEmission > 0) {
		return Number((item.Emission / totalEmission) * 100).toFixed(2)
	} else {
		return 0
	}
}

exports = async function (input) {
	try {
		const res = context.services.get("mongodb-atlas").db(context.environment.values.database).collection("transaction")

		// here we are getting the scope 3 categories with status 1 and total scope 3 categories data as well

		let query = {
			RegistrationNumber: input.RegistrationNumber,
			PeriodYear: input.year,
		}
		if (input.AnalysisIDs) {
			query["Analysis"] = {
				$elemMatch: {
					AnalysisID: {
						$in: [...input.AnalysisIDs],
					},
				},
			}
		}

		const [totalCategoryData, categoryDataWithStatus1] = await Promise.all([
			res
				.aggregate([{ $match: { ...query } }, { $group: { _id: "$Scope_3_Category", Emission: { $sum: "$Scope_3" } } }])
				.toArray(),
			res
				.aggregate([
					{ $match: { ...query, Status: 1 } },
					{ $group: { _id: "$Scope_3_Category", Emission: { $sum: "$Scope_3" } } },
				])
				.toArray(),
		])

		// totalEmissionCategoriesData is a gloabal variable we are setting it to
		// totalCategoryData so that we can use it in
		// getEmissionFromTotalEmissionCategories function

		totalEmissionCategoriesData = [...totalCategoryData]

		let categoriesWithStatus1 = []

		for (let i = 1; i <= 15; i++) {
			// if i is less the 9 that means those are upstream categories
			// here we are finding percentage of the scope 3 categories with status 1
			// to get primary data
			if (i < 9) {
				const item = categoryDataWithStatus1.find((category) => category._id === i) || 0

				// getEmissionFromTotalEmissionCategories is a function which will return the
				// total emission of the category

				const totalEmission = getEmissionFromTotalEmissionCategories(i)

				categoriesWithStatus1.push({
					_id: i,
					Emission: item ? item.Emission : 0,
					Percentage: getPercentage(item, totalEmission),
				})
			}
		}

		// here we are finding total emission of upstram categories

		let totalEmission = totalCategoryData.reduce((sum, item) => {
			if (item._id && !isNaN(item._id) && item._id <= 8) {
				return sum + item.Emission
			} else {
				return sum
			}
		}, 0)

		// Initialize downStreamTotalEmissionCatagory
		let downStreamTotalEmissionCatagory = 0

		// Initialize categoryData array
		let categoryData = []

		// Step 2: Loop through categories 1 to 15 and calculate percentages
		// if i is less then 9 that means those are upstream categories
		// else those are downstream categories
		for (let i = 1; i <= 15; i++) {
			const item = totalCategoryData.find((category) => category._id === i)
			if (i <= 8) {
				let percentage = 0
				if (item || item != undefined) {
					// Calculate raw percentage
					percentage = getPercentage(item, totalEmission)
					categoryData.push({
						Scope_3_Category: i,
						Description: category_description[i],
						Percentage: percentage, // Store the raw percentage for adjustment later
						Emission: item.Emission,
					})
				} else {
					categoryData.push({
						Scope_3_Category: i,
						Description: category_description[i],
						Percentage: 0,
						Emission: 0,
					})
				}
			} else {
				if (item) {
					downStreamTotalEmissionCatagory += item.Emission
				}
			}
		}

		// adjust percentages because sometimes the sum is not 100%

		let categoryDataWithAdjustedPercentages = adjustPercentages(categoryData)

		return {
			categoryData: categoryDataWithAdjustedPercentages,
			categoriesWithStatus1,
			downStreamTotalEmissionCatagory,
			success: true,
		}
	} catch (err) {
		console.log(err)
		return { success: false }
	}
}
