let accountData = {}
let outputData = []

const getAccountId = (AccountID, StandardAccountID) => {
	const AccountID2Digit = AccountID.substring(0, 2)

	let finalAccountID = ""

	if (AccountID in accountData) {
		finalAccountID = AccountID
	} else if (StandardAccountID in accountData) {
		finalAccountID = StandardAccountID
	} else if (AccountID2Digit in accountData) {
		finalAccountID = AccountID2Digit
	}
	return finalAccountID
}

const getStandardAccountID = async (RegistrationNumber, AccountID) => {
	try {
		const company = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("company")
			.findOne({ RegistrationNumber: RegistrationNumber, "Accounts.AccountID": AccountID }, { "Accounts.$": 1 })
		if (company && company.Accounts && company.Accounts.length) {
			return company.Accounts[0].StandardAccountID
		} else {
			return ""
		}
	} catch (err) {
		return ""
	}
}

const getFunctionName = async (transactions, AccountID) => {
	// Get the account data from value AccountID
	accountData = context.values.get("accountData")

	AccountID = "" + AccountID
	if (AccountID in accountData) {
		return { functionData: accountData[AccountID], functionName: accountData[AccountID].function }
	}

	const StandardAccountID = await getStandardAccountID(transactions.RegistrationNumber, AccountID)
	const finalAccountID = getAccountId(AccountID, StandardAccountID)
	if (!finalAccountID) {
		return { functionData: null, functionName: "f_default" }
	}
	const data = accountData[finalAccountID]
	return { functionData: data, functionName: data.function }
}

const updateAccountID = async (transactions, AccountID, functionName, functionData) => {
	const Amount = parseFloat(transactions.Amount)
	const emissionData = context.functions.execute("RunAccountDataFunction", {
		functionName: functionName,
		Amount: Amount,
		period: "" + parseInt(transactions.Period),
		year: transactions.PeriodYear,
		// Why do we not need AccountID here?
	})
	let Scope_1 = emissionData.scope_1
	let Scope_2 = emissionData.scope_2
	let Scope_3 = emissionData.scope_3
	let liter = emissionData.liter ? emissionData.liter : 0.0
	let kwh = emissionData.kwh ? emissionData.kwh : transactions.kwh
	try {
		let updateValue = {}
		if (functionName == "f_default" || !functionName) {
			let data = {
				AccountID: AccountID,
				liter: liter,
				AccountDescription: transactions.AccountDescription,
			}
			if (functionData && functionData.relation && functionData.relation == "") {
				data.Relation = functionData.relation
			}
			if ((functionData && functionData.type) || functionData.type == "") {
				data.Type = functionData.type
			}
			if (functionData && functionData.scope) {
				data.Scope = functionData.scope
			}
			if (functionData && functionData["scope-3-category"]) {
				data.Scope_3_Category = functionData["scope-3-category"]
			}
			updateValue = {
				$set: data,
			}
			Scope_1 = transactions.Scope_1
			Scope_2 = transactions.Scope_2
			Scope_3 = transactions.Scope_3
		} else {
			let data = {
				AccountID: AccountID,
				Scope_1: Scope_1,
				Scope_2: Scope_2,
				Scope_3: Scope_3,
				liter: liter,
				kwh: kwh,
				AccountDescription: transactions.AccountDescription,
				Relation: functionData.relation ? functionData.relation : "",
				Type: functionData.type ? functionData.type : "",
			}
			if (functionData.scope) {
				data.Scope = functionData.scope
			}
			if (functionData["scope-3-category"]) {
				data.Scope_3_Category = functionData["scope-3-category"]
			}
			updateValue = {
				$set: data,
			}
		}
		const request = context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("transaction")
		const result = await request.findOneAndUpdate(
			{
				_id: transactions._id,
			},
			updateValue,
			{ returnOriginal: false, returnDocument: "after" }
		)
		const updateResult = await request.findOne({ _id: transactions._id })
		outputData.push(updateResult)
	} catch (err) {
		outputData.push({})
		console.error("Error in updating transactions:", err)
	}
}

exports = async function (arg) {
	const AccountID = arg.AccountID
	const transactions = arg.transactions

	if (transactions.length == 0) {
		return { success: true, data: outputData }
	}
	let IDS = []
	for (let i = 0; i < transactions.length; i++) {
		IDS.push(transactions[i]._id)
	}
	const data = await context.functions.execute("rerunEmissionTransactionFunction", { ids: IDS, AccountID })
	return { success: true, data: data }
}
