import { useQuery } from "@tanstack/react-query"

export default function useGetScopeCategoryData(app, input, enabled = true) {
	return useQuery({
		queryKey: ["scope_catagory_data", input],
		queryFn: async () => {
			const { RegistrationNumber, year, AnalysisIDs } = input
			if (input.RegistrationNumber !== "")
				return await app.getScopeCategoryData(RegistrationNumber, year, AnalysisIDs)
		},

		enabled: input !== undefined && enabled,
		refetchOnMount: true,
		retry: 5,
	})
}
