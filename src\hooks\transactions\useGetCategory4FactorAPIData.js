//import { CATEGORY4_TRANSPORTATION_API_DATA } from "../../components/transaction/EditDetails/Scope3/Category4/CATEGORY4_DATA"

import { useQuery } from "@tanstack/react-query"
//import axios from "axios"

//const source = axios.CancelToken.source()

const getCategory4FactorAPIData = async () => {
	try {
		const response = await fetch("/.netlify/functions/getCategory4Data", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
		})

		const data = await response.json()

		return data // Ensure you return res.data or another relevant part of the response
	} catch (error) {
		throw new Error("Failed to fetch category 4 API data")
	} // Ensure you return res.data or another relevant part of the response
}

const useGetCategory4FactorAPIData = () => {
	return useQuery({
		queryKey: ["Category4FactorAPIData"],
		queryFn: getCategory4FactorAPIData,
	})
}

export default useGetCategory4FactorAPIData
