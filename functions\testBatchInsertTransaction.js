/* ------------------------ Populate data into trasaction using account Id ------------------------ */
const fillData = async (transaction) => {
	const extraData = await context.functions.execute("ClassifyTransaction", {
		transaction,
	})
	return extraData
}

let transactionAnalysis = []

// this function will check if analysis is already present in the analysisTransaction array or not

const isAnalysisPresent = (analysisId) => {
	for (let i = 0; i < transactionAnalysis.length; i++) {
		if (transactionAnalysis[i]?.AnalysisID === analysisId) {
			return true
		}
	}
	return false
}

const getDefaultSupplierFactor = (periodYear) => {
	DEFAULT_CO2_INTENSITY = {
		2020: 23.27,
		2021: 22.59,
		2022: 22.02,
		2023: 20.61,
		2024: 19.75,
		2025: 19.16,
	}

	return DEFAULT_CO2_INTENSITY[periodYear]
}

// this function will add analysis to the analysis transaction array of each line
// analysis is an array of objects which will contain analysisId, amount , analysisType
// we will add this analysis to the analysisTransaction with keys analysisId, analysisType
// and share where share will be amount of analysis amount / amount of transaction
// we will only add those analysis which are of type A or P

const addAnalysisToAnalysisTransaction = (analysis, amount) => {
	// we will iterate over each analysis and add it to the analysisTransaction array

	analysis.forEach((analysisData) => {
		if (analysisData?.AnalysisType === "A" || analysisData?.AnalysisType === "P") {
			if (!isAnalysisPresent(analysisData.AnalysisID)) {
				// we will add analysis to the analysisTransaction array
				transactionAnalysis.push({
					AnalysisID: analysisData?.AnalysisID,
					AnalysisType: analysisData?.AnalysisType,
					share: analysisData?.AnalysisAmount?.Amount / amount || 0,
				})
			}
		}
	})
}

/* ------------------------ Get Account and supplier data ------------------------ */

const transformTransaction = async (transaction, insertNewCopy, index) => {
	// Get the needed properties from transaction object. ClassifyTransaction
	const extraData = await fillData(transaction)
	// Set the finalAccountID from the fillData.getAccountId as AccountID
	// transaction.AccountID = extraData.AccountID

	alreadySlittedAnalysisIds = {}

	transaction.NaceCode = extraData?.NaceCode || transaction?.NaceCode || ""
	transaction.Status = extraData?.Status || transaction?.Status || 0
	transaction.DescriptionDetails = extraData?.DescriptionDetails || ""
	transaction.Scope = extraData?.Scope || 3
	transaction.Scope_3_Category = extraData?.Scope_3_Category || 1
	transaction.Notes = extraData?.Notes || ""
	transaction.Relation = extraData?.Relation || ""
	transaction.Type = extraData?.Type || ""

	//const TransactionID = transaction.TransactionID
	let Amount = transaction?.Amount || 0.0
	let AccountID = transaction?.AccountID || ""
	let SupplierID = transaction?.SupplierID || ""
	let Description = []
	let ReferenceNumber = transaction?.ReferenceNumber || ""
	let Status = transaction?.Status || 0
	let NaceCode = transaction?.NaceCode
	//let AccountIdInRange = transaction.AccountIdInRange
	let AccountDescription = ""
	let naceCode = ""
	let supplierName = ""
	let supplierFactor = 0

	if (transaction.Description instanceof Array) {
		Description = transaction.Description
	} else {
		Description.push(transaction.Description)
	}

	// We will use "found" flag to indicate whether we already found line with required account id or not.
	// This will help us detect transaction with two "lines" in given account id range.
	let found = false
	let descriptionAdded = false

	const year = transaction.PeriodYear

	let AnalysisOfFirstLine = transaction.Lines[0].Analysis

	let isSameAnalysis = true

	// Go over each line in the transaction. All lines are on every trans
	for (let i = 0; i < transaction.Lines.length; i++) {
		// Cast AccountID to Number to do the range check
		let AccountId = transaction.Lines[i].AccountID
		let AccountIdInRange = transaction.Lines[i].LineAccountIdInRange
		// Check if accountid is in required range or not.
		if (AccountIdInRange) {
			// We already set "AccountID" property in main "transaction" object earlier during splitting into multiple transaction parts.
			// So if Lines have different accountID than the "transaction" account id then continue.

			if (i > 0) {
				isSameAnalysis = compareAnalysis(AnalysisOfFirstLine, transaction.Lines[i].Analysis)
			}

			if (AccountId != transaction.AccountID || !isSameAnalysis) {
				continue
			}

			// Add analysis to the analysisTransaction array
			addAnalysisToAnalysisTransaction(transaction.Lines[i].Analysis, transaction.Lines[i].DebitAmount.Amount)

			// If first time this AccountId is seen.
			if (found == false) {
				found = true

				if (transaction.Lines[i].DebitAmount.Amount) {
					Amount += parseFloat(transaction.Lines[i].DebitAmount.Amount)
				}
				// substract the CreditAmount to the transaction to avoid double counting when periodizing expenses
				if (transaction.Lines[i].CreditAmount.Amount) {
					if (Number(AccountId) >= 1100 && Number(AccountId) < 1300) {
						// Account Id is in Depreciation range so, doesn't need to substract CreditAmount.
					} else {
						Amount -= parseFloat(transaction.Lines[i].CreditAmount.Amount)
					}
				}

				if (transaction.Lines[i].Description && insertNewCopy) {
					Description = [transaction.Lines[i].Description, ...Description]
					descriptionAdded = true
				}
			} else {
				// Edge case: when there are more than 2 element in lines array for which accountId is in given range.
				// console.error(`found edge case: TransactionID: ${TransactionID} transaction.Lines[${i}]`);
				// Add the debit amount to the transaction
				if (transaction.Lines[i].DebitAmount.Amount) {
					Amount += parseFloat(transaction.Lines[i].DebitAmount.Amount)
				}
				// substract the CreditAmount to the transaction to avoid double counting when periodizing expenses
				if (transaction.Lines[i].CreditAmount.Amount) {
					if (Number(AccountId) >= 1100 && Number(AccountId) < 1300) {
						// Account Id is in Depreciation range so, doesn't need to substract CreditAmount.
					} else {
						Amount -= parseFloat(transaction.Lines[i].CreditAmount.Amount)
					}
				}

				if (transaction.Lines[i].Description && insertNewCopy && !descriptionAdded) {
					Description = [transaction.Lines[i].Description, ...Description]
					descriptionAdded = true
				}
			}
		}

		// Check if another property like "Supplier id" is availabel or not.
		if (transaction.Lines[i].SupplierID && transaction.Lines[i].SupplierID !== "") {
			SupplierID = transaction.Lines[i].SupplierID
			supplierName = transaction.Lines[i].SupplierName
			supplierFactor =
				typeof transaction.Lines[i]?.supplierFactor === "object"
					? transaction.Lines[i]?.supplierFactor[year]
					: transaction.Lines[i]?.supplierFactor
			naceCode = transaction.Lines[i].NaceCode
		}

		if (transaction.Lines[i].Description) {
			Description = [...Description, transaction.Lines[i].Description]
		}

		if (AccountId === transaction.AccountID) {
			AccountDescription = transaction.Lines[i].AccountDescription
		}

		if (transaction.Lines[i].ReferenceNumber) {
			ReferenceNumber = transaction.Lines[i].ReferenceNumber
		}
	}

	transaction["Analysis"] = transactionAnalysis

	transactionAnalysis = []

	// Set the emissions to the right scope variable.
	let Scope_1 = 0.0
	let Scope_2 = 0.0
	let Scope_3 = 0.0
	let liter = 0.0
	let kwh = 0.0
	let non_renewables = 0.0
	let renewables = 0.0
	let mobile = 0.0
	let stationaryCombustion = 0.0
	let marketBased = null
	let locationBased = null
	let consumptionBased = null
	let nuclear = null

	// If the AccountID has a function then run that function
	if (extraData.function) {
		const emissionData = context.functions.execute("RunAccountDataFunction", {
			functionName: extraData.function,
			Amount: Amount,
			period: transaction.Period,
			year: transaction.PeriodYear,
			AccountID,
		})
		Scope_1 = emissionData.scope_1
		Scope_2 = emissionData.scope_2
		Scope_3 = emissionData.scope_3
		liter = emissionData.liter ? emissionData.liter : liter
		kwh = parseFloat(emissionData.kwh ? emissionData.kwh : kwh)
		marketBased = emissionData?.market_based ? parseFloat(emissionData?.market_based) : 0
		locationBased = emissionData?.location_based ? parseFloat(emissionData?.location_based) : 0
		consumptionBased = emissionData?.consumption_based ? parseFloat(emissionData?.consumption_based) : 0
		non_renewables = parseFloat(emissionData.non_renewables ? emissionData.non_renewables : non_renewables)
		renewables = parseFloat(emissionData.renewables ? emissionData.renewables : renewables)
		mobile = emissionData.mobile ? emissionData.mobile : 0.0
		stationaryCombustion = emissionData.stationaryCombustion ? emissionData.stationaryCombustion : 0.0
		nuclear = emissionData.nuclear ? emissionData.nuclear : 0.0
	} else {
		// If the AccountID has no function get EmissionData from the function
		let tco2e = 0.0
		if (supplierFactor !== 0) {
			tco2e = (Amount * supplierFactor) / 1000 / 1000
		} else {
			tco2e = (Amount * getDefaultSupplierFactor(year)) / 1000 / 1000
		}

		Status = 2

		switch (transaction.Scope) {
			case 1:
				Scope_1 = tco2e
				break
			case 2:
				Scope_2 = tco2e
				break
			case 3:
				Scope_3 = tco2e
				break
		}
	}

	if (insertNewCopy) {
		// If it is new transaction object then return an insertOne operation.
		delete transaction._id
		transaction.TransactionID = transaction.TransactionID + "." + index
		transaction.Amount = Math.floor(Number(Amount))
		transaction.AccountID = AccountID
		transaction.SupplierID = SupplierID
		transaction.Description = Description
		transaction.ReferenceNumber = ReferenceNumber
		transaction.Scope_1 = Scope_1
		transaction.Scope_2 = Scope_2
		transaction.Scope_3 = Scope_3
		transaction.liter = liter
		// What is going on here????
		transaction.kwh = kwh
		transaction.Status = Status
		transaction.supplierFactor = supplierFactor
		transaction.AccountDescription = AccountDescription
		transaction.NaceCode = NaceCode === "" ? naceCode : NaceCode
		transaction.SupplierName = supplierName
		transaction.renewable_energy = renewables
		transaction.non_renewable_energy = non_renewables
		transaction.stationaryCombustion = stationaryCombustion
		transaction.mobileCombustion = mobile
		transaction.IsSplittedTransaction = true
		transaction.isProcessed = true
		if (consumptionBased && marketBased && locationBased) {
			transaction.consumptionBased = consumptionBased
			transaction.locationBased = locationBased
			transaction.marketBased = marketBased
			transaction.nuclear = nuclear ? nuclear : 0.0
		}

		return {
			insertOne: {
				document: transaction
			}
		};
	} else {
		// Return an updateOne operation for the existing transaction object.
		const updateData = {
			Lines: transaction.Lines,
			Amount: Math.floor(Number(Amount)),
			AccountID: AccountID,
			SupplierID: SupplierID,
			Description: Description,
			ReferenceNumber: ReferenceNumber,
			Scope_1: Scope_1,
			Scope_2: Scope_2,
			Scope_3: Scope_3,
			liter: liter,
			kwh: kwh,
			supplierFactor,
			AccountDescription,
			NaceCode: NaceCode === "" ? naceCode : NaceCode,
			SupplierName: supplierName,
			renewable_energy: renewables,
			non_renewable_energy: non_renewables,
			stationaryCombustion,
			mobileCombustion: mobile,
			nuclear: nuclear ? nuclear : 0.0,
			Status: Status,
			DescriptionDetails: transaction.DescriptionDetails,
			Notes: transaction.Notes,
			Relation: transaction.Relation,
			Updated: true,
			isProcessed : true,
			Analysis: transaction.Analysis,
			Type: transaction.Type,
		}

		// These 2 ifs are to hedge for nulls. Is Scope missing from the transaction?
		if (transaction.Scope != "") {
			updateData["Scope"] = extraData.Scope === 0 ? 0 : transaction.Scope
		}
		if (transaction.Scope_3_Category != "") {
			updateData.Scope_3_Category = transaction.Scope_3_Category
		}
		if (consumptionBased && marketBased && locationBased) {
			updateData.consumptionBased = consumptionBased
			updateData.locationBased = locationBased
			updateData.marketBased = marketBased
			//updateData.nuclear = nuclear
		}

		return {
			updateOne: {
				filter: { _id: transaction._id },
				update: { $set: updateData }
			}
		};
	}
}

// function to compare analysis which will be array of objects of two lines of transaction every analysis will contain
// analysisId, amount, analysisType and we need to compare each and every property if they are
// same we will return true else false

let alreadySlittedAnalysisIds = {}

const compareAnalysis = (analysis1, analysis2) => {
	// here analysis1 is the analysis of the first line of transaction
	// and analysis2 is the analysis of the second line of transaction

	if (analysis1.length != analysis2.length) {
		return false
	}

	// sort analysis based on analysisId so that we can compare each and every analysis

	analysis1.sort((a, b) => a.AnalysisID - b.AnalysisID)

	analysis2.sort((a, b) => a.AnalysisID - b.AnalysisID)

	// we will compare each and every property of analysis

	for (let i = 0; i < analysis1.length; i++) {
		if (analysis1[i]?.AnalysisType !== "A" && analysis1[i]?.AnalysisType !== "P") {
			continue
		}

		if (
			analysis1[i]?.AnalysisID != analysis2[i]?.AnalysisID ||
			analysis1[i]?.AnalysisType != analysis2[i]?.AnalysisType
		) {
			alreadySlittedAnalysisIds[analysis2[i]?.AnalysisID] = analysis2[i]?.AnalysisType

			if (analysis1[i]?.AnalysisID in alreadySlittedAnalysisIds) {
				return true
			}

			return false
		}
	}

	return true
}

exports = async function (input) {
	// To clear companies or transaction data from mongodb collection. only uncomment this if you want to clear some data.

	// MongoDB provides newly inserted transaction in "changeEvent" object.
	let { transactions } = input
	// If the transaction has been here before there is no need to transaform so just return

    // const request = context.services
	// 		.get("mongodb-atlas")
	// 		.db(context.environment.values.database)
	// 		.collection("transaction")


	let operations =[]

	for (const transaction of transactions) {
		let newTransactions = []
		let AccountIDAlreadyPresent = {}
		alreadySlittedAnalysisIds={}

		// For each line in the trans we will
		for (let i = 0; i < transaction.Lines.length; i++) {
			let AccountID = transaction.Lines[i].AccountID
			let StandardAccountID = transaction.Lines[i].StandardAccountID
			let AccountIdInRange = transaction.Lines[i].LineAccountIdInRange
			// If the transaction line is in Range
			if (AccountIdInRange == true) {
				const ind = newTransactions.length

				if (!(AccountID in AccountIDAlreadyPresent)) {
					// AccountID for the first time so create new transaction

					const Analysis = transaction.Lines[i].Analysis

					AccountIDAlreadyPresent[AccountID] = Analysis

					// and push it onto the array.
					newTransactions.push({ ...transaction })

					// Set the AccountID and Standard AccountId for this transaction
					newTransactions[ind].AccountID = AccountID
					newTransactions[ind].StandardAccountID = StandardAccountID

					newTransactions[ind].Lines = []

					// push the line which causes the transaction to split into multiple transaction at the start of the array
					// so that when we will calculate transaction amount we take first line analysis and compare rest of them with this

					newTransactions[ind].Lines.push({ ...transaction.Lines[i] })

					// Put all the lines array content in a new transaction object.

					for (let j = 0; j < transaction.Lines.length; j++) {
						if (j != i) {
							newTransactions[ind].Lines.push({ ...transaction.Lines[j] })
						}
					}
				} else {
					// we have incounter the same AccountID again so we will check if analysis is same
					// or not if it is not same then we will split the transaction into multiple transaction

					const Analysis = transaction.Lines[i].Analysis

					const analysisToCompare = AccountIDAlreadyPresent[AccountID]

					if (!compareAnalysis(Analysis, analysisToCompare)) {
						// if analysis is not same then we will create new transaction
						// and push it onto the array.

						AccountIDAlreadyPresent[AccountID] = Analysis

						newTransactions.push({ ...transaction })

						// Set the AccountID and Standard AccountId for this transaction

						newTransactions[ind].AccountID = AccountID
						newTransactions[ind].StandardAccountID = StandardAccountID

						newTransactions[ind].Lines = []

						// push line from the original transaction is splited

						newTransactions[ind].Lines.push({ ...transaction.Lines[i] })

						// Put all the lines array content in a new transaction object except the current line

						for (let j = 0; j < transaction.Lines.length; j++) {
							if (j != i) {
								newTransactions[ind].Lines.push({ ...transaction.Lines[j] })
							}
						}
					}
				}
			} else {
				// AccountID not in range - next line please
				transaction.Lines[i].AccountID = String(AccountID)
			}
			// Can we provide some information to a transaction out of range?
		}
		// For each transaction in the newTransactions array with the original and possible split ones

		// Process transactions in the newTransactions array in parallel
		const transactionPromises = newTransactions.map(async (transaction, i) => {
			const insertNewCopy = i >= 1 ? true : false;
			return transformTransaction(transaction, insertNewCopy, i);
		});

		const batchOperations = await Promise.all(transactionPromises);
		operations.push(...batchOperations);
	}

	return operations

	// A trans might have multiple lines with the same accountID, so then we will
	// split the transaction into multiple transaction, according to accountID

	// return newTransactions
}
