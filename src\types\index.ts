// Core application types

// User types
export interface User {
	id: string
	email: string
	firstName: string
	lastName: string
	organizationId?: string
	role: UserRole
	preferences: UserPreferences
	createdAt: string
	updatedAt: string
}

export type UserRole = "admin" | "manager" | "user" | "viewer"

export interface UserPreferences {
	language: string
	theme: "light" | "dark" | "auto"
	dateFormat: string
	numberFormat: string
	notifications: NotificationSettings
}

export interface NotificationSettings {
	email: boolean
	push: boolean
	inApp: boolean
	frequency: "immediate" | "daily" | "weekly"
}

// Organization types
export interface Organization {
	id: string
	name: string
	industry: string
	size: OrganizationSize
	settings: OrganizationSettings
	createdAt: string
	updatedAt: string
}

export type OrganizationSize = "small" | "medium" | "large" | "enterprise"

export interface OrganizationSettings {
	currency: string
	timezone: string
	fiscalYearStart: string
	emissionFactors: EmissionFactors
}

// Transaction types
export interface Transaction {
	id: string
	date: string
	amount: number
	currency: string
	category: string
	subcategory?: string
	scope: EmissionScope
	supplier?: Supplier
	description?: string
	emissions: EmissionData
	metadata: TransactionMetadata
	organizationId: string
	userId: string
	createdAt: string
	updatedAt: string
}

export type EmissionScope = 1 | 2 | 3

export interface EmissionData {
	co2: number
	ch4: number
	n2o: number
	total: number
	unit: "kg" | "tonnes"
}

export interface TransactionMetadata {
	source: "manual" | "import" | "api"
	confidence: number
	verified: boolean
	tags: string[]
	attachments: string[]
}

// Supplier types
export interface Supplier {
	id: string
	name: string
	category: string
	location: Location
	emissionFactor: number
	verified: boolean
	createdAt: string
	updatedAt: string
}

export interface Location {
	country: string
	region?: string
	city?: string
	coordinates?: {
		lat: number
		lng: number
	}
}

// Emission factors
export interface EmissionFactors {
	[category: string]: {
		[subcategory: string]: {
			co2: number
			ch4: number
			n2o: number
			unit: string
			source: string
			lastUpdated: string
		}
	}
}

// API types
export interface ApiResponse<T = unknown> {
	data: T
	message?: string
	success: boolean
	timestamp: string
}

export interface ApiError {
	message: string
	code: string
	status: number
	details?: Record<string, unknown>
}

export interface PaginationParams {
	page: number
	limit: number
	sortBy?: string
	sortOrder?: "asc" | "desc"
}

export interface PaginatedResponse<T> {
	data: T[]
	pagination: {
		page: number
		limit: number
		total: number
		totalPages: number
		hasNext: boolean
		hasPrev: boolean
	}
}

// Form types
export interface FormField {
	name: string
	label: string
	type: "text" | "email" | "password" | "number" | "select" | "textarea" | "file" | "date"
	required: boolean
	validation?: ValidationRule[]
	options?: SelectOption[]
	placeholder?: string
	helpText?: string
}

export interface SelectOption {
	value: string | number
	label: string
	disabled?: boolean
}

export interface ValidationRule {
	type: "required" | "email" | "minLength" | "maxLength" | "pattern" | "custom"
	value?: unknown
	message: string
}

export interface FormError {
	field: string
	message: string
}

// UI types
export interface LoadingState {
	isLoading: boolean
	error?: string | null
	lastUpdated?: string
}

export interface TableColumn<T = any> {
	key: keyof T
	label: string
	sortable?: boolean
	width?: string
	render?: (value: unknown, row: T) => React.ReactNode
}

export interface FilterOption {
	key: string
	label: string
	type: "select" | "date" | "range" | "search"
	options?: SelectOption[]
	defaultValue?: unknown
}

// Chart types
export interface ChartData {
	labels: string[]
	datasets: ChartDataset[]
}

export interface ChartDataset {
	label: string
	data: number[]
	backgroundColor?: string | string[]
	borderColor?: string | string[]
	borderWidth?: number
}

// Dashboard types
export interface DashboardMetric {
	id: string
	title: string
	value: number | string
	unit?: string
	change?: {
		value: number
		type: "increase" | "decrease"
		period: string
	}
	trend?: number[]
}

export interface DashboardWidget {
	id: string
	type: "metric" | "chart" | "table" | "custom"
	title: string
	size: "small" | "medium" | "large"
	data: unknown
	config?: Record<string, unknown>
}

// File upload types
export interface FileUpload {
	id: string
	name: string
	size: number
	type: string
	url?: string
	status: "pending" | "uploading" | "completed" | "error"
	progress?: number
	error?: string
}

// Notification types
export interface Notification {
	id: string
	type: "info" | "success" | "warning" | "error"
	title: string
	message: string
	timestamp: string
	read: boolean
	actions?: NotificationAction[]
}

export interface NotificationAction {
	label: string
	action: () => void
	type?: "primary" | "secondary"
}

// Search types
export interface SearchResult<T = any> {
	item: T
	score: number
	highlights: string[]
}

export interface SearchFilters {
	[key: string]: unknown
}

// Export utility types
export type DeepPartial<T> = {
	[P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type Nullable<T> = T | null

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
