/* eslint-disable import/no-unresolved */
import React, { useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"
import Select from "react-select"

import { FACTORS_DATA_COMBUSTION, METRIC_LIST_COMBUSTION } from "./constants/combustion"

import Button from "@/components/ui/Button"
import Input from "@/components/ui/Input"
import { useToast } from "@/hooks"

function VolumeContent(props) {
	const { volumeRows, updateVolumeScope } = props
	const unitInputRef = useRef()
	const { t } = useTranslation("transactions")
	const toast = useToast()

	const defaultMetricName = "PetrolForecourt"
	const defaultMetric = FACTORS_DATA_COMBUSTION[defaultMetricName]

	const [matric, setMatric] = useState({
		name: "Petrol bio-blend",
		value: defaultMetricName,
		label: "Petrol bio-blend",
	})
	const [label, setLabel] = useState("Petrol bio-blend")
	const [factor, setFactor] = useState(
		Number(defaultMetric.scope1 + defaultMetric.scope3).toFixed(2)
	)
	const [unit, setUnit] = useState(0.0)
	const [unitType, setUnitType] = useState(defaultMetric.unit)

	const getFactor = matric => {
		const data =
			FACTORS_DATA_COMBUSTION[matric.value].scope1 + FACTORS_DATA_COMBUSTION[matric.value].scope3
		return data
	}

	const metricChange = event => {
		setMatric(event)
		setLabel(event.label)
		setFactor(Number(getFactor(event)).toFixed(2))
		setUnitType(FACTORS_DATA_COMBUSTION[event.value].unit)

		unitInputRef.current.focus()
	}

	const addVolumeRow = (scope1, scope3, kwh, mobileCombustion) => {
		let newVolumeRow = {
			matric: label,
			factor: Number(factor),
			unit: Number(unit),
			unitType,
			scope1: Number(Number(scope1).toFixed(2)),
			scope2: 0,
			scope3: Number(Number(scope3).toFixed(2)),
			kwh,
			mobileCombustion: Number(mobileCombustion),
		}

		const newVolumeRows = [...volumeRows, newVolumeRow]

		updateVolumeScope(newVolumeRow, false, newVolumeRows)
	}

	const calculateVehicleVolumeEmissions = () => {
		if (parseFloat(factor) <= 0 && parseFloat(unit) <= 0) {
			toast("error", t("factor_unit_greater_0_error"))
			return
		}
		const metricValue = matric.value
		const factorData = FACTORS_DATA_COMBUSTION[metricValue]
		let scope1 = factor * unit
		let scope3 = factorData.scope3 * unit
		let kwh = factorData.kwh ? factorData.kwh : 10
		let mobileCombustion = kwh * unit

		addVolumeRow(scope1 - scope3, scope3, kwh, mobileCombustion.toFixed(2))
	}

	const deleteVolumeRow = index => {
		let volumeRow = volumeRows[index]
		let newVolumeRows = [...volumeRows]
		newVolumeRows.splice(index, 1)

		updateVolumeScope(volumeRow, true, newVolumeRows)
	}

	const handleFocus = event => event.target.select()

	return (
		<>
			{volumeRows.map((row, index) => (
				<div key={index}>
					<div className="grid grid-cols-5 gap-10 saved-emission my-1">
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("Metric")}
							</label>
							<div>{row.matric}</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("Factor")}
							</label>
							<div className="flex relative">
								<span>{row.factor}</span>
								<span className="custom-span-unit-value-save">{t("Kg-Co2e")}</span>
							</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">{t("Units")}</label>
							<div className="flex relative">
								<span>{row.unit}</span>
								<span className="text-nowrap custom-span-unit-value-save">{row.unitType}</span>
							</div>
						</div>
						<div>
							<label className="text-slate-800 font-semibold whitespace-nowrap">
								{t("TotalCo2e")}
							</label>
							<div className="flex relative">
								<span>{Number(row.scope1 + row.scope3).toFixed(2)}</span>
								<span className="text-nowrap custom-span-unit-value-save">{t("kg")}</span>
							</div>
						</div>

						<div className="self-center delete-icon">
							<span aria-hidden onClick={() => deleteVolumeRow(index)}>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}

			<div className="grid grid-cols-4 gap-10 my-2">
				<div>
					<label className="text-slate-800 font-semibold whitespace-nowrap" htmlFor={"metric-row-"}>
						{t("Metric")}
					</label>
					<Select
						id={"metric-row-"}
						value={matric || null}
						onChange={metricChange}
						options={METRIC_LIST_COMBUSTION}
						maxMenuHeight={150}
					/>
				</div>

				<div>
					<Input
						label={t("Factor")}
						type="number"
						labelColor="text-sky-500"
						value={factor}
						handleChange={e => setFactor(e.target.value)}
						handleFocus={handleFocus}
						unit={t("Kg-Co2e")}
					/>
				</div>
				<div>
					<Input
						label={t("Units")}
						type="number"
						labelColor="text-sky-500"
						value={unit}
						ref={unitInputRef}
						handleChange={e => setUnit(e.target.value)}
						handleFocus={handleFocus}
						unit={unitType}
					/>
				</div>
				<div className="self-end mb-1">
					<Button
						title={`${t("Add")} | +`}
						handleClick={() => calculateVehicleVolumeEmissions()}
						color="sky-500"
					/>
				</div>
			</div>
		</>
	)
}

export default VolumeContent
