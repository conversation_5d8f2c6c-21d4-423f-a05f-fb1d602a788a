/**
 * Cache Manager Tests
 * Comprehensive tests for request deduplication and caching functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from "vitest"
import { cacheManager, CACHE_TAGS } from "../services/cache-manager"
import { requestDeduplicator } from "../services/query-cache-integration"
import { cacheWarmingService } from "../services/cache-warming"

describe("CacheManager", () => {
	beforeEach(() => {
		cacheManager.clear()
		vi.clearAllTimers()
		vi.useFakeTimers()
	})

	afterEach(() => {
		vi.useRealTimers()
	})

	describe("Basic Cache Operations", () => {
		it("should store and retrieve data", async () => {
			const testData = { id: 1, name: "test" }
			cacheManager.set("test-key", testData)

			const result = await cacheManager.get("test-key")
			expect(result).toEqual(testData)
		})

		it("should return null for non-existent keys", async () => {
			const result = await cacheManager.get("non-existent")
			expect(result).toBeNull()
		})

		it("should handle TTL expiration", async () => {
			const testData = { id: 1, name: "test" }
			cacheManager.set("test-key", testData, {
				key: "test-key",
				ttl: 1000,
				tags: [],
				staleWhileRevalidate: false,
				maxAge: 2000,
			})

			// Data should be fresh initially
			let result = await cacheManager.get("test-key")
			expect(result).toEqual(testData)

			// Advance time beyond TTL
			vi.advanceTimersByTime(1500)

			// Data should be stale/expired
			result = await cacheManager.get("test-key")
			expect(result).toBeNull()
		})

		it("should handle stale-while-revalidate", async () => {
			const originalData = { id: 1, name: "original" }
			const newData = { id: 1, name: "updated" }

			let fetchCount = 0
			const mockFetcher = vi.fn(() => {
				fetchCount++
				return Promise.resolve(fetchCount === 1 ? originalData : newData)
			})

			// Initial fetch
			const result1 = await cacheManager.get("test-key", mockFetcher, {
				key: "test-key",
				ttl: 1000,
				staleWhileRevalidate: true,
				tags: [],
				maxAge: 2000,
			})
			expect(result1).toEqual(originalData)
			expect(mockFetcher).toHaveBeenCalledTimes(1)

			// Advance time to make data stale
			vi.advanceTimersByTime(1500)

			// Should return stale data immediately and revalidate in background
			const result2 = await cacheManager.get("test-key", mockFetcher, {
				key: "test-key",
				ttl: 1000,
				staleWhileRevalidate: true,
				tags: [],
				maxAge: 2000,
			})
			expect(result2).toEqual(originalData) // Still returns stale data
		})
	})

	describe("Request Deduplication", () => {
		it("should deduplicate concurrent identical requests", async () => {
			let fetchCount = 0
			const mockFetcher = vi.fn(() => {
				fetchCount++
				return new Promise(resolve => {
					setTimeout(() => resolve({ id: fetchCount, data: "test" }), 100)
				})
			})

			// Make multiple concurrent requests
			const promises = [
				cacheManager.get("test-key", mockFetcher),
				cacheManager.get("test-key", mockFetcher),
				cacheManager.get("test-key", mockFetcher),
			]

			vi.advanceTimersByTime(100)
			const results = await Promise.all(promises)

			// Should only fetch once
			expect(mockFetcher).toHaveBeenCalledTimes(1)

			// All results should be identical
			expect(results[0]).toEqual(results[1])
			expect(results[1]).toEqual(results[2])
			expect(results[0]).toEqual({ id: 1, data: "test" })
		})

		it("should handle request deduplication with requestDeduplicator", async () => {
			let fetchCount = 0
			const mockFetcher = vi.fn(() => {
				fetchCount++
				return Promise.resolve({ id: fetchCount, data: "test" })
			})

			// Make concurrent requests through deduplicator
			const promises = [
				requestDeduplicator.deduplicate("test-key", mockFetcher),
				requestDeduplicator.deduplicate("test-key", mockFetcher),
				requestDeduplicator.deduplicate("test-key", mockFetcher),
			]

			const results = await Promise.all(promises)

			// Should only fetch once
			expect(mockFetcher).toHaveBeenCalledTimes(1)

			// All results should be identical
			expect(results[0]).toEqual(results[1])
			expect(results[1]).toEqual(results[2])
		})
	})

	describe("Intelligent Cache Invalidation", () => {
		it("should invalidate cache entries by tags", async () => {
			// Set up test data with different tags
			cacheManager.set(
				"transactions-1",
				{ id: 1 },
				{
					key: "transactions-1",
					tags: [CACHE_TAGS.TRANSACTIONS],
					ttl: 5000,
					staleWhileRevalidate: true,
					maxAge: 10000,
				}
			)

			cacheManager.set(
				"suppliers-1",
				{ id: 2 },
				{
					key: "suppliers-1",
					tags: [CACHE_TAGS.SUPPLIERS],
					ttl: 5000,
					staleWhileRevalidate: true,
					maxAge: 10000,
				}
			)

			cacheManager.set(
				"mixed-1",
				{ id: 3 },
				{
					key: "mixed-1",
					tags: [CACHE_TAGS.TRANSACTIONS, CACHE_TAGS.DASHBOARD],
					ttl: 5000,
					staleWhileRevalidate: true,
					maxAge: 10000,
				}
			)

			// Verify data exists
			await expect(cacheManager.get("transactions-1")).resolves.toEqual({ id: 1 })
			await expect(cacheManager.get("suppliers-1")).resolves.toEqual({ id: 2 })
			await expect(cacheManager.get("mixed-1")).resolves.toEqual({ id: 3 })

			// Invalidate by transactions tag
			cacheManager.invalidateByTags([CACHE_TAGS.TRANSACTIONS])

			// Transactions and mixed should be invalidated, suppliers should remain
			await expect(cacheManager.get("transactions-1")).resolves.toBeNull()
			await expect(cacheManager.get("mixed-1")).resolves.toBeNull()
			await expect(cacheManager.get("suppliers-1")).resolves.toEqual({ id: 2 })
		})

		it("should invalidate specific cache entries", async () => {
			cacheManager.set("test-key-1", { id: 1 })
			cacheManager.set("test-key-2", { id: 2 })

			// Verify both exist
			expect(await cacheManager.get("test-key-1")).toEqual({ id: 1 })
			expect(await cacheManager.get("test-key-2")).toEqual({ id: 2 })

			// Invalidate specific key
			cacheManager.invalidate("test-key-1")

			// Only test-key-1 should be invalidated
			expect(await cacheManager.get("test-key-1")).toBeNull()
			expect(await cacheManager.get("test-key-2")).toEqual({ id: 2 })
		})
	})

	describe("Cache Size Management", () => {
		it("should evict least used entries when cache is full", () => {
			// Create a small cache for testing
			const testCache = new (cacheManager.constructor as any)(3) // Max 3 entries

			// Fill cache
			testCache.set("key1", { id: 1 }, { priority: "low" })
			testCache.set("key2", { id: 2 }, { priority: "medium" })
			testCache.set("key3", { id: 3 }, { priority: "high" })

			// Access key2 multiple times to increase its access count
			testCache.get("key2")
			testCache.get("key2")
			testCache.get("key2")

			// Add another entry, should evict key1 (lowest priority, least accessed)
			testCache.set("key4", { id: 4 }, { priority: "medium" })

			const stats = testCache.getStats()
			expect(stats.totalEntries).toBeLessThanOrEqual(3)
		})

		it("should track cache statistics correctly", async () => {
			// Clear cache and reset stats
			cacheManager.clear()

			// Perform cache operations
			await cacheManager.get("miss-key") // Miss
			cacheManager.set("hit-key", { id: 1 })
			await cacheManager.get("hit-key") // Hit

			const stats = cacheManager.getStats()
			expect(stats.totalEntries).toBe(1)
			expect(stats.hitRate).toBeGreaterThan(0)
			expect(stats.missRate).toBeGreaterThan(0)
		})
	})

	describe("Cache Warming", () => {
		it("should warm cache with multiple strategies", async () => {
			const mockApp = {
				getTransactions: vi.fn().mockResolvedValue([{ id: 1 }]),
				getSuppliers: vi.fn().mockResolvedValue([{ id: 2 }]),
				getYearlyChartData: vi.fn().mockResolvedValue({ data: "chart" }),
			}

			const mockOrganization = {
				id: "org-1",
				RegistrationNumber: "REG123",
			}

			const stats = await cacheWarmingService.warmCriticalData(mockApp, mockOrganization)

			expect(stats.totalStrategies).toBeGreaterThan(0)
			expect(stats.successCount).toBeGreaterThan(0)
			expect(mockApp.getYearlyChartData).toHaveBeenCalled()
			expect(mockApp.getSuppliers).toHaveBeenCalled()
		})

		it("should handle warming failures gracefully", async () => {
			const mockApp = {
				getTransactions: vi.fn().mockRejectedValue(new Error("API Error")),
				getSuppliers: vi.fn().mockRejectedValue(new Error("Suppliers API Error")),
				getYearlyChartData: vi.fn().mockRejectedValue(new Error("Chart API Error")),
			}

			const mockOrganization = {
				id: "org-1",
				RegistrationNumber: "REG123",
			}

			const stats = await cacheWarmingService.warmCriticalData(mockApp, mockOrganization)

			expect(stats.totalStrategies).toBeGreaterThan(0)
			// At least one should succeed (user profile doesn't use app methods)
			expect(stats.successCount).toBeGreaterThanOrEqual(1)
			// Some may fail due to mocked errors
			expect(stats.failureCount).toBeGreaterThanOrEqual(0)
		})
	})

	describe("Cache Cleanup", () => {
		it("should clean up expired entries", async () => {
			// Set entry with short TTL
			cacheManager.set(
				"short-lived",
				{ id: 1 },
				{
					key: "short-lived",
					ttl: 1000,
					tags: [],
					staleWhileRevalidate: false,
					maxAge: 1000,
				}
			)

			// Set entry with long TTL
			cacheManager.set(
				"long-lived",
				{ id: 2 },
				{
					key: "long-lived",
					ttl: 10000,
					tags: [],
					staleWhileRevalidate: false,
					maxAge: 10000,
				}
			)

			// Advance time beyond short TTL but not long TTL
			vi.advanceTimersByTime(2000)

			// Short-lived should return null when expired (no fetcher provided)
			expect(await cacheManager.get("short-lived")).toBeNull()
			// Long-lived should still be available
			expect(await cacheManager.get("long-lived")).toEqual({ id: 2 })
		})
	})
})
