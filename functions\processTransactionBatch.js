exports = async function (batchId) {
	const collection = context.services
		.get("mongodb-atlas")
		.db(context.environment.values.database)
		.collection("transaction")

	const pageSize = 100

	const query = {
		ImportID: batchId,
		isProcessed: { $exists: false },
	}

	const batch = await collection.find(query).sort({ _id: 1 }).limit(pageSize).toArray()

	if (batch.length === 0) {
		return { status: "done", batchId }
	}

	const bulkOperations = await context.functions.execute("testBatchInsertTransaction", { transactions: batch })

	if (bulkOperations?.length) {
		await collection.bulkWrite(bulkOperations)
	}

	// Trigger another batch
	context.functions.execute("processTransactionBatch", batchId);

	return { processed: batch.length }
}
