import { DropDownListComponent } from "@syncfusion/ej2-react-dropdowns"
import { DialogComponent } from "@syncfusion/ej2-react-popups"
import React, { useState, useEffect } from "react"
import Select from "react-select"
import AsyncSelect from "react-select/async"

import blue_flag from "../../../assets/blue_flag.png"
import green_flag from "../../../assets/green_flag.png"
import lightGreen_flag from "../../../assets/lightGreen_flag.png"
import orange_flag from "../../../assets/orange_flag.svg"
import red_flag from "../../../assets/red_flag.png"
import Pop from "../../../components/PopUp.component"
import Input from "../../../components/ui/Input"
import TextArea from "../../../components/ui/TextArea"
// import { RiDeleteBin6Line } from "react-icons/ri"

export default function UpdateMultipleTransactions(props) {
	const [state, setState] = useState(initializeState(props))

	const editorData = [
		{ text: "blue flag", value: 0 },
		{ text: "green flag", value: 1 },
		{ text: "orange flag", value: 2 },
		{ text: "red flag", value: 3 },
		{ text: "lightgreen flag", value: 4 },
	]
	const dropDownFields = { text: "text", value: "value" }
	const valueToFlag = {
		0: blue_flag,
		1: green_flag,
		2: orange_flag,
		3: red_flag,
		4: lightGreen_flag,
	}
	const displayNameForFlag = {
		0: "Info",
		1: "Activity data",
		2: "Processed",
		3: "Warning",
		4: "Checked",
	}

	const flagItemTemplate = props => {
		return (
			<div className="flex items-center">
				<img
					src={valueToFlag[props.value]}
					alt={props.value}
					style={{ marginTop: "0px", height: "25px" }}
				/>
				{displayNameForFlag[props.value]}
			</div>
		)
	}
	const flagValueTemplate = props => {
		return (
			<div className="flex items-center">
				<img
					src={valueToFlag[props.value]}
					alt={props.value}
					style={{ marginTop: "0px", height: "25px" }}
				/>
				{displayNameForFlag[props.value]}
			</div>
		)
	}

	const buttons = [
		{
			buttonModel: {
				content: props.t("Save"),
				cssClass: "e-flat",
				isPrimary: true,
			},
			click: () => {
				let s = { ...state }
				props.setShowMultiTransactionsEditDialog(false)
				props.handleUpdateMultipleTransactions(s)
			},
		},
		{
			buttonModel: {
				content: props.t("Cancel"),
				cssClass: "e-flat",
			},
			click: () => {
				props.setShowMultiTransactionsEditDialog(false)
			},
		},
	]

	useEffect(() => {
		//if (props.data !== state.data) {
		setState(initializeState(props))
		//}
	}, [props])

	const handleUpdateState = updatedValues => {
		setState(prevState => {
			return { ...prevState, ...updatedValues }
		})
	}

	/**
	 * Function to handle the change event for NACE codes.
	 *
	 * @param {Object} event - the event object containing data and value properties
	 */

	const naceChange = event => {
		if (event && event.data && event.value) {
			if (event.data && event.data.code) {
				let data = {
					NaceCode: event.data.code,
					Scope_1: 0,
					Scope_2: 0,
					Scope_3: 0,
					industry: event.data.name,
					industryFactor: event.data.co2Intensity[props.reportingYear],
					naceDescription: event.data.naceNotes,
					naceShortName: event.data.shortName,
				}
				handleUpdateState(data)
			} else {
				handleUpdateState({ NaceCode: "", industry: "", industryFactor: "" })
			}
			let { scope, scope1, scope2, scope3, Amount } = state

			let co2Factor = event.data.co2Intensity[props.reportingYear]

			if (scope === 1) {
				scope1 = (co2Factor * Amount) / 1000
			}
			if (scope === 2) {
				scope2 = (co2Factor * Amount) / 1000
			}
			if (scope === 3) {
				scope3 = (co2Factor * Amount) / 1000
			}
			handleUpdateState({ scope1, scope2, scope3, isNaceCode: true, isCustomFactor: false })
		}
	}

	const naceInputChange = inputValue => {
		return new Promise(resolve => {
			if (state.naceData && state.naceData.length) {
				let naceData = []
				state.naceData.forEach(nace => {
					if (nace.code.includes(inputValue) || nace.name.includes(inputValue)) {
						naceData.push({ label: `(${nace?.code}) ${nace?.name}`, value: nace.code, data: nace })
					}
				})
				resolve(naceData)
			} else {
				resolve([])
			}
		})
	}

	const selectedRecalculationChange = value => {
		if (value === "NaceCode-Industry") {
			let { Scope, Scope_1C, Scope_2C, Scope_3C, Amount, industryFactor } = state
			industryFactor = industryFactor ? industryFactor : 0
			if (Scope === 1) {
				Scope_1C = (industryFactor * Amount) / 1000
			}
			if (Scope === 2) {
				Scope_2C = (industryFactor * Amount) / 1000
			}
			if (Scope === 3) {
				Scope_3C = (industryFactor * Amount) / 1000
			}
			handleUpdateState({ Scope_1: Scope_1C, Scope_2: Scope_2C, Scope_3: Scope_3C })
			//setState({ Scope_1: Scope_1C, Scope_2: Scope_2C, Scope_3: Scope_3C })
		}
		handleUpdateState({ selectedRecalculation: value })
	}

	const handleChangeScope = event => {
		handleUpdateState({ Scope_3_Category: event.id, defaultScope3Catagory: event })
	}
	const handleChangeCustomEmmisionFactor = value => {
		let { scope, Amount, scope1, scope2, scope3 } = state
		let calculated = (Number(value) * Amount) / 1000

		if (scope === 1) {
			scope1 = calculated
		} else if (scope === 2) {
			scope2 = calculated
		} else if (scope === 3) {
			scope3 = calculated
		}

		handleUpdateState({
			scope1,
			scope2,
			scope3,
			customEmmisonFactor: value,
			NaceCode: `custom factor ${value}`,
			isNaceCode: true,
			isCustomFactor: true,
			changedScopeValue: calculated,
		})
	}

	const { t } = props
	//const {data,selectedRecalculation,customEmmisonFactor} = state
	if (!props.showMultiTransactionsEditDialog) {
		return <></>
	}
	return (
		<div
			id="dialog-target-flag"
			style={{
				position: "absolute",
				top: 0,
				height: "100vh",
				width: "100%",
			}}
		>
			<DialogComponent
				width="44rem"
				isModal={true}
				allowDragging={true}
				enableResize={true}
				target="#dialog-target-flag"
				close={() => props.setShowMultiTransactionsEditDialog(false)}
				header={t("multiple_transactions_heading")}
				visible={props.showMultiTransactionsEditDialog}
				showCloseIcon={true}
				buttons={buttons}
			>
				<div id="#dialog-target-flag">
					<div className="mb-5">
						<Input
							className="w-full"
							label={t("TransactionID")}
							id="TransactionID"
							name="TransactionID"
							type="text"
							disable={true}
							value={state?.transactionIds}
						/>
					</div>

					<div
						className="grid mb-2 bg-amber-100 border-l-4 rounded-md border-amber-500 p-3"
						role="alert"
					>
						<div className="text-slate-800">{t("MultipleEdit")}</div>
					</div>
					<div className="my-3">
						<h5 className="text-slate-800 font-bold">{t("SelectEmission")}</h5>
					</div>

					<div className="grid grid-cols-2 gap-4">
						<div className="flex items-center">
							<input
								type="radio"
								value="1"
								name="recalculation-type"
								checked={state.selectedRecalculation === "CustomEmissionFactor"}
								onChange={() => selectedRecalculationChange("CustomEmissionFactor")}
							/>
							<span className="ml-3 mt-2 radio-label">{t("CustomEmissionFactor")}</span>
						</div>

						<div className="flex items-center">
							<Input
								id="customSuplier"
								name="customSuplier"
								type="number"
								disable={state.selectedRecalculation !== "CustomEmissionFactor"}
								value={state.customEmmisonFactor}
								handleChange={e => handleChangeCustomEmmisionFactor(e.target.value)}
								unit={t("KGPer1000NOK")}
							/>
						</div>

						<div className="flex items-center">
							<input
								type="radio"
								value="1"
								name="isNaceCode"
								checked={state.selectedRecalculation === "NaceCode-Industry"}
								onChange={() => selectedRecalculationChange("NaceCode-Industry")}
							/>
							<span className="ml-3 mt-2 radio-label">{t("NaceCode-Industry")}</span>
						</div>
						<div className="w-full">
							<div className="e-control-wrapper">
								<AsyncSelect
									id="industry-nace-list"
									isDisabled={state.selectedRecalculation !== "NaceCode-Industry"}
									placeholder={t("SearchByIndustryOrNaceCode")}
									onChange={e => naceChange(e)}
									loadOptions={e => naceInputChange(e)}
									//styles={customStyles}
								/>
							</div>
						</div>
					</div>

					{state.isNaceCode && (
						<div
							className="flex flex-row gap-4 rounded-md border-l-4 border-sky-400 bg-sky-100 p-3 text-sm text-slate-800 mt-3"
							role="alert"
						>
							<div className="min-w-fit">
								<h6 className="font-bold text-slate-800">Nace code</h6>
								<input
									id="NaceCode"
									name="NaceCode"
									className={
										state?.isCustomFactor
											? "border-0 bg-sky-100 text-base"
											: "w-20 border-0 bg-sky-100 text-base"
									}
									value={state.NaceCode}
									disabled
								/>
							</div>
							<div className="grow">
								<h6 className="flex flex-row font-bold">
									Industry
									<Pop
										content={{
											color: "sky",
											title: state.naceShortName,
											body: state.naceDescription,
										}}
									/>
								</h6>
								<p className="text-base mb-0">{state.industry}</p>
							</div>
							<div className="min-w-fit">
								<h6 className="font-bold text-slate-800">Industry factor</h6>
								<p className="text-base mb-0">
									{`${state.industryFactor ? Number(state.industryFactor).toFixed(2) : "0.0"}`}
									<span className="pl-2 text-xs text-slate-600">Kg Co2e/kNOK</span>
								</p>
							</div>
						</div>
					)}
					<div className="flex gap-4 justify-between my-3">
						<div className="grow border-b-4 border-dotted border-slate-500 h-4"></div>
						<div className="text-slate-600 grow-0 text-xl">{t("Or")}</div>
						<div className="grow border-b-4 border-dotted border-slate-500 h-4"></div>
					</div>
					<div className="my-2">
						<h5 className="text-slate-800 font-bold">{t("InputEmmissionManually")}</h5>
					</div>

					<div className="grid grid-cols-2 gap-4">
						<div>
							<Input
								label={t("Scope1")}
								id="Scope_1"
								name="scope1"
								type="number"
								disable={false}
								value={state.scope1}
								handleChange={e => handleUpdateState({ scope1: e.target.value })}
								unit={t("Kg-Co2e")}
							/>
						</div>
						<div>
							<Input
								label={t("Scope2")}
								id="Scope_2"
								name="scope2"
								type="number"
								disable={false}
								value={state.scope2}
								handleChange={e => handleUpdateState({ scope2: e.target.value })}
								unit={t("Kg-Co2e")}
							/>
						</div>
						<div>
							<Input
								label={t("Scope3")}
								id="Scope_3"
								name="scope3"
								type="number"
								disable={false}
								value={state.scope3}
								handleChange={e => handleUpdateState({ scope3: e.target.value })}
								unit={t("Kg-Co2e")}
							/>
						</div>
						<div className="">
							<label className="font-semibold block text-sm leading-4 mb-1 text-slate-800">
								{t("Scope 3 Category")}
							</label>
							<Select
								id="Scope_3_Catagory"
								name="Scope_3_Catagory"
								value={state.defaultScope3Catagory}
								onChange={event => {
									handleChangeScope(event)
								}}
								options={state.Scope_3_Catagory}
								maxMenuHeight={150}
							/>
							<span className="e-float-line"></span>
						</div>

						<div className="e-control-wrapper border-0">
							<TextArea
								className=""
								label={t("Notes")}
								id="Notes"
								name="Notes"
								value={state?.Notes}
								rows="2"
								handleChange={e => handleUpdateState({ Notes: e.target.value })}
							></TextArea>
						</div>

						<div className="e-float-input e-control-wrapper">
							<label className="font-semibold block text-sm leading-4 mb-1 text-slate-800">
								Status
							</label>
							<DropDownListComponent
								id="Status"
								name="Status"
								value={state?.Status}
								itemTemplate={flagItemTemplate}
								valueTemplate={flagValueTemplate}
								dataSource={editorData}
								fields={dropDownFields}
								change={e => handleUpdateState({ Status: e.itemData.value, isStatusChanged: true })}
							/>
							<input style={{ display: "none" }} />
							{/* <span className="e-float-line"></span> */}
						</div>
					</div>
				</div>
			</DialogComponent>
		</div>
	)
}

const getScope3CategoryOption = t => {
	let data = [
		{ value: 1, label: t("Category1-PurchasedGoodsAndServices") },
		{ value: 2, label: t("Category2-CapitalGoods") },
		{ value: 3, label: t("Category3-FuelAndEnergyRelatedActivities") },
		{ value: 4, label: t("Category4-UpstreamTransportationAndDistribution") },
		{ value: 5, label: t("Category5-WasteGeneratedInOperations") },
		{ value: 6, label: t("Category6-BusinessTravel") },
		{ value: 7, label: t("Category7-EmployeeCommuting") },
		{ value: 8, label: t("Category8-UpstreamLeasedAssets") },
		{ value: 9, label: t("Category9-DownstreamTransportationandDistribution") },
		{ value: 10, label: t("Category10-ProcessingofSoldProducts") },
		{ value: 11, label: t("Category11-UseofSoldProducts") },
		{ value: 12, label: t("Category12-EndofLifeTreatmentofSoldProducts") },
		{ value: 13, label: t("Category13-DownstreamLeasedAssets") },
		{ value: 14, label: t("Category14-Franchises") },
		{ value: 15, label: t("Category15-Investments") },
	]
	return data
}

function initializeState(props) {
	const { data, naceData, t } = props
	let data1 = { ...data }
	data1.Scope_1C = data1.scope1
	data1.Scope_2C = data1.scope2
	data1.Scope_3C = data1.scope3
	data1.scope1 = Number(data1.scope1).toFixed(2)
	data1.scope2 = Number(data1.scope2).toFixed(2)
	data1.scope3 = Number(data1.scope3).toFixed(2)
	data1["selectedRecalculation"] = ""
	data1["naceData"] = naceData
	data1["changedScopeValue"] = 0
	data1["customEmmisonFactor"] = ""
	data1["isNaceCode"] = false
	data1["hasFlagChanged"] = false
	let record = data1.naceData.filter(rec => rec.code === data1.NaceCode)
	data1["industryFactor"] = record.length > 0 ? record[0].co2Intensity : ""
	data1["industry"] = record.length > 0 ? record[0].name : ""
	data1["naceShortName"] = record.length > 0 ? record[0].shortName : ""
	data1["naceDescription"] = record.length > 0 ? record[0].naceNotes : ""
	data1["Scope_3_Catagory"] = getScope3CategoryOption(t)
	return data1
}
