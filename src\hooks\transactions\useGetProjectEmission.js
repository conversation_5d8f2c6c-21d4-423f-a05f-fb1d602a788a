import { useQuery } from "@tanstack/react-query"

export default function useGetCompanyAnalysis(app, RegistrationNumber, year) {
	return useQuery({
		queryKey: ["projectEmissions", RegistrationNumber, year],
		queryFn: async () => {
			return await app.getProjectEmissions(RegistrationNumber, year)
		},
		enabled: RegistrationNumber !== null,
		//refetchOnWindowFocus: false,
		refetchOnMount: true,
		retry: 5,
	})
}
