exports = async (args) => {
	const { RegistrationNumber, ids } = args

    // Registration Number of a company and ids is array of _id

	try {
		const company = await context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("transaction")

        // Convert string ids to ObjectID
        const objectIds = ids.map(id => new BSON.ObjectId(id));

        // Delete transactions from database.
        await company.deleteMany({ RegistrationNumber: RegistrationNumber, _id: { $in: objectIds } })

        return {success : true}

	} catch (error) {
		console.log(error)
		return { success: false }
	}
}
