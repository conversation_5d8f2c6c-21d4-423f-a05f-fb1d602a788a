{
	"compilerOptions": {
		"target": "ES2020",
		"lib": ["ES2020", "DOM", "DOM.Iterable"],
		"allowJs": true,
		"skipLibCheck": true,
		"esModuleInterop": true,
		"allowSyntheticDefaultImports": true,
		"strict": true,
		"forceConsistentCasingInFileNames": true,
		"noFallthroughCasesInSwitch": true,
		"module": "ESNext",
		"moduleResolution": "bundler",
		"resolveJsonModule": true,
		"isolatedModules": true,
		"noEmit": true,
		"jsx": "react-jsx",
		"baseUrl": ".",
		"paths": {
			"@/*": ["./src/*"]
		},
		"types": ["vite/client", "vitest/globals", "@testing-library/jest-dom"],
		// Enhanced strict mode options
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"exactOptionalPropertyTypes": true,
		"noImplicitReturns": true,
		"noImplicitOverride": true,
		"noPropertyAccessFromIndexSignature": true,
		"noUncheckedIndexedAccess": true,
		"useUnknownInCatchVariables": true,
		"allowUnreachableCode": false,
		"allowUnusedLabels": false,
		"noImplicitAny": true,
		"strictNullChecks": true,
		"strictFunctionTypes": true,
		"strictBindCallApply": true,
		"strictPropertyInitialization": true,
		"alwaysStrict": true
	},
	"include": ["src/**/*", "vite.config.ts", "vitest.config.ts", "src/types/global.d.ts"],
	"exclude": ["node_modules", "dist", "build"]
}
