exports = async function (input) {
  try {
    const request = context.services
      .get("mongodb-atlas")
      .db(context.environment.values.database)
      .collection("transaction");
    let query = {
      RegistrationNumber: input.RegistrationNumber,
      PeriodYear: input.year,
    };
    if (input.AnalysisIDs) {
      query["Analysis"] = {
        $elemMatch: {
          AnalysisID: {
            $in: [...input.AnalysisIDs],
          },
        },
      };
    }

    const commonMatch = {
      $match: query,
    };

    const res = await request
			.aggregate([
				commonMatch,
				{
					$facet: {
						capitalGood: [
							{ $match: { Relation: "Production", Type: "Capital good" } },
							{ $group: { _id: undefined, Emission: { $sum: "$Scope_3" } } },
						],
						intermediate: [
							{ $match: { Relation: "Production", Type: "intermediate" } },
							{ $group: { _id: undefined, Emission: { $sum: "$Scope_3" } } },
						],
						final: [
							{ $match: { Relation: "Production", Type: "final" } },
							{ $group: { _id: undefined, Emission: { $sum: "$Scope_3" } } },
						],
						categoriesData: [
							{
								$group: {
									_id: "$Scope_3_Category",
									Emission: { $sum: "$Scope_3" },
								},
							},
						],
						scopeData: [
							{ $match: { Status: 1, Scope_3_Category:{$lte:8} } },
							{
								$group: {
									_id: undefined,
									Scope_1: { $sum: "$Scope_1" },
									Scope_2: { $sum: "$Scope_2" },
									Scope_3: { $sum: "$Scope_3" },
								},
							},
						],
						totalEnergy: [
							{
								$group: {
									_id: undefined,
									totalNonRenewable: { $sum: "$non_renewable_energy" },
									totalRenewable: { $sum: "$renewable_energy" },
									totalStationary: { $sum: "$stationaryCombustion" },
									totalMobile: { $sum: "$mobileCombustion" },
									totalNuclear: { $sum: "$nuclear" },
								},
							},
						],
						totalScope: [
							{
								$group: {
									_id: undefined,
									Scope_1: { $sum: "$Scope_1" },
									Scope_2: { $sum: "$Scope_2" },
									Scope_3: { $sum: "$Scope_3" },
								},
							},
						],
					},
				},
			])
			.toArray()

    // here scopeData with scope data with scope 1
    // totalScope is scope data with all status eg ( 0,1,2,3)
    // categoriesData is all categories data

    let { scopeData, totalScope, categoriesData } = res[0];

    let downStreamEmission = 0;

    // here we are getting downstream emission as downstream  categories are 9-15 that is why
    // there is if condition

    categoriesData.forEach((category) => {
      if (category?._id > 8) {
        downStreamEmission += category.Emission;
      }
    });

    // need to subtract downstram emission from scopeData and total Scope
    // because downstream emission is also scope3 emission and we do sum
    // of scope 3 emissions it is also added in it

    // check if there is totalScope or not
    if (totalScope.length > 0)
      totalScope[0].Scope_3 = totalScope[0].Scope_3 - downStreamEmission;

    // check if there is scopeData or not ( primary data )
    //if (scopeData.length > 0)
      // this is check wheter scope 3 is presnt or not
      //scopeData[0].Scope_3 = scopeData[0].Scope_3 - downStreamEmission;

    const scopeDataKeys =
      scopeData.length > 0
        ? Object.keys(scopeData[0]).filter((key) => key !== "_id")
        : [];

    let totalPrimaryScope = 0;

    // this will return data with key value pairs key will be Scope_1, Scope_2
    // or Scope_3 value will be percentage

    const scopePercentageWithStatus1 = scopeDataKeys.reduce((acc, key) => {
      totalPrimaryScope += scopeData[0][key];
      const percentage = scopeData[0][key]
        ? ((scopeData[0][key] / totalScope[0][key]) * 100).toFixed(2)
        : 0;
      return { ...acc, [key]: percentage };
    }, {});

    //totalPrimaryScope += downStreamEmission

    const data = {
      capitalGood:
        res[0].capitalGood.length == 0 ? 0.0 : res[0].capitalGood[0].Emission,
      intermediate:
        res[0].intermediate.length == 0 ? 0.0 : res[0].intermediate[0].Emission,
      final: res[0].final.length == 0 ? 0.0 : res[0].final[0].Emission,
      totalNonRenewable:
        res[0].totalEnergy.length == 0
          ? 0.0
          : res[0].totalEnergy[0].totalNonRenewable,
      totalRenewable:
        res[0].totalEnergy.length == 0
          ? 0.0
          : res[0].totalEnergy[0].totalRenewable,
      totalStationary:
        res[0].totalEnergy.length == 0
          ? 0.0
          : res[0].totalEnergy[0].totalStationary,
      totalMobile:
        res[0].totalEnergy.length == 0
          ? 0.0
          : res[0].totalEnergy[0].totalMobile,
      totalNuclear:
        res[0].totalEnergy.length == 0
          ? 0.0
          : res[0].totalEnergy[0].totalNuclear,
      scopePercentageWithStatus1,
      totalPrimaryScope,
      totalScope:
        totalScope.length === 0
          ? 0.0
          : Number(totalScope[0].Scope_1) +
            Number(totalScope[0].Scope_2) +
            Number(totalScope[0].Scope_3),
      success: true,
    };

    return data;
  } catch (err) {
    console.error(err);
    return {
      capitalGood: 0.0,
      intermediate: 0.0,
      final: 0.0,
      totalNonRenewable: 0.0,
      totalRenewable: 0.0,
      success: false,
    };
  }
};
