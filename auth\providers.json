{"anon-user": {"name": "anon-user", "type": "anon-user", "disabled": true}, "api-key": {"name": "api-key", "type": "api-key", "disabled": false}, "custom-token": {"name": "custom-token", "type": "custom-token", "config": {"audience": ["authenticated"], "requireAnyAudience": false, "signingAlgorithm": "HS256", "useJWKURI": false}, "secret_config": {"signingKeys": ["Clerk_Realm_Authenticated_JWT-template"]}, "disabled": false, "metadata_fields": [{"required": true, "name": "email", "field_name": "email"}, {"required": true, "name": "sub", "field_name": "netlifyID"}, {"required": true, "name": "app_metadata.provider", "field_name": "provider"}, {"required": true, "name": "role", "field_name": "role"}]}, "local-userpass": {"name": "local-userpass", "type": "local-userpass", "config": {"autoConfirm": true, "resetPasswordSubject": "reset", "resetPasswordUrl": "http://localhost:3000/reset", "runConfirmationFunction": false, "runResetFunction": false}, "disabled": false}}