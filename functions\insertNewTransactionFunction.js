exports = async function (arg) {
	// Get registration number.
	try {
		const transaction = arg.data
		transaction.RegistrationNumber = arg.RegistrationNumber
		const request = context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("transaction")
		const res = await request.insertOne(transaction)
		const { SupplierID, Scope_1, Scope_2, Scope_3, RegistrationNumber } = transaction

		if (SupplierID && (Scope_1 != 0 || Scope_2 != 0 || Scope_3 != 0)) {
			let newEmission = Scope_1 + Scope_2 + Scope_3

			const req = context.services.get("mongodb-atlas").db(context.environment.values.database).collection("company")
			const query = {
				RegistrationNumber: RegistrationNumber,
				"Suppliers.SupplierID": SupplierID,
			}

			const update = {
				$set: { "Suppliers.$[supplier].Emission": newEmission },
			}

			const options = {
				arrayFilters: [{ "supplier.SupplierID": SupplierID }],
			}

			const result = await req.updateOne(query, update, options)
		}
		transaction._id = res.insertedId
		return { success: true, transaction: transaction }
	} catch (err) {
		console.error("Insert transaction error:", err)
		return { success: false }
	}
}
