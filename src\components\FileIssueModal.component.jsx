import { Dialog, Transition } from "@headlessui/react"
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline"
import axios from "axios"
import { Fragment } from "react"

// eslint-disable-next-line import/no-unresolved
import { useToast } from "@/hooks"

export default function FileIssueModal({
	open,
	setOpen,
	userID,
	errorMessage,
	RegistrationNumber = "",
	userEmail,
}) {
	//const [open, setOpen] = useState(true)
	const Toast = useToast()

	const handleSubmit = async e => {
		try {
			e.preventDefault()
			const myForm = e.target
			const formData = new FormData(myForm)
			await axios.post("/", formData, {
				headers: { "Content-Type": "multipart/form-data" },
			})
			Toast("success", "We have received your report. We will contact you soon.")
		} catch (error) {
			// handle error
		}
	}

	return (
		<div className="z-50">
			<Transition.Root show={open} as={Fragment}>
				<Dialog as="div" className="relative z-50" onClose={setOpen}>
					<Transition.Child
						as={Fragment}
						enter="ease-out duration-300"
						enterFrom="opacity-0"
						enterTo="opacity-100"
						leave="ease-in duration-200"
						leaveFrom="opacity-100"
						leaveTo="opacity-0"
					>
						<div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
					</Transition.Child>

					<div className="fixed inset-0 z-50 overflow-y-auto">
						<div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
							<Transition.Child
								as={Fragment}
								enter="ease-out duration-300"
								enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
								enterTo="opacity-100 translate-y-0 sm:scale-100"
								leave="ease-in duration-200"
								leaveFrom="opacity-100 translate-y-0 sm:scale-100"
								leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
							>
								<Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
									<div className="sm:flex sm:items-start">
										<div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
											<ExclamationTriangleIcon
												className="h-6 w-6 text-red-600"
												aria-hidden={true}
											/>
										</div>
										<div className=" sm:ml-4 sm:mt-0 sm:text-left">
											<Dialog.Title
												as="h2"
												className="text-lg font-semibold leading-6 text-rose-700"
											>
												Upload error
											</Dialog.Title>
											<div className="">
												<p className="text-sm text-slate-700 mb-0">
													Unfortunately something went wrong when we were trying to parse the
													information. This might be caused by malformed SAF-T file or missing
													information. Click the send report and we will be notified to resolve the
													issue.
												</p>
											</div>
										</div>
									</div>
									<form name="FileIssue" onSubmit={handleSubmit}>
										<input type="hidden" name="form-name" value="FileIssue" />
										<input className="hidden" type="email" name="User Email" value={userEmail} />
										<input
											className="hidden"
											type="text"
											name="Registration Number"
											value={RegistrationNumber}
										/>
										<input className="hidden" type="text" name="User ID" value={userID} />
										<input
											className="hidden"
											type="text"
											name="Error Message"
											value={errorMessage}
										/>
										<div className=" sm:flex sm:flex-row-reverse">
											<button
												type="submit"
												className="mt-3 inline-flex w-full justify-center rounded-md bg-sky-600 px-3 py-2 ml-2 text-sm font-semibold text-white shadow-sm border border-inset hover:bg-sky-600 sm:mt-0 sm:w-auto"
												onClick={() => setOpen(false)}
											>
												SEND REPORT
											</button>
											<button
												type="button"
												className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-sky-700 shadow-sm border border-inset border-sky-400 hover:bg-sky-50 sm:mt-0 sm:w-auto"
												onClick={() => setOpen(false)}
											>
												CANCEL
											</button>
										</div>
									</form>
								</Dialog.Panel>
							</Transition.Child>
						</div>
					</div>
				</Dialog>
			</Transition.Root>
		</div>
	)
}
