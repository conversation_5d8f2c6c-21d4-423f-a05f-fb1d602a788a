{"root": true, "env": {"browser": true, "es2020": true, "node": true}, "globals": {"vi": "readonly", "describe": "readonly", "it": "readonly", "expect": "readonly", "beforeEach": "readonly", "afterEach": "readonly", "beforeAll": "readonly", "afterAll": "readonly"}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:jsx-a11y/recommended", "plugin:import/recommended", "plugin:import/typescript", "prettier"], "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "settings": {"react": {"version": "detect"}, "import/resolver": {"typescript": {"alwaysTryTypes": true, "project": "./tsconfig.json"}, "node": {"extensions": [".js", ".jsx", ".ts", ".tsx"]}}}, "plugins": ["react", "react-hooks", "jsx-a11y", "import", "prettier"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "prettier/prettier": "error", "no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "no-empty": "warn", "no-console": ["warn", {"allow": ["warn", "error"]}], "no-debugger": "error", "prefer-const": "error", "no-var": "error", "import/no-unresolved": "error", "import/no-duplicates": "error", "import/no-unused-modules": "warn", "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}], "jsx-a11y/anchor-is-valid": "error", "jsx-a11y/alt-text": "error", "jsx-a11y/aria-props": "error", "jsx-a11y/aria-proptypes": "error", "jsx-a11y/aria-unsupported-elements": "error", "jsx-a11y/role-has-required-aria-props": "error", "jsx-a11y/role-supports-aria-props": "error", "react/jsx-key": "error", "react/jsx-no-duplicate-props": "error", "react/jsx-no-undef": "error", "react/jsx-uses-react": "off", "react/jsx-uses-vars": "error", "react/no-deprecated": "warn", "react/no-direct-mutation-state": "error", "react/no-unknown-property": "error", "react/require-render-return": "error", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}, "overrides": [{"files": ["*.ts", "*.tsx"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json"}, "extends": ["plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking"], "plugins": ["@typescript-eslint"], "rules": {"no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-non-null-assertion": "warn", "@typescript-eslint/prefer-nullish-coalescing": "error", "@typescript-eslint/prefer-optional-chain": "error", "@typescript-eslint/no-unnecessary-type-assertion": "error", "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/await-thenable": "error", "@typescript-eslint/no-misused-promises": "error", "@typescript-eslint/require-await": "warn", "@typescript-eslint/no-unsafe-assignment": "warn", "@typescript-eslint/no-unsafe-call": "warn", "@typescript-eslint/no-unsafe-member-access": "warn", "@typescript-eslint/no-unsafe-return": "warn", "@typescript-eslint/restrict-template-expressions": "warn", "@typescript-eslint/prefer-as-const": "error", "@typescript-eslint/no-inferrable-types": "error", "@typescript-eslint/consistent-type-imports": ["error", {"prefer": "type-imports", "disallowTypeAnnotations": false}]}}, {"files": ["*.test.ts", "*.test.tsx", "*.spec.ts", "*.spec.tsx"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/no-unsafe-member-access": "off"}}]}