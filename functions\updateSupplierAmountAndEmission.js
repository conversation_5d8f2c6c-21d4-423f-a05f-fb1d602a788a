exports = async function (arg) {
	// Get registration number.
	try {
		const { supplier_ids, RegistrationNumber } = arg

		const transactionsCollection = context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("transaction")

		const pipeline = [
			{
				$match: { RegistrationNumber, SupplierID: { $in: supplier_ids } },
			},
			{
				$group: {
					_id: "$SupplierID",
					//SupplierID: { $first: '$SupplierID' }, // Include the SupplierID in the result
					Emission: { $sum: { $add: ["$Scope_1", "$Scope_2", "$Scope_3"] } },
					Amount: { $sum: "$Amount" },
				},
			},
		]

		const transactions = await transactionsCollection.aggregate(pipeline).toArray()

		const bulkOperations = transactions.map((transaction) => ({
			updateOne: {
				filter: { RegistrationNumber, "Suppliers.SupplierID": transaction._id },
				update: {
					$set: {
						"Suppliers.$.Amount": transaction.Amount,
						"Suppliers.$.Emission": transaction.Emission,
					},
				},
			},
		}))

		const companiesCollection = context.services
			.get("mongodb-atlas")
			.db(context.environment.values.database)
			.collection("company")

		const res = await companiesCollection.bulkWrite(bulkOperations)

		return { success: true }
	} catch (err) {
		console.error("Insert transaction error:", err)
		return { success: false }
	}
}
