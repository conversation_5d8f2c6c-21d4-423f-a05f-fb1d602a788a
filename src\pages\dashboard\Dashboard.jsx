/* eslint-disable jsx-a11y/click-events-have-key-events */
import { Callout, Flex, Card, Text, Metric, CategoryBar, Title } from "@tremor/react"
import React, { useState, useEffect, useRef } from "react"
import { useTranslation } from "react-i18next"
import { FaExclamation } from "react-icons/fa"
import { useSelector, useDispatch } from "react-redux"
import { useNavigate } from "react-router-dom"

import AddReportingEntityModal from "../../components/AddReportingEntityModal.component"
import PieChart from "../../components/ui/PieChart"
import Spinner from "../../components/ui/Spinner"
import {
	useGetSuppliers,
	useGetTransactions,
	useGetNaceQuery,
	useGetScopeChartData,
	useGetTopSuppliers,
	useGetScopeCategoryData,
	useGetSuppliersList,
	useGetCO2AddedData,
	useGetCompanyEmission,
	useGetLastUploadedTransactionDate,
	useGetMonthlyChartData,
	useToast,
	useGetYearlyChartData,
	useGetIndustryCo2Intensity,
} from "../../hooks"
import { useRealmApp } from "../../realm/RealmAppProvider"
import "../../styles/dashboard.css"
import { getPrimaryDataColors } from "../../services/helper"
import { handleSetScopeData } from "../../store/actions/UserAction"
import useFullPageLoader from "../loader/useFullPageLoader"

import BarChartComponent from "./BarChart.component"
import Scope from "./Scope.component"
import Scope3Category from "./Scope3Category.compnent"

const Dashboard = props => {
	const { t, i18n } = useTranslation("dashboard")
	const app = useRealmApp()
	const dispatch = useDispatch()
	const navigate = useNavigate()
	const toast = useToast()
	const scrollToTop = useRef(null)

	const currentOrganization = useSelector(state => state.user.currentOrganization)
	const isCompanyRegistered = useSelector(state => state.company)
	const reportingYear = useSelector(state => state.reporting)
	const netlifyId = currentOrganization ? currentOrganization.netlifyID : ""
	const RegistrationNumber = currentOrganization ? currentOrganization.RegistrationNumber : ""
	const naceCode = currentOrganization ? currentOrganization?.company?.NaceCode : "0000"
	const [loader, showLoader, hideLoader] = useFullPageLoader()
	let colorCode = ["red", "amber", "blue", "lime", "cyan", "emerald", "pink", "purple"]
	const [scopeData, setScopeData] = useState({
		Scope_1: 0.0,
		Scope_2: 0.0,
		Scope_3: 0.0,
		total: 0.0,
		isDefault: true,
	})
	const [stackBarData, setStackBarData] = useState([
		{ label: "Category Data", data: [0], backgroundColor: "#22aa99", stack: "1" },
	])
	const [scopeCategoryData, setScopeCategoryData] = useState([])
	const [isScopeCategoryData, setIsScopeCategoryData] = useState(false)
	const [topSuppliers, setTopSuppliers] = useState([])
	const [companyRevenueColor, setCompanyRevenueColor] = useState("emerald-700")
	const [revenueReportingYear, setRevenueReportingYear] = useState("")
	const [companyRevenue, setCompanyRevenue] = useState(null)
	const [co2Added, setCo2Added] = useState([])
	const [totalCo2Added, setTotalCo2Added] = useState(0)
	const [isTotalCo2Added, setIsTotalCo2Added] = useState(false)
	const [totalEnergy, setTotalEnergy] = useState(0)
	const [energyData, setEnergyData] = useState([])
	const [energyChartValues, setEnergyChartValues] = useState([])
	const isUnmounted = useRef(false)
	const workingOnData = useSelector(state => state.uploading)
	const [scope2CalculationMethod, setScope2CalculationMethod] = useState({
		name: "marketBased",
		label: t("market-based"),
		value: "market-based",
	})
	const [scope2Data, setScope2Data] = useState([])
	const scope2Methods = [
		{ name: "combustionBased", label: t("consumption-based"), value: "consumption-based" },
		{ name: "locationBased", label: t("location-based"), value: "location-based" },
		{ name: "marketBased", label: t("market-based"), value: "market-based" },
	]

	const [showModal, setShowModal] = useState(false)
	const [carbonIntensity, setCarbonIntensity] = useState(null)
	const [revenue, setRevenue] = useState("")
	const [scopeCategoryPrimaryData, setScopeCategoryPrimaryData] = useState([])
	const [scopePrimaryData, setScopePrimaryData] = useState([])
	const [totalPrimaryScope, setTotalPrimaryScope] = useState(null)
	const [downStreamCatagoryEmission, setDownStreamCatagoryEmission] = useState(null)
	const [totalEmissionIncludingDownStream, settotalEmissionIncludingDownStream] = useState(null)
	const [scope3EmissionIncludingDownStream, setScope3EmissionIncludingDownStream] = useState(null)
	const [yearlyEmissionData, setYearlyEmissionData] = useState([])

	const scope1Transactions = useGetTransactions(app, {
		RegistrationNumber,
		year: reportingYear,
		Scope: 1,
	})
	const scope2Transactions = useGetTransactions(app, {
		RegistrationNumber,
		year: reportingYear,
		Scope: 2,
	})
	const scope3Transactions = useGetTransactions(app, {
		RegistrationNumber,
		year: reportingYear,
		Scope: 3,
	})
	const yearlyData = useGetYearlyChartData(app, { RegistrationNumber })

	const companyCo2Intensity = useGetIndustryCo2Intensity(app, { naceCode })

	useGetSuppliers(app, { RegistrationNumber, year: reportingYear })
	const naceData = useGetNaceQuery(app)
	useGetSuppliersList(app, { RegistrationNumber })
	useGetLastUploadedTransactionDate(app, { RegistrationNumber })

	const scope_Data = useGetScopeChartData(app, { RegistrationNumber, year: reportingYear })
	const top_Suppliers = useGetTopSuppliers(app, { RegistrationNumber, year: reportingYear })
	const scope_catagory_data = useGetScopeCategoryData(app, {
		RegistrationNumber,
		year: reportingYear,
	})
	const CO2AddedData = useGetCO2AddedData(app, {
		RegistrationNumber,
		year: reportingYear,
	})
	const monthlyChartData = useGetMonthlyChartData(app, {
		RegistrationNumber,
		year: reportingYear,
	})

	const CompanyEmission = useGetCompanyEmission(app, { RegistrationNumber, year: reportingYear })

	useEffect(() => {
		if (!workingOnData) {
			scope_Data.refetch()
			top_Suppliers.refetch()
			scope1Transactions.refetch()
			scope2Transactions.refetch()
			scope3Transactions.refetch()
			scope_catagory_data.refetch()
			CO2AddedData.refetch()
			monthlyChartData.refetch()
		}
	}, [workingOnData])

	const generateColorCode = () => {
		const randomColor = Math.floor(Math.random() * 16777215).toString(16)
		return randomColor
	}

	const addEmployeeCommutingTransaction = () => {
		navigate("/transactions", {
			state: { transactionType: "EmployeeCommutingTransaction" },
		})
	}

	const redirectToScopeTransaction = (scope, e) => {
		if (e && e.target && typeof e.target.className == "string") {
			if (e.target.innerText.includes("Scope")) {
				navigate(`/transactions/${scope}`)
			}
		}
	}

	const calculateEmission = e => {
		let { name } = e
		let s = { ...scopeData }
		s.total -= s.Scope_2
		let total = s.total + Number(scope2Data[name]) || 0
		s = { ...scopeData, Scope_2: Number(scope2Data[name]), total: Number(total) }
		setScope2CalculationMethod(e)
		setScopeData(s)
	}

	const calculateCarbonIntensity = async () => {
		try {
			const totalScopeData = scopeData.total * 1000
			const revenueInThousands = Number(revenue) / 1000
			const carbonIntensity = (totalScopeData / revenueInThousands).toFixed(2)
			setCarbonIntensity(carbonIntensity)
			setShowModal(false)

			const emissionInput = {
				year: reportingYear,
				Revenue: Number(revenue),
				RegistrationNumber: RegistrationNumber,
			}
			showLoader()
			await app.updateCompanyEmission(emissionInput)
			hideLoader()
		} catch (error) {
			// Handle error
		}
	}

	useEffect(() => {
		isUnmounted.current = false
	}, [netlifyId, currentOrganization.organizationId, reportingYear])

	useEffect(() => {
		const getCo2Added = async () => {
			try {
				/* eslint react-hooks/exhaustive-deps: 0 */
				setIsTotalCo2Added(false)
				const res = CO2AddedData
				//	if (res.data.success) {
				let data = [
					{
						name: "Total emissions",
						value: res?.data?.totalScope || 0,
					},
					{
						name: "Production related intermediate products",
						value: res?.data?.intermediate || 0,
					},
					{
						name: "Production related final products",
						value: res?.data?.final || 0,
					},
				]

				let total =
					Math.round(res.data?.totalNonRenewable || 0) +
						Math.round(res.data?.totalRenewable || 0) +
						Math.round(res.data?.totalMobile || 0) +
						Math.round(res.data?.totalStationary || 0) +
						Math.round(res.data?.totalNuclear || 0) || 0

				let energyData = [
					{
						name: "Stationary combustion",
						value: Math.round(res.data?.totalStationary || 0),
						percentage: (Math.round(res.data?.totalStationary || 0) / total) * 100 || 0,
					},
					{
						name: "Mobile combustion",
						value: Math.round(res.data?.totalMobile || 0),
						percentage: (Math.round(res.data?.totalMobile || 0) / total) * 100 || 0,
					},
					{
						name: "Renewable electricity",
						value: Math.round(res.data?.totalRenewable || 0),
						percentage: (Math.round(res.data?.totalRenewable || 0) / total) * 100 || 0,
					},
					{
						name: "Non-Renewable electricity",
						value: Math.round(res.data?.totalNonRenewable || 0),
						percentage: (Math.round(res.data?.totalNonRenewable || 0) / total) * 100 || 0,
					},
					{
						name: "Nuclear energy",
						value: Math.round(res.data?.totalNuclear || 0),
						percentage: (Math.round(res.data?.totalNuclear || 0) / total) * 100 || 0,
					},
				]

				setEnergyChartValues({
					"Stationary combustion": Math.round(res.data?.totalStationary || 0),
					"Mobile combustion": Math.round(res.data?.totalMobile || 0),
					"Renewable electricity": Math.round(res.data?.totalRenewable || 0),
					"Non-Renewable electricity": Math.round(res.data?.totalNonRenewable || 0),
					"Nuclear energy": Math.round(res.data?.totalNuclear || 0),
				})

				let co2Added = res.data?.totalScope - res.data?.intermediate - res.data?.final || 0

				setTotalPrimaryScope((res?.data?.totalPrimaryScope / res.data?.totalScope) * 100)

				setScopePrimaryData(res?.data?.scopePercentageWithStatus1 || [])

				setTotalCo2Added(co2Added)
				setCo2Added(data)
				setTotalEnergy(total)
				setEnergyData(energyData)
				//}
				setIsTotalCo2Added(true)
			} catch (error) {
				// Handle Error
				setIsTotalCo2Added(true)
			}
		}

		//if (!scopeData.isDefault) {
		getCo2Added()
		//}
	}, [CO2AddedData.data])

	useEffect(() => {
		try {
			if (isScopeCategoryData) {
				let heightOne = 0
				heightOne += document.getElementById("scope-sub-container").clientHeight
				document.getElementById("bar-chart-container").style.maxHeight = heightOne + 24 + "px"
				document.getElementById("bar-chart-container").style.paddingTop = 24 + "px"
			}
		} catch (error) {
			// Handle Error
		}
	}, [isScopeCategoryData])

	useEffect(() => {
		//setIsScopeCategoryData(false)
		const getScopeCategoryData = async () => {
			try {
				/* eslint react-hooks/exhaustive-deps: 0 */
				const res = scope_catagory_data.data
				//if (isUnmounted.current) return
				//if (res.success) {
				if (res && res.categoryData && res.categoryData.length > 8) {
					for (let i = 8; i < res.categoryData.length; i++) {
						/* eslint react-hooks/exhaustive-deps: 0 */
						colorCode.push(`#${generateColorCode()}`)
					}
				}
				let data = {}
				for (let i = res.categoryData.length - 1; i >= 0; i--) {
					if (res.categoryData[i].Percentage >= 0) {
						// data.push({
						// 	label: `${res.categoryData[i].Description}`,
						// 	data: res.categoryData[i].Percentage,
						// 	/* eslint react-hooks/exhaustive-deps: 0 */
						// 	//backgroundColor: colorCode[i],
						// })

						data[res.categoryData[i].Description] = res.categoryData[i].Percentage
					}
				}

				setDownStreamCatagoryEmission(res?.downStreamTotalEmissionCatagory || 0)

				setStackBarData(data)
				setScopeCategoryPrimaryData(res?.categoriesWithStatus1 || [])
				setScopeCategoryData(res.categoryData)
				setIsScopeCategoryData(true)
				//} else {
				// Handle Error
				//}
			} catch (error) {
				// Handle Error
			}
		}

		//getScopeData()
		// getTopSuppliers()
		setIsScopeCategoryData(false)
		getScopeCategoryData()
		return () => {
			isUnmounted.current = true
		}
	}, [scope_catagory_data.data])

	useEffect(() => {
		if (top_Suppliers.data && top_Suppliers.data.success) {
			setTopSuppliers(top_Suppliers.data.topSuppliers)
		}
	}, [top_Suppliers.data])

	useEffect(() => {
		if (yearlyData?.data?.success) {
			setYearlyEmissionData(yearlyData.data.Data)
		}
	}, [yearlyData.data])

	// this handles scope data
	useEffect(() => {
		if (scope_Data.data && scope_Data.data.scope) {
			let {
				Scope_1,
				Scope_2,
				Scope_3,
				consumptionBased,
				locationBased,
				scope3IncludingDownStream,
			} = scope_Data.data.scope

			setScopeData({
				Scope_1: parseFloat(Scope_1),
				Scope_2: parseFloat(Scope_2),
				Scope_3: parseFloat(Scope_3),
				total: Scope_1 + Scope_2 + Scope_3,
				isDefault: false,
			})

			// if scope3IncludingDownStream is not null, then set scope3EmissionIncludingDownStream and totalEmissionIncludingDownStream
			if (scope3IncludingDownStream) {
				settotalEmissionIncludingDownStream(Scope_1 + Scope_2 + scope3IncludingDownStream)
				setScope3EmissionIncludingDownStream(scope3EmissionIncludingDownStream)
			} else {
				settotalEmissionIncludingDownStream(null)
				setScope3EmissionIncludingDownStream(null)
			}

			// save this data in redux store because we need same data on other pages

			dispatch(
				handleSetScopeData({
					Scope_1: parseFloat(Scope_1),
					Scope_2: parseFloat(Scope_2),
					Scope_3: parseFloat(Scope_3),
					total: Scope_1 + Scope_2 + Scope_3,
					isDefault: false,
				})
			)

			setScope2Data({
				combustionBased: Number(consumptionBased).toFixed(2),
				locationBased: Number(locationBased).toFixed(2),
				marketBased: Number(Scope_2).toFixed(2),
			})
		}
		// if (scope_Data.data && scope_Data.data.yearlyEmissionData) {
		// 	setYearlyEmissionData(scope_Data?.data?.yearlyEmissionData || [])
		// }
	}, [scope_Data.data])

	useEffect(() => {
		if (CompanyEmission.data?.success) {
			setRevenue(CompanyEmission.data?.data?.Revenue)
		} else {
			setRevenue("")
			setCarbonIntensity(null)
		}
	}, [CompanyEmission.data])

	useEffect(() => {
		let totalScope = scopeData.total * 1000
		let totalRevenue = Number(revenue) / 1000
		const intensity = (totalScope / totalRevenue).toFixed(2)

		dispatch(
			handleSetScopeData({ ...scopeData, revenue: Number(revenue), carbonIntensity: intensity })
		)

		if (revenue) setCarbonIntensity(intensity)
		else setCarbonIntensity(null)
	}, [scopeData.total, revenue])

	useEffect(() => {
		if (!naceData?.data?.data) naceData.refetch()
	}, [RegistrationNumber])

	const intensityScale = (factor = 15, industry = 12) => {
		if (typeof industry === "object") {
			let currentYear = reportingYear

			// if current year industry factor is not available, then use previous year industry factor

			if (!industry[currentYear]) {
				currentYear = reportingYear - 1
			}

			industry = industry[currentYear]
		}

		const highEnd = industry * 2
		const lowEnd = industry / 2

		// Ensure factor is within the range [lowEnd, highEnd]
		const clampedFactor = Math.min(Math.max(factor, lowEnd), highEnd)

		// Calculate logarithmic scale
		const indicator =
			((Math.log(clampedFactor) - Math.log(lowEnd)) / (Math.log(highEnd) - Math.log(lowEnd))) * 100

		return [indicator, lowEnd.toFixed(2), highEnd.toFixed(2), factor]
	}

	const handleScopeCatagoryFiltering = value => {
		try {
			navigate("/transactions", {
				state: {
					catagory: value,
					filter: true,
				},
			})
		} catch (error) {
			// handle error
		}
	}

	const handleTopSuppliersFiltering = value => {
		try {
			navigate("/transactions", {
				state: {
					supplierName: value,
					supplierFIlter: true,
				},
			})
		} catch (error) {
			// handle error
		}
	}

	async function fetchCo2Intensity() {
		try {
			showLoader()
			const response = await fetch("/.netlify/functions/proxy", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ RegistrationNumber }),
			})

			const data = await response.json()
			hideLoader()
			if (data?.error) {
				throw data
			}

			const driftsinntekt = parseInt(
				data[0]["resultatregnskapResultat"]["driftsresultat"]["driftsinntekter"][
					"sumDriftsinntekter"
				]
			)
			const year = data[0]["regnskapsperiode"]["fraDato"].slice(0, 4)

			setRevenueReportingYear(year)

			if (year === reportingYear) {
				setCompanyRevenueColor("rose-600")
			} else {
				setCompanyRevenueColor("emerald-700")
			}

			setCompanyRevenue(driftsinntekt)
			setRevenue(driftsinntekt)

			// Return the result in the specified format
			//return { [year]: { Revenue: driftsinntekt } }
		} catch (error) {
			setCompanyRevenueColor("rose-600")
			setCompanyRevenue(null)
			toast("error", t("fetch_intensity_error"))
		}
	}

	useEffect(() => {
		scrollToTop.current.scrollIntoView()
	}, [])

	return (
		<div ref={scrollToTop}>
			{!isCompanyRegistered ? (
				<AddReportingEntityModal props={props} />
			) : (
				<div className="bg-slate-50 p-4 2xl:px-8">
					<h1 className="text-2xl font-bold text-slate-900">{t("Dashboard")}</h1>
					<div className="grid auto-rows-min grid-cols-1 gap-x-8 gap-4 lg:grid-cols-2 xl:grid-cols-3">
						<div className="first flex flex-col gap-3">
							<Card
								className="mx-auto min-h-0 self-start"
								decoration="top"
								decorationColor="emerald"
							>
								<Text className="mb-2 text-[18px] font-bold">
									{t("Total emissions for")}
									{currentOrganization.CompanyName}
								</Text>

								<div className="flex flex-row justify-between">
									<div>
										<Metric className="leading-9">
											{Intl.NumberFormat(i18n.language === "en" ? "en" : "no-NO", {
												maximumFractionDigits: 2,
											}).format(scopeData.total)}
											<span className="pl-2 text-base font-normal">tCo2e</span>
										</Metric>
										<div className="flex flex-row items-baseline xl:flex-col 2xl:flex-row">
											<p
												className={`text-md font-bold text-${getPrimaryDataColors(totalPrimaryScope || 0)}-500`}
											>
												{Intl.NumberFormat(i18n.language === "en" ? "en" : "no-NO").format(
													totalPrimaryScope || ""
												)}{" "}
												%
											</p>
											<span className="pl-1 text-sm font-bold text-[#6B7280]">
												{t("common:primary")}
											</span>
										</div>
									</div>
									{totalEmissionIncludingDownStream && (
										<div>
											<Metric>
												{Intl.NumberFormat(i18n.language === "en" ? "en" : "no-NO", {
													maximumFractionDigits: 2,
												}).format(totalEmissionIncludingDownStream || scopeData.total)}
												<span className="pl-2 text-base font-normal">tCo2e</span>
											</Metric>
											<span className="pl-1 text-sm font-bold text-[#6B7280] xl:break-all">
												{t("Including_dwonstream")}
											</span>
										</div>
									)}
								</div>
							</Card>

							{/* This is the carbon intensity card */}
							<Card className="mx-auto" decoration="left" decorationColor="slate">
								<Flex>
									<Text className="text-[18px] font-bold">{t("Carbon intensity")}</Text>
									<Text className="text-sm font-bold">
										{carbonIntensity ? `${carbonIntensity} Kg / tNOK` : t("not_set_yet")}
									</Text>
									<div
										role="button"
										tabIndex={0}
										onClick={() => setShowModal(!showModal)}
										className="cursor-pointer"
									>
										<svg
											xmlns="http://www.w3.org/2000/svg"
											fill="none"
											viewBox="0 0 24 24"
											strokeWidth="1.5"
											stroke="currentColor"
											className="h-8 w-8 rounded-md p-1 ring-1 ring-slate-200"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												d="M3 4.5h14.25M3 9h9.75M3 13.5h9.75m4.5-4.5v12m0 0l-3.75-3.75M17.25 21L21 17.25"
											/>
										</svg>
									</div>

									{showModal && (
										<div className="absolute w-80 p-3 bg-white right-0 top-28 z-50 rounded shadow-lg">
											<div>
												<h6 className="text-sm">
													{t("Revenue for reporting year")} {reportingYear}
												</h6>
												<input
													className="w-full h-8 border rounded px-2 text-right"
													onChange={e => {
														const regex = /^-?\d*\.?\d*$/
														const value = e.target.value
														if (regex.test(value)) setRevenue(Number(e.target.value))
													}}
													value={revenue}
												/>
												{companyRevenue && (
													<span className={`text-sm font-bold text-${companyRevenueColor}`}>
														{t("lookup_return_revenue")}
														{revenueReportingYear}
													</span>
												)}
												<div className="flex flex-row justify-between align-middle mt-3">
													<button
														onClick={() => fetchCo2Intensity()}
														className="bg-teal-100 text-revenue_button text-lg font-semibold px-3 py-1"
													>
														Lookup
													</button>
													<div>
														<button
															onClick={() => setShowModal(!showModal)}
															className="bg-white border mr-3 border-revenue_button text-revenue_button text-lg font-semibold px-3 py-1"
														>
															Cancel
														</button>
														<button
															onClick={() => {
																calculateCarbonIntensity()
															}}
															className="bg-revenue_button text-white text-lg font-semibold px-3 py-1"
														>
															Save
														</button>
													</div>
												</div>
											</div>
										</div>
									)}
								</Flex>
								<CategoryBar
									values={[22, 21, 14, 21, 22]}
									showLabels={false}
									colors={["emerald", "lime", "yellow", "orange", "rose"]}
									markerValue={
										intensityScale(carbonIntensity || 0, companyCo2Intensity.data || 0)[0]
									}
									className="mt-4"
								/>
								<Flex>
									<Text className="mt-2">
										{intensityScale(carbonIntensity || 0, companyCo2Intensity.data || 0)[1]}{" "}
										<span className="text-xs">Kg/tNOK</span>
									</Text>
									{/* <Text className=" mt-2">
                value: {intensityScale()[0].toFixed(2)}
              </Text> */}
									<Text className="mt-2">
										{intensityScale(carbonIntensity || 0, companyCo2Intensity.data || 0)[2]}
										... <span className="text-xs">Kg</span>
									</Text>
								</Flex>
							</Card>
							{/* This is the scopes cards */}
							<div className="w-full">
								<Flex flexDirection="col" className="gap-3">
									<div
										className="w-full"
										key="scope1"
										role="button"
										tabIndex={0}
										onClick={e => redirectToScopeTransaction("scope1", e)}
									>
										<Scope
											heading={t("Scope 1 emissions")}
											decorationColor="rose"
											primaryColor={getPrimaryDataColors(scopePrimaryData.Scope_1 || 0)}
											primaryData={scopePrimaryData.Scope_1 || 0}
											colors={["rose", "slate"]}
											primaryDataLabel={t("common:primary")}
											value={scopeData.Scope_1 || 0}
											total={scopeData.total || 0}
											language={i18n.language === "en" ? "en" : "no-NO"}
										/>
									</div>
									<div
										className="w-full emissions"
										key="scope2"
										role="button"
										tabIndex={0}
										onClick={e => redirectToScopeTransaction("scope2", e)}
									>
										<Scope
											heading={t("Scope 2 emissions")}
											decorationColor="sky"
											primaryColor={getPrimaryDataColors(scopePrimaryData.Scope_2 || 0)}
											colors={["sky", "slate"]}
											primaryDataLabel={t("common:primary")}
											primaryData={scopePrimaryData.Scope_2 || null}
											value={scopeData.Scope_2 || 0}
											total={scopeData.total || 0}
											select={true}
											selectedValue={scope2CalculationMethod}
											onChangeHandler={e => calculateEmission(e)}
											options={scope2Methods}
											language={i18n.language === "en" ? "en" : "no-NO"}
										/>
									</div>

									<div
										className="w-full"
										id="scope3"
										tabIndex={0}
										role="button"
										onClick={e => redirectToScopeTransaction("scope3", e)}
									>
										<Scope
											heading={t("Scope 3 emissions")}
											decorationColor="amber"
											primaryData={scopePrimaryData.Scope_3 || null}
											primaryColor={getPrimaryDataColors(scopePrimaryData.Scope_3 || 0)}
											colors={["amber", "slate"]}
											value={scopeData.Scope_3}
											dwonStreamEmission={scope3EmissionIncludingDownStream}
											primaryDataLabel={t("common:primary")}
											total={scopeData.total}
											language={i18n.language === "en" ? "en" : "no-NO"}
										/>
									</div>
								</Flex>
							</div>

							<Card>
								<Title>{t("Emission_Per_Scope")}</Title>
								<Flex alignItems="center" flexDirection="row">
									<PieChart
										data={{
											"Scope 1": scopeData?.Scope_1,
											"Scope 2": scopeData?.Scope_2,
											"Scope 3": scopeData?.Scope_3,
										}}
										language={i18n.language === "en" ? "en" : "no-NO"}
										t={t}
									/>
								</Flex>
							</Card>
							{/* emission by years */}

							<div className="col-span-1">
								<BarChartComponent
									height="266px"
									language={i18n.language === "en" ? "en" : "no-NO"}
									title={"Yearly chart data"}
									index="_id"
									Data={yearlyEmissionData}
								/>
							</div>
						</div>

						{/* second coloumns */}
						<div className="flex flex-col">
							<Scope3Category
								t={t}
								chartData={stackBarData}
								primaryData={scopeCategoryPrimaryData}
								categoryData={scopeCategoryData}
								primaryDataLabel={t("common:primary")}
								isScopeCategoryData={isScopeCategoryData}
								handleScopeCatagoryFiltering={handleScopeCatagoryFiltering}
								language={i18n.language === "en" ? "en" : "no-NO"}
							/>

							<Card
								className=" min-h-36 self-start mt-3 flex items-center"
								decoration="left"
								decorationColor="emerald"
							>
								<div className="flex flex-col justify-center">
									<div className="">
										<Text className="text-sm font-bold">
											<span>{t("Category9_15")}</span>
										</Text>
									</div>
									<div className="flex mt-1 place-items-baseline">
										<Metric>
											{Intl.NumberFormat(i18n.language === "en" ? "en" : "no-NO", {
												maximumFractionDigits: 2,
											}).format(downStreamCatagoryEmission || 0)}
										</Metric>
										<Text className="text-sm font-bold">
											<span className="pl-2">tCo2e</span>
										</Text>
									</div>
								</div>
							</Card>
						</div>

						{/* third column */}

						<div className=" lg:col-span-2 xl:col-span-1 xl:row-span-2">
							{/* Nested grid for the four items */}
							<div className="grid grid-cols-1 gap-4 lg:grid-cols-2 xl:grid-cols-1">
								{/* Four items */}
								<div className="flex flex-col gap-3">
									<div className="ml-2 text-xl font-bold">{t("Top 5 suppliers")}</div>
									<div className="inline-block min-w-full align-middle">
										<div className="overflow-hidden border-t-4 border-sky-600 shadow-md ring-1 ring-slate-200 sm:rounded-lg">
											<table className="min-w-full divide-y divide-slate-300">
												<thead className="bg-slate-100">
													<tr>
														<th className="py-2 px-4 text-left text-base font-semibold text-slate-900 ">
															{t("Supplier Name")}
														</th>
														<th className="px-4 py-2 text-left text-base font-semibold text-slate-900">
															{t("Emissions")}
															<span className="text-slate-500"> tCo2e</span>
														</th>
													</tr>
												</thead>
												<tbody className="divide-y divide-slate-200 bg-white">
													{!top_Suppliers.isLoading ? (
														topSuppliers.map((supplier, index) => {
															return (
																<tr key={index}>
																	<td
																		onClick={() =>
																			handleTopSuppliersFiltering(supplier.SupplierName)
																		}
																		className="text-base text-sky-600 truncate py-2.5 px-4 cursor-pointer"
																		style={{ maxWidth: "100px" }}
																	>
																		{supplier.SupplierName}
																	</td>
																	<td className="w-24 whitespace-nowrap px-4 py-2.5 text-right text-base text-slate-700">
																		{Intl.NumberFormat(i18n.language === "en" ? "en" : "no-NO", {
																			maximumFractionDigits: 2,
																		}).format(supplier.Emission)}
																	</td>
																</tr>
															)
														})
													) : (
														<tr>
															<td className="custom-td" colSpan={2}>
																<Spinner type="md" />
															</td>
														</tr>
													)}
												</tbody>
											</table>
										</div>
									</div>
								</div>

								<div className="flex flex-col gap-3 xl:mt-2">
									<div className="ml-2 text-xl font-bold">{t("Energy overview")}</div>
									<div className="overflow-hidden border-t-4 border-sky-600 shadow-md ring-1 ring-slate-200 sm:rounded-lg mb-3">
										<table className="min-w-full divide-y divide-slate-300">
											<thead className="bg-slate-100">
												<tr>
													<th className="px-4 py-3 text-left text-base font-semibold text-slate-900 ">
														{t("Energy source")}
													</th>
													<th className="hidden 2xl:table-cell lg:table-cell md:table-cell px-2 py-2 text-left text-base font-semibold text-slate-900">
														{t("Percentage")}
													</th>
													<th className="float-right py-3 pl-2 pr-2 text-left text-base font-semibold text-slate-900">
														{t("Kilowatt hours")}
													</th>
												</tr>
											</thead>
											<tbody className="divide-y divide-slate-200 bg-white">
												{isTotalCo2Added ? (
													energyData.map((co2, index) => {
														return (
															<tr className="table-light" key={index}>
																<td
																	className="truncate py-2.5 px-4 text-base text-slate-700 "
																	style={{ maxWidth: "100px" }}
																>
																	{t(co2.name)}
																</td>
																<td className="hidden 2xl:table-cell lg:table-cell md:table-cell px-2 py-2 text-right text-base text-slate-700">
																	{/* Add your percentage value here */}
																	{co2.percentage.toFixed(2) || 0} %
																</td>
																<td className="w-6 px-4 py-2 text-right text-base text-slate-700">
																	{Intl.NumberFormat(i18n.language === "en" ? "en" : "no-NO", {
																		maximumFractionDigits: 2,
																	}).format(co2.value)}
																</td>
															</tr>
														)
													})
												) : (
													<tr>
														<td className="custom-td" colSpan={3}>
															<Spinner type="sm" />
														</td>
													</tr>
												)}
											</tbody>
											<tfoot>
												<tr className="border-t-2 border-slate-300">
													<td className="px-4 text-left text-base font-semibold text-slate-900">
														{t("Total energy to output")}
													</td>
													<td className="hidden 2xl:table-cell lg:table-cell md:table-cell px-2 py-2 text-right text-base font-semibold text-slate-900">
														{/* Add your total percentage value here */}
														100%
													</td>
													<td className="w-6 px-4 py-2 text-right text-base font-semibold text-slate-900">
														{Intl.NumberFormat(i18n.language === "en" ? "en" : "no-NO", {
															maximumFractionDigits: 2,
														}).format(totalEnergy)}
													</td>
												</tr>
											</tfoot>
										</table>
									</div>
								</div>

								<Card>
									<Title>{`${t("Energy")} Data`}</Title>
									<Flex alignItems="center" flexDirection="row">
										<PieChart
											data={energyChartValues}
											language={i18n.language === "en" ? "en" : "no-NO"}
											colors={["stone", "rose", "teal", "amber", "lime"]}
											//title={`${t("Energy")} Data`}
											//type="value"
											t={t}
										/>
									</Flex>
								</Card>

								<div className="flex flex-col gap-3">
									<div className="text-xl font-bold mt-3">{t("CO2 added")}</div>

									<div className="overflow-hidden border-t-4 border-sky-600 shadow-md ring-1 ring-slate-200 sm:rounded-lg">
										<table className="min-w-full divide-y divide-slate-300">
											<thead className="bg-slate-100">
												<tr>
													<th className="px-4 py-3 text-left text-base font-semibold text-slate-900 ">
														Type
													</th>
													<th className="px-4 py-2 text-left text-base font-semibold text-slate-900">
														{t("Emissions")}
														<span className="text-slate-500"> tCo2e</span>
													</th>
												</tr>
											</thead>
											<tbody className="divide-y divide-slate-200 bg-white">
												{isTotalCo2Added ? (
													co2Added.map((co2, index) => {
														return (
															<tr className="table-light" key={index}>
																<td
																	className="truncate py-2.5 px-4 text-base text-slate-700 "
																	style={{ maxWidth: "100px" }}
																>
																	{t(co2.name)}
																</td>
																<td className="w-24 whitespace-nowrap px-4 py-2.5 text-right text-base text-slate-700">
																	{Intl.NumberFormat(i18n.language === "en" ? "en" : "no-NO", {
																		maximumFractionDigits: 2,
																	}).format(co2.value)}
																</td>
															</tr>
														)
													})
												) : (
													<tr>
														<td className="custom-td" colSpan={2}>
															<Spinner />
														</td>
													</tr>
												)}
											</tbody>
											<tfoot>
												<tr className="border-t-2 border-slate-300">
													<td className="px-4 text-left text-base font-semibold text-slate-900">
														{t("Total Co2 added")}
													</td>
													<td className="py-3 pr-4 text-right text-base font-semibold text-slate-900">
														{Intl.NumberFormat(i18n.language === "en" ? "en" : "no-NO", {
															maximumFractionDigits: 2,
														}).format(totalCo2Added)}
													</td>
												</tr>
											</tfoot>
										</table>
									</div>
								</div>
							</div>
						</div>

						{/* emission by months */}

						<div className="col-span-1 lg:col-span-2 xl:col-span-3">
							<BarChartComponent
								language={i18n.language === "en" ? "en" : "no-NO"}
								title={"Monthly chart data"}
								Data={monthlyChartData?.data?.EmissionData}
							/>
						</div>

						<Callout
							className="overflow-y-hidden ring-1 ring-sky-400"
							title={t("Manglende data")}
							icon={FaExclamation}
						>
							{t("missing data")}
						</Callout>

						<Callout
							className="overflow-y-hidden ring-1 ring-sky-400"
							title={t("Røde flagg")}
							icon={FaExclamation}
						>
							{`${t("p1")} ${t("p1.1")}`}
						</Callout>

						<Callout
							className="overflow-y-hidden ring-1 ring-sky-400"
							title={t("travel_heading")}
							icon={FaExclamation}
						>
							{t("travel2")}
							<span
								className="text-blue underline bold cursor-pointer"
								role="button"
								tabIndex={0}
								onClick={addEmployeeCommutingTransaction}
							>
								{t("commuting")}
							</span>
						</Callout>

						{loader}
					</div>
				</div>
			)}
		</div>
	)
}

export default Dashboard
