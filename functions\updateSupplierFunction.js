exports = async (input) => {
	try {
		let transactions = []
		let EMISSION = 0
		// if supplier factor changed than re calculate emission of all transactions which are from that supplier id
		if (input.isSupplierFactorChanged || input.isSupplierNameChanged) {
			const { updatedTransactions, TOTAL_EMISSION } = await context.functions.execute("recalculateScope", {
				RegistrationNumber: input.companyRegistrationNumber,
				supplierId: input.SupplierID,
				factor: input.SupplierCo2Intensity[input.year],
				NaceCode: input.NaceCode,
				year: input.year,
				isSupplierFactorChanged: input.isSupplierFactorChanged,
				isSupplierNameChanged: input.isSupplierNameChanged,
			})
			transactions = updatedTransactions

			EMISSION = TOTAL_EMISSION
			//console.log(JSON.stringify(transactions))
		}

		const request = context.services.get("mongodb-atlas").db(context.environment.values.database).collection("company")

		// Update item in suppliers array with new data which is sent in "input" object.

		// console.log(JSON.stringify(input));

		const result = await request.updateOne(
			{
				$and: [{ RegistrationNumber: input.companyRegistrationNumber }, { "Suppliers.SupplierID": input.SupplierID }],
			},
			{
				$set: {
					"Suppliers.$.Contact.ContactPerson.FirstName": input.Contact.ContactPerson.FirstName || "",
					"Suppliers.$.Contact.ContactPerson.LastName": input.Contact.ContactPerson.LastName || "",
					"Suppliers.$.Contact.Telephone": input.Contact.Telephone || "",
					"Suppliers.$.RegistrationNumber": input.RegistrationNumber || "",
					"Suppliers.$.Contact.Email": input.Contact.Email || "",
					"Suppliers.$.NaceCode": input.NaceCode || "",
					["Suppliers.$.IndustryCo2Intensity"]: input.IndustryCo2Intensity || 0,
					["Suppliers.$.SupplierCo2Intensity"]: input.SupplierCo2Intensity || 0,
					"Suppliers.$.Name": input.Name || "",
					"Suppliers.$.Notes": input.Notes || "",
					"Suppliers.$.Industry": input.Industry || "",
					"Suppliers.$.Status": input.Status || 1,
					"Suppliers.$.isSupplierFactorUpdated": input.isSupplierFactorUpdated,
				},
			}
		)
		return {
			status: "done",
			SupplierID: input.SupplierID,
			transactions: transactions,
			Emission: EMISSION,
		}
	} catch (error) {
		console.log(error)
		return { status: "error" }
	}
	return {}
}
