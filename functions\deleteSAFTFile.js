exports = async (args) => {
	const { RegistrationNumber, index } = args;

	try {
		const db = context.services.get("mongodb-atlas").db(context.environment.values.database);
		const companyCollection = db.collection("company");

		const company = await companyCollection.findOne({ RegistrationNumber });
		const { SAFT_files, ImportID } = company;

		const importID = Object.keys(ImportID[index])[0];
		const updateSAFTFiles = SAFT_files.filter((_, i) => i !== index);
		const updateImportID = ImportID.filter((_, i) => i !== index);

		if (importID) {
			await Promise.all([
				db.collection("transaction").deleteMany({ ImportID: importID }),
				companyCollection.updateOne(
					{ RegistrationNumber },
					{ $set: { SAFT_files: updateSAFTFiles, ImportID: updateImportID } }
				)
			]);
			return { success: true };
		}

		return { success: false };
	} catch (error) {
		console.log(error);
		return { success: false, message: error.message };
	}
};
