exports = async (args) => {
  const { RegistrationNumber, year } = args;

  try {
    const company = await context.services
      .get("mongodb-atlas")
      .db(context.environment.values.database)
      .collection("company")
      .findOne(
        { RegistrationNumber: RegistrationNumber },
        { reports: 1, Logo: 1, Name: 1 },
      );

    // as report is array of objects we need to filter it by year

    if (!company?.reports) {
      return { success: false, canImportReport: false };
    }

    let { reports, Name, Logo } = company

    let report = {}

    let yearsToImportReport = []

    let reportFound = false

    reports.forEach(element => {
      if(Number(element.year) < Number(year)){
        yearsToImportReport.push(element.year)
      }
      if(element.year === year){
        report = element
        reportFound = true 
      }
    });

    return {
			success: true,
			name: Name,
			Logo: Logo,
			yearsToImportReport,
            canImportReport: yearsToImportReport.length > 0 ,
    		report,
            reportFound
		}   

  } catch (error) {
    console.log(error);
    return { success: false };
  }
};
