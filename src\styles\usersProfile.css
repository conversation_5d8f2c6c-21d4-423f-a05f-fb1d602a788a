.cl-navbar-link-custom {
	display: inline-block;
	border-radius: 0.375rem;
	padding: 0.625rem 1.25rem;
	color: var(--clerk-font-color-l1, #808080);
	font-weight: 500;
	font-size: 0.875rem;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	color: var(--clerk-font-color-l1, #808080) !important;
}

.cl-navbar-link-custom:hover {
	display: inline-block;
	border-radius: 0.375rem;
	padding: 0.625rem 1.25rem;
	color: black;
	font-weight: 500;
	font-size: 0.875rem;
	text-overflow: ellipsis;
	white-space: nowrap;
	text-decoration: none;
	overflow: hidden;
	background-color: #f5f5f5;
	color: var(--clerk-font-color-l1, #808080) !important;
}

.cl-icon-custom {
	position: relative;
	margin-right: 0.5rem;
	bottom: 2px;
	font-size: 18px;
	display: inline-block;
}

.cl-internal-phfxlr {
	width: 100% !important;
}

.cl-internal-rx251b {
	width: auto;
}

.cl-internal-1c61r6f {
	margin-left: auto;
	margin-right: auto;
	margin-top: 1rem;
}
